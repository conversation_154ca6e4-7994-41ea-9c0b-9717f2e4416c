/**
 * 图标处理工具函数
 * 参考Python版本的实现逻辑，使用Tauri命令处理文件系统访问
 */

import { invoke } from '@tauri-apps/api/core'

/**
 * 检查图标文件是否存在（通过Tauri命令）
 * @param iconPath 图标路径
 * @returns Promise<boolean>
 */
export async function checkIconExists(iconPath: string): Promise<boolean> {
  try {
    return await invoke<boolean>('check_icon_exists', { iconPath })
  } catch (error) {
    console.warn(`检查图标文件失败: ${iconPath}`, error)
    return false
  }
}

/**
 * 获取图标的base64数据URL（通过Tauri命令）
 * @param iconPath 图标路径
 * @returns Promise<string | null>
 */
export async function getIconBase64(iconPath: string): Promise<string | null> {
  try {
    return await invoke<string>('get_icon_base64', { iconPath })
  } catch (error) {
    console.warn(`获取图标base64失败: ${iconPath}`, error)
    return null
  }
}

/**
 * 获取英雄名称的首字符作为占位符
 * @param heroName 英雄名称
 * @returns 首字符
 */
export function getHeroPlaceholder(heroName: string): string {
  if (!heroName) return '?'
  return heroName.charAt(0)
}

/**
 * 尝试多种图片格式
 * @param basePath 基础路径（不含扩展名）
 * @returns Promise<string | null> 找到的图片路径或null
 */
export async function tryMultipleFormats(basePath: string): Promise<string | null> {
  const formats = ['png', 'jpg', 'jpeg', 'webp', 'svg']
  
  for (const format of formats) {
    const fullPath = `${basePath}.${format}`
    try {
      const result = await getIconBase64(fullPath)
      if (result) {
        return result
      }
    } catch (error) {
      // 继续尝试下一个格式
      continue
    }
  }
  
  return null
}

// 用于跟踪是否已经显示过成功加载的日志
let hasLoggedSuccess = false

// 全局图标缓存
const globalIconCache = new Map<string, string | null>()

/**
 * 智能获取图标数据，包含格式尝试和存在性检查
 * @param relativePath 相对路径
 * @returns Promise<string | null> base64数据URL或null
 */
export async function getSmartIconPath(relativePath: string | null | undefined): Promise<string | null> {
  if (!relativePath) return null
  
  // 检查全局缓存
  if (globalIconCache.has(relativePath)) {
    return globalIconCache.get(relativePath) || null
  }
  
  let result: string | null = null
  
  // 直接尝试获取base64，避免额外的存在性检查
  try {
    result = await getIconBase64(relativePath)
    if (result && !hasLoggedSuccess) {
      console.log(`✅ 图标加载功能正常，找到图标: ${relativePath}`)
      hasLoggedSuccess = true
    }
  } catch (error) {
    // 如果原始路径失败，尝试去掉扩展名后尝试多种格式
    const pathWithoutExt = relativePath.replace(/\.[^/.]+$/, '')
    result = await tryMultipleFormats(pathWithoutExt)
    
    if (result && !hasLoggedSuccess) {
      console.log(`✅ 图标加载功能正常，通过格式尝试找到: ${pathWithoutExt}`)
      hasLoggedSuccess = true
    }
  }
  
  // 缓存结果（包括null值）
  globalIconCache.set(relativePath, result)
  
  return result
}

/**
 * 清理图标缓存
 * @param maxSize 最大缓存大小，超过时清理最旧的条目
 */
export function cleanIconCache(maxSize: number = 200) {
  if (globalIconCache.size > maxSize) {
    const entries = Array.from(globalIconCache.entries())
    const toDelete = entries.slice(0, entries.length - maxSize)
    toDelete.forEach(([key]) => globalIconCache.delete(key))
    console.log(`🧹 清理了 ${toDelete.length} 个图标缓存条目`)
  }
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats() {
  return {
    size: globalIconCache.size,
    keys: Array.from(globalIconCache.keys())
  }
}