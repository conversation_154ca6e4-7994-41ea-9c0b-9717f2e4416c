# -*- coding: utf-8 -*-
"""
业务逻辑管理器模块

本文件包含用于管理特定业务逻辑的"经理"类。
每个管理器类封装一个独立功能模块的所有逻辑，包括状态管理、后台循环、UI交互等。
"""
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import re
import imagehash
import sqlite3
import logging # 引入日志模块
from PIL import Image, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed
import pytesseract

import config
import utils
import ocr_handler
import data_query

class HexManager:
    """
    海克斯识别功能的总管理器。
    封装了与海克斯识别相关的所有状态、UI元素和业务逻辑。
    """
    def __init__(self, app_ref):
        """
        初始化HexManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
                                       用于访问主窗口、屏幕尺寸等共享资源。
        """
        self.app = app_ref  # 对主应用的引用
        self.hex_query = data_query.EnhancedHextechQuery()
        self.hex_whitelist = self._load_hex_whitelist()

        # --- 状态变量 ---
        self.hex_ocr_running = False
        self.refresh_detection_active = False
        self.hex_start_time = 0
        self.hex_no_detect_time = 0
        self.current_hex_plan = 1  # 1-方案一，2-方案二

        # --- [新增] 缓存的游戏窗口坐标和监测优化 ---
        self.cached_game_window_rect = None
        self.last_window_check_time = 0
        self.window_check_interval = 10.0  # 每10秒重新检测一次窗口坐标

        # --- 新的状态管理机制 ---
        self.hex_states = ['EMPTY', 'EMPTY', 'EMPTY']       # 每个区域的状态: EMPTY, PROCESSING, STABLE, AWAITING_MANUAL_INPUT
        self.stable_hashes = [None, None, None]     # 每个区域稳定时的图像哈希
        self.stable_results = [None, None, None]    # 每个区域稳定时的识别结果

        # --- 图像与哈希相关 ---
        self.hash_threshold = 8.0 # 提高阈值以忽略游戏内微小动画，防止闪烁
        self.confidence_threshold = 70 # OCR识别成功的分数阈值, 从80下调
        self.last_hex_image_hashes_lock = threading.Lock()

        # --- UI元素 ---
        self.hex_overlay_windows = []
        self.hex_rank_labels = []
        self.hex_variant_widgets = [None, None, None] # 用于显示海克斯变体的窗口
        self.hex_query_widgets = [None, None, None] 
        self.hex_debug_boxes = [] # [新增] 用于显示OCR区域的调试红框
        self.hex_extra_info_window_height = int(self.app.hex_extra_info_font_size * 2.2) # 调整高度计算
        self._create_hex_widgets() # 初始化时创建UI组件
        self._create_debug_boxes() # [新增] 初始化时创建调试红框

        # --- 结果跟踪 ---
        self.last_hex_names = [None, None, None]
        self.last_hex_ranks = [None, None, None]
        
    def _load_hex_whitelist(self):
        """从数据库加载海克斯白名单。"""
        try:
            conn = sqlite3.connect(config.HEX_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM hexes")
            whitelist = [row[0] for row in cursor.fetchall()]
            conn.close()
            return whitelist
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"无法从数据库加载海克斯名称列表：{e}")
            return []

    def _create_hex_widgets(self):
        """创建海克斯功能所需的所有顶层窗口和标签。"""
        screen_width = self.app.screen_width
        screen_height = self.app.screen_height

        # 方案一的覆盖区域
        HEX_OVERLAY_REGIONS_RELATIVE_PLAN1 = [
            (664/2560, 1020/1440, 820/2560, 1080/1440),
            (1206/2560, 1020/1440, 1362/2560, 1080/1440),
            (1748/2560, 1020/1440, 1904/2560, 1080/1440)
        ]
        # 方案二的覆盖区域
        HEX_OVERLAY_REGIONS_RELATIVE_PLAN2 = [
            (588/2560, 965/1440, 744/2560, 1015/1440),
            (1270/2560, 965/1440, 1426/2560, 1015/1440),
            (1815/2560, 965/1440, 1971/2560, 1015/1440)
        ]
        
        self.HEX_OVERLAY_PLANS = {
            1: HEX_OVERLAY_REGIONS_RELATIVE_PLAN1,
            2: HEX_OVERLAY_REGIONS_RELATIVE_PLAN2
        }
        
        # 先创建好所有UI组件，初始状态为隐藏
        for i in range(3):
            # 主评级窗口
            overlay_window = tk.Toplevel(self.app)
            overlay_window.overrideredirect(True)
            overlay_window.attributes('-alpha', 0.8)
            overlay_window.attributes('-topmost', True)
            rank_label = ttk.Label(overlay_window, text="", style="HexRank.TLabel", anchor="center")
            rank_label.pack(expand=True, fill=tk.BOTH)
            self.hex_overlay_windows.append(overlay_window)
            self.hex_rank_labels.append(rank_label)
            overlay_window.withdraw()
            
        self.update_hex_windows_positions() # 根据默认方案设置初始位置

    def _create_debug_boxes(self):
        """[新增] 创建用于调试的红色OCR区域指示框。"""
        # 使用一种不太可能出现的颜色作为透明色
        TRANSPARENT_COLOR = '#abcdef'

        for i in range(3):
            debug_box = tk.Toplevel(self.app)
            debug_box.overrideredirect(True)
            debug_box.attributes('-topmost', True)
            # 设置透明色
            debug_box.attributes('-transparentcolor', TRANSPARENT_COLOR)
            # 设置窗口背景为透明色
            debug_box.config(bg=TRANSPARENT_COLOR)
            # 禁用窗口，让鼠标事件穿透
            debug_box.attributes('-disabled', 1)

            # 创建一个带红色边框的Frame来实现红框效果
            frame = tk.Frame(debug_box, 
                             highlightbackground="red", 
                             highlightcolor="red", 
                             highlightthickness=2, 
                             bg=TRANSPARENT_COLOR)
            frame.pack(expand=True, fill=tk.BOTH)

            self.hex_debug_boxes.append(debug_box)
            debug_box.withdraw() # 初始状态为隐藏

    def start_hex_ocr(self, event=None, show_popup=True):
        """手动或自动启动OCR，并可选择是否显示提示。"""
        # 如果已经在运行，先停止
        if self.is_running():
            self.stop_hex_ocr()
            time.sleep(0.1) # 等待确保旧循环完全停止

        logging.info("启动海克斯OCR功能。") # 日志记录
        # 根据标志决定是否显示启动确认信息
        if show_popup:
            messagebox.showinfo("海克斯OCR", "海克斯评分已开启", parent=self.app)

        # [新增] 启动时立即获取和缓存游戏窗口坐标
        self._get_game_window_rect_cached(force_update=True)

        # 启动OCR循环
        self.hex_ocr_running = True
        self.hex_start_time = time.time()
        self.hex_no_detect_time = time.time()
        
        # 启动时重置为方案一
        self.current_hex_plan = 1
        config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1
        self.update_hex_windows_positions()

        # [新增] 如果在调试模式下，显示OCR区域红框
        if self.app.debug_mode:
            self._show_debug_boxes()

        threading.Thread(target=self.run_hex_ocr_loop, daemon=True).start()

    def stop_hex_ocr(self):
        """停止海克斯OCR循环并清理资源。"""
        self.hex_ocr_running = False
        self.refresh_detection_active = False
        
        logging.info("停止海克斯OCR功能。") # 日志记录

        # [新增] 停止时，总是隐藏调试红框
        self._hide_debug_boxes()

        # 在主线程中安全地隐藏所有窗口
        self.app.after(0, self.hide_all_windows)
        
        # 重置状态变量
        self.hex_states = ['EMPTY', 'EMPTY', 'EMPTY']
        self.stable_hashes = [None, None, None]
        self.stable_results = [None, None, None]
        self.last_hex_names = [None, None, None]
        self.last_hex_ranks = [None, None, None]
        
        # 确保所有变体窗口也被关闭
        self.app.after(0, self._hide_all_variant_windows)

        if self.app.debug_mode:
            print("海克斯OCR已停止并清理资源。")
            logging.debug("海克斯OCR已停止并清理资源。")

    def run_hex_ocr_loop(self):
        """
        海克斯OCR的主循环。
        采用"并行决策引擎"模式，对任何变化的区域直接启动多策略并发识别。
        """
        self.refresh_detection_active = True
        
        while self.hex_ocr_running:
            try:
                if time.time() - self.hex_start_time > 50:
                    self.stop_hex_ocr()
                    break

                # [新增] 实时获取游戏窗口坐标，实现动态适配
                game_window_rect = self._get_game_window_rect_cached()

                # 1. 截图、哈希计算与状态变化检测
                images_to_process = {}  # {index: image}
                current_hashes = [None] * 3
        
                for i, rel_region in enumerate(config.SCREENSHOT_REGIONS_RELATIVE):
                    # [修改] 使用新的坐标转换函数替代原来的计算方式
                    x1, y1, x2, y2 = utils.convert_relative_to_absolute(rel_region, game_window_rect)
                    
                    img = ocr_handler.capture_screen((x1, y1, x2, y2))
                    
                    if not img:
                        current_hashes[i] = None
                        # 如果之前是稳定状态，现在截图失败，则视为空
                        if self.hex_states[i] == 'STABLE':
                            self.hex_states[i] = 'EMPTY'
                        continue
                    
                    current_hash = utils.calculate_image_hash(img)
                    current_hashes[i] = current_hash
                    
                    # 状态变化检测：哈希值与稳定哈希对比
                    hash_diff = utils.hash_distance(current_hash, self.stable_hashes[i])
                    if hash_diff > self.hash_threshold:
                        logging.info(f"[Hex] 位置 {i} 检测到变化 (哈希差异: {hash_diff:.2f})，区域: {rel_region}")
                        
                        # 触发闪烁效果，进入处理中状态
                        self.app.after(0, self._flash_effect_start, i)
                        self.hex_states[i] = 'PROCESSING'
                        self.stable_hashes[i] = None # 清除旧的稳定哈希
                        self.stable_results[i] = None
                        images_to_process[i] = img
                
                # 2. 并行OCR决策引擎
                if images_to_process:
                    ocr_start_time = time.time()
                    
                    with ThreadPoolExecutor(max_workers=len(images_to_process)) as executor:
                        future_to_index = {
                            executor.submit(
                                ocr_handler.enhanced_ocr_recognize, 
                                img, self.hex_query, self.confidence_threshold
                            ): i 
                            for i, img in images_to_process.items()
                        }

                        for future in as_completed(future_to_index):
                            i = future_to_index[future]
                            try:
                                # [新增] 增加日志，无论如何都要看清线程返回了什么
                                logging.info(f"[Hex] 位置 {i} OCR线程准备获取结果。")
                                result = future.result()
                                logging.info(f"[Hex] 位置 {i} OCR线程返回结果: {result}")

                                if not result:
                                    self.hex_states[i] = 'EMPTY'
                                    continue

                                status = result.get('status')
                                data = result.get('data')

                                if status == 'SUCCESS':
                                    self.hex_states[i] = 'STABLE'
                                    self.stable_hashes[i] = current_hashes[i] # 存入新的稳定哈希
                                    self.stable_results[i] = {
                                        'name': data['match'], 
                                        'candidates': data['candidates'], 
                                        'result_obj': self.hex_query.smart_query(data['match'])
                                    }
                                    # 使用.get()方法安全地访问可能不存在的键，避免KeyError
                                    original_text = data.get('text', 'N/A')
                                    confidence = data.get('confidence', 0.0)
                                    logging.info(f"[Hex] 位置 {i} 识别成功: {data['match']} (原始文本: '{original_text}', 置信度: {confidence:.2f})")
                                elif status == 'LOW_CONFIDENCE':
                                    self.hex_states[i] = 'AWAITING_MANUAL_INPUT'
                                elif status == 'NO_TEXT':
                                    self.hex_states[i] = 'EMPTY'
                                else:
                                    self.hex_states[i] = 'EMPTY' # 安全保护
                            
                            except Exception as e:
                                # [增强] 捕获并记录所有可能的异常
                                print(f"[HexManager] [位置 {i}] 处理OCR决策引擎结果时出错: {e}")
                                logging.error(f"[Hex] 位置 {i} 处理OCR决策引擎结果时出现致命错误: {e}", exc_info=True)
                                self.hex_states[i] = 'EMPTY'

                    if self.app.debug_mode:
                        print(f"[HexManager] 并行处理 {len(images_to_process)} 个区域，总耗时: {time.time() - ocr_start_time:.2f}s")

                # 3. UI渲染
                self.app.after(0, self._update_ui_controller)
                self._check_window_consistency() # 增加周期性一致性检查，确保窗口同步
                time.sleep(0.1)

            except Exception as e:
                if self.app.debug_mode:
                    print(f"Hex OCR 循环出错: {e}")
                logging.error(f"Hex OCR 主循环异常: {e}", exc_info=True)
                time.sleep(1)

        self.refresh_detection_active = False

    def _show_stable_result(self, i):
        """显示稳定状态的结果，将主窗口和变体窗口绑定显示。"""
        # 1. 确保其他类型的窗口（如手动搜索框）是隐藏的
        self.hide_hex_query_windows(i)

        # 2. 更新并显示主评级窗口
        result = self.stable_results[i]['result_obj']
        self.update_hex_rank_label(i, result)
        self.hex_overlay_windows[i].deiconify()

        # 3. 基于主窗口的显示，决定是否显示变体窗口
        self._handle_variant_display(i, self.stable_results[i]['name'], self.stable_results[i]['candidates'])

    def _render_ui_for_index(self, i):
        """根据指定索引的当前状态，渲染所有相关的UI组件。"""
        state = self.hex_states[i]

        if state == 'STABLE':
            # 调用新的绑定显示函数
            self._show_stable_result(i)

        elif state == 'PROCESSING':
            # 在处理中时，隐藏所有窗口，仅显示一个"?"占位符
            self.hide_all_windows_for_index(i) 
            self.hex_rank_labels[i].config(text="?", style="HexRank.TLabel")
            self.hex_overlay_windows[i].deiconify()

        elif state == 'AWAITING_MANUAL_INPUT':
            # 等待输入时，隐藏其他所有窗口，只显示查询窗口
            self.hide_all_windows_for_index(i) 
            if self.hex_query_widgets[i] is None:
                self.create_hex_query_window(i)
            else:
                if self.hex_query_widgets[i] and self.hex_query_widgets[i]['window']:
                    self.hex_query_widgets[i]['window'].deiconify()

        elif state == 'EMPTY':
            # 空状态时，隐藏所有窗口
            self.hide_all_windows_for_index(i)
    
    def _update_ui_controller(self):
        """全局UI渲染控制器，根据当前状态决定显示内容。"""
        # 门卫逻辑：少于2个稳定状态，则隐藏所有窗口
        stable_count = self.hex_states.count('STABLE')
        if stable_count < 2:
            # 当稳定数不足时，为所有位置隐藏所有窗口
            for i in range(3):
                self.hide_all_windows_for_index(i)
            return
            
        for i in range(3):
            # 为每个索引调用独立的渲染函数
            self._render_ui_for_index(i)

    def _check_window_consistency(self):
        """[新增] 仿照参考代码，检查并确保主评级窗口和变体窗口的显示状态同步。"""
        try:
            for i in range(3):
                main_window = self.hex_overlay_windows[i]
                if not main_window.winfo_exists():
                    continue

                # 如果主窗口不可见，但其对应的变体窗口可见，则隐藏变体窗口
                variant_widget = self.hex_variant_widgets[i]
                if not main_window.winfo_ismapped() and variant_widget and variant_widget['window'].winfo_exists() and variant_widget['window'].winfo_ismapped():
                    if self.app.debug_mode:
                        print(f"[一致性检查] 主窗口 {i} 已隐藏，同步隐藏悬空的变体窗口。")
                    self._hide_variant_window(i)

        except Exception as e:
            if self.app.debug_mode:
                print(f"窗口一致性检查出错: {e}")

    def _flash_effect_start(self, index):
        """为指定索引的窗口启动闪烁效果（第一步：隐藏）"""
        # 这是一个简单的实现，通过立即隐藏旧内容来给用户反馈
        self.hide_all_windows_for_index(index)

    def update_hex_rank_label(self, label_index, result):
        """更新单个海克斯评级标签的样式和文本。"""
        label = self.hex_rank_labels[label_index]
        
        if result['status'] == 'exact':
            current_rank = result['data']['rank']
            target_style = f"HexRank{current_rank}.TLabel" if current_rank in ['S', 'A', 'B', 'C', 'D'] else "HexRank.TLabel"
            label.config(text=current_rank, style=target_style)
        else:
            label.config(text="?", style="HexRank.TLabel")

    def _handle_variant_display(self, index, hex_name, candidates):
        """检查并决定是否显示海克斯变体列表窗口。"""
        if not hex_name:
            self._hide_variant_window(index)
            return

        variants = None
        # 检查是否有多个变体
        if any(re.search(r'[ⅠⅡⅢⅣⅤI]+$', c[0]) for c in candidates):
            base_name = self.hex_query._preprocess_name(hex_name)
            variants = self.hex_query.get_variants_by_base_name(base_name)
        
        # 检查特殊情况
        elif "之徽" in hex_name or "之冕" in hex_name or "之环" in hex_name:
            variants = self.hex_query.handle_special_variants(hex_name)

        if variants:
            # 如果找到变体，则创建或更新变体窗口
            self._create_variant_window(index, variants)
        else:
            # 如果没有变体，确保旧的变体窗口是关闭的
            self._hide_variant_window(index)

    def _create_variant_window(self, index, variants):
        """为指定位置创建一个显示海克斯变体及其评级的窗口。"""
        # [优化] 检查现有窗口和内容是否匹配，防止不必要的重绘和抖动
        existing_widget = self.hex_variant_widgets[index]
        if existing_widget and existing_widget.get('variants') == variants:
            # 内容相同，确保窗口可见即可
            if not existing_widget['window'].winfo_ismapped():
                existing_widget['window'].deiconify()
            return

        # 如果已有窗口但内容不同，或没有窗口，先安全地销毁旧的
        if self.hex_variant_widgets[index] is not None:
            self._hide_variant_window(index)
            
        # 获取主评级窗口的位置和大小作为参考
        ref_win = self.hex_overlay_windows[index]
        x, y, w, h = ref_win.winfo_x(), ref_win.winfo_y(), ref_win.winfo_width(), ref_win.winfo_height()

        variant_window = tk.Toplevel(self.app)
        variant_window.overrideredirect(True)
        variant_window.attributes('-topmost', True)
        
        main_frame = ttk.Frame(variant_window, style="Card.TFrame")
        main_frame.pack(expand=True, fill="both")
        
        title_label = ttk.Label(main_frame, text="相关海克斯评级", style="QueryTitle.TLabel")
        title_label.pack(pady=(5,2))
        
        listbox = tk.Listbox(main_frame, height=len(variants), background="#333", foreground="#CCC", selectbackground="#0078D7", relief="flat", borderwidth=0, highlightthickness=0)
        listbox.pack(expand=True, fill='both', padx=5, pady=5)
        
        # 填充列表
        for name, rank in variants.items():
            display_text = f"{name}  [{rank}]"
            listbox.insert(tk.END, display_text)
            
        # 动态计算窗口高度和位置
        # 强制UI更新以获取真实的尺寸
        variant_window.update_idletasks()
        
        # 获取计算后的实际高度
        win_height = variant_window.winfo_height()
        
        # 将窗口置于主评级窗口的上方
        new_y = y - win_height
        
        # 重新设置几何位置，这次只移动，不改变大小
        variant_window.geometry(f"{w}x{win_height}+{x}+{new_y}")

        self.hex_variant_widgets[index] = {
            'window': variant_window,
            'variants': variants # 保存用于创建窗口的数据，用于后续比较
        }

        # 点击外部或窗口本身都会关闭
        variant_window.bind("<FocusOut>", lambda e, i=index: self._hide_variant_window(i))
        listbox.bind("<Button-1>", lambda e, i=index: self._hide_variant_window(i))
        
        variant_window.deiconify()
        
    def _hide_variant_window(self, index):
        """隐藏并销毁指定索引的海克斯变体窗口，并立即清除其引用以防止竞态条件。"""
        # 立即捕获要销毁的窗口小部件并清除列表中的引用，这是修复竞态条件的关键
        widget_to_destroy = self.hex_variant_widgets[index]
        self.hex_variant_widgets[index] = None

        if widget_to_destroy:
            def _destroy():
                try:
                    widget_to_destroy['window'].destroy()
                except tk.TclError:
                    pass  # 窗口可能已被销毁

            # 在UI线程中安排销毁
            self.app.after(0, _destroy)

    def _hide_all_variant_windows(self):
        """隐藏并销毁所有的海克斯变体窗口。"""
        for i in range(len(self.hex_variant_widgets)):
            self._hide_variant_window(i)

    def create_hex_query_window(self, index):
        """为指定的海克斯位置创建一个智能的手动查询窗口。"""
        # 如果该位置已有窗口，先销毁
        if self.hex_query_widgets[index] is not None:
            self.hide_hex_query_windows(index)

        # 获取主评级窗口的位置和大小作为参考
        ref_win = self.hex_overlay_windows[index]
        x, y, w, h = ref_win.winfo_x(), ref_win.winfo_y(), ref_win.winfo_width(), ref_win.winfo_height()
        
        query_window = tk.Toplevel(self.app)
        query_window.overrideredirect(True)
        query_window.attributes('-topmost', True)

        # UI布局
        main_frame = ttk.Frame(query_window, style="Card.TFrame")
        main_frame.pack(expand=True, fill="both")
        
        title_label = ttk.Label(main_frame, text="手动搜索 (输入名称/拼音)", style="QueryTitle.TLabel")
        title_label.pack(pady=(5,2))

        entry_var = tk.StringVar()
        entry = ttk.Entry(main_frame, textvariable=entry_var)
        entry.pack(fill='x', padx=5)

        listbox = tk.Listbox(main_frame, height=5, background="#333", foreground="#CCC", selectbackground="#0078D7", relief="flat", borderwidth=0, highlightthickness=0)
        listbox.pack(expand=True, fill='both', padx=5, pady=5)
        
        # 调整窗口大小和位置
        # 高度大约是标题+输入框+5行列表的高度
        win_height = int(h * 4) 
        # 将窗口置于主评级窗口的上方
        new_y = y - win_height
        query_window.geometry(f"{w}x{win_height}+{x}+{new_y}")

        # 存储所有组件
        self.hex_query_widgets[index] = {
            'window': query_window,
            'entry': entry,
            'listbox': listbox,
            'var': entry_var
        }

        # 绑定事件
        entry.bind("<KeyRelease>", lambda e, i=index: self._on_hex_query_keyup(e, i))
        listbox.bind("<<ListboxSelect>>", lambda e, i=index: self.on_hex_query_select(e, i))
        # 增加回车确认
        listbox.bind("<Return>", lambda e, i=index: self.on_hex_query_select(e, i))
        # 增加点击外部关闭窗口的功能
        query_window.bind("<FocusOut>", lambda e, i=index: self.hide_hex_query_windows(i))

        query_window.deiconify()
        entry.focus_set()

    def _on_hex_query_keyup(self, event, index):
        """处理手动查询输入框的键盘抬起事件，实时更新建议列表。"""
        widgets = self.hex_query_widgets[index]
        if not widgets:
            return

        query = widgets['var'].get()
        # 调用模糊搜索算法
        results = self.hex_query.fuzzy_search_hex(query)
        
        listbox = widgets['listbox']
        listbox.delete(0, tk.END) # 清空列表
        
        # 为每个结果获取评级并显示在建议列表中
        for name in results:
            result_obj = self.hex_query.smart_query(name)
            rank = "?"
            # 确保查询成功并获取到评级
            if result_obj and result_obj.get('status') == 'exact':
                rank = result_obj['data']['rank']
            
            # 将海克斯名称和评级一起显示
            display_text = f"{name}  [{rank}]"
            listbox.insert(tk.END, display_text)

    def on_hex_query_select(self, event, index):
        """处理手动查询下拉框的选择事件。"""
        widgets = self.hex_query_widgets[index]
        if not widgets:
            return
        
        listbox = widgets['listbox']
        # 获取选中的项目
        selected_indices = listbox.curselection()
        if not selected_indices:
            return
            
        selected_text = listbox.get(selected_indices[0])
        
        # 从 "海克斯名称  [评级]" 的格式中解析出真正的海克斯名称
        selected_name = selected_text.split("  [")[0]

        if selected_name:
            # 更新状态为STABLE
            self.hex_states[index] = 'STABLE'
            # 伪造一个stable_result，让UI控制器能够渲染它
            self.stable_results[index] = {
                'name': selected_name,
                'candidates': [],
                'result_obj': self.hex_query.smart_query(selected_name)
            }
            # 手动选择后，我们没有图像哈希，所以设为None以便下次能检测到变化
            self.stable_hashes[index] = None
            
            # 隐藏并销毁查询窗口，UI的更新会由主循环的_update_ui_controller处理
            self.hide_hex_query_windows(index)

    def hide_hex_query_windows(self, index=None):
        """隐藏并销毁指定索引或所有的海克斯查询窗口，并立即清除引用以防止竞态条件。"""
        
        # 确定要操作的索引列表
        indices_to_hide = range(len(self.hex_query_widgets)) if index is None else [index]

        for i in indices_to_hide:
            # 检查索引有效性
            if i < len(self.hex_query_widgets):
                # 立即捕获要销毁的窗口小部件并清除列表中的引用
                widget_to_destroy = self.hex_query_widgets[i]
                self.hex_query_widgets[i] = None 

                if widget_to_destroy:
                    # 使用闭包来正确捕获每次循环的widget_to_destroy值
                    def schedule_destroy(widget):
                        def _destroy_callback():
                            try:
                                widget['window'].destroy()
                            except tk.TclError:
                                pass # 窗口可能已被销毁
                        self.app.after(0, _destroy_callback)
                    
                    schedule_destroy(widget_to_destroy)

    def hide_all_windows(self):
        """隐藏所有由HexManager创建的窗口"""
        for i in range(3):
            self.hide_all_windows_for_index(i)

    def hide_all_windows_for_index(self, index):
        """隐藏指定索引的所有窗口"""
        if self.hex_overlay_windows[index]: self.hex_overlay_windows[index].withdraw()
        self.hide_hex_query_windows(index)
        self._hide_variant_window(index) # 确保相关评级窗口（变体窗口）也一并隐藏
    
    def update_hex_windows_positions(self):
        """根据当前方案更新所有海克斯窗口和调试框的位置和大小。"""
        regions = self.HEX_OVERLAY_PLANS.get(self.current_hex_plan, [])
        # [修改] 使用OCR截图区域来定位调试框
        ocr_regions = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1 if self.current_hex_plan == 1 else config.SCREENSHOT_REGIONS_RELATIVE_PLAN2

        if not regions: return

        # [新增] 获取游戏窗口坐标用于位置计算
        game_window_rect = self._get_game_window_rect_cached()
        logging.info(f"[Hex] 更新窗口位置时获取游戏窗口坐标: {game_window_rect}")

        for i in range(3):
            # [修改] 更新评级窗口 - 使用新的坐标转换
            rx1, ry1, rx2, ry2 = utils.convert_relative_to_absolute(regions[i], game_window_rect)
            w, h = rx2 - rx1, ry2 - ry1
            self.hex_overlay_windows[i].geometry(f"{w}x{h}+{rx1}+{ry1}")

            # [修改] 更新调试红框 - 使用新的坐标转换
            if self.hex_debug_boxes and len(self.hex_debug_boxes) > i:
                ocr_x1, ocr_y1, ocr_x2, ocr_y2 = utils.convert_relative_to_absolute(ocr_regions[i], game_window_rect)
                ocr_w, ocr_h = ocr_x2 - ocr_x1, ocr_y2 - ocr_y1
                self.hex_debug_boxes[i].geometry(f"{ocr_w}x{ocr_h}+{ocr_x1}+{ocr_y1}")
                if self.app.debug_mode:
                    print(f"[HexManager] 调试框 {i} 位置更新: {ocr_w}x{ocr_h}+{ocr_x1}+{ocr_y1}")

    def switch_hex_plan(self):
        """切换海克斯识别方案并更新窗口位置。"""
        self.current_hex_plan = 2 if self.current_hex_plan == 1 else 1
        
        if self.current_hex_plan == 1:
            config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN1
        else:
            config.SCREENSHOT_REGIONS_RELATIVE = config.SCREENSHOT_REGIONS_RELATIVE_PLAN2
            
        self.update_hex_windows_positions()
        plan_text = "一" if self.current_hex_plan == 1 else "二"
        messagebox.showinfo("切换成功", f"已切换到海克斯方案 {plan_text}")

    def is_running(self):
        """返回海克斯OCR是否正在运行。"""
        return self.hex_ocr_running

    def _show_debug_boxes(self):
        """[新增] 显示调试红框。"""
        for box in self.hex_debug_boxes:
            box.deiconify()

    def _hide_debug_boxes(self):
        """[新增] 隐藏调试红框。"""
        for box in self.hex_debug_boxes:
            box.withdraw()

    def destroy(self):
        """
        销毁所有由HexManager管理的资源，特别是UI窗口。
        这是程序关闭时的关键步骤。
        """
        print("正在销毁海克斯管理器资源...")
        self.hex_ocr_running = False # 确保所有循环都会停止
        
        # 将所有窗口的销毁操作都放到主UI线程中执行
        def _destroy_windows():
            all_windows = (self.hex_overlay_windows)
            
            # 先单独处理并销毁查询和变体窗口
            self.hide_all_windows() # 使用hide_all_windows来确保所有窗口被正确处理

            # [新增] 销毁调试红框
            for box in self.hex_debug_boxes:
                try:
                    box.destroy()
                except tk.TclError:
                    pass
            
            for window in all_windows:
                try:
                    window.destroy()
                except tk.TclError as e:
                    print(f"销毁窗口时发生错误 (可能已被销毁): {e}")

            # 清空列表，防止内存泄漏
            self.hex_overlay_windows.clear()
            self.hex_rank_labels.clear()
            self.hex_variant_widgets.clear()
            self.hex_query_widgets.clear()
            self.hex_debug_boxes.clear() # [新增] 清空列表

        if self.app and self.app.winfo_exists():
            self.app.after(0, _destroy_windows)

    def _get_game_window_rect_cached(self, force_update=False):
        """
        [新增] 获取缓存的游戏窗口坐标，减少频繁的API调用。
        
        Args:
            force_update (bool): 是否强制更新缓存
            
        Returns:
            tuple: (x, y, width, height) 游戏窗口坐标
        """
        current_time = time.time()
        
        # 判断是否需要更新缓存
        if (force_update or 
            self.cached_game_window_rect is None or 
            current_time - self.last_window_check_time > self.window_check_interval):
            
            # 获取新的窗口坐标
            self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
            self.last_window_check_time = current_time
            
            if self.app.debug_mode:
                print(f"[HexManager] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
            logging.info(f"[Hex] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
        
        return self.cached_game_window_rect

# ==============================================================================
# EquipManager - 装备识别功能的总管理器
# ==============================================================================

class EquipManager:
    """
    装备识别功能的总管理器。
    封装了与装备识别相关的所有状态、UI元素和业务逻辑。
    """
    def __init__(self, app_ref):
        """
        初始化EquipManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
        """
        self.app = app_ref
        self.equip_query = data_query.EquipmentQuery()
        self.equip_whitelist = self._load_equip_whitelist()

        # --- 状态变量 ---
        self.equip_ocr_running = False
        self.equip_start_time = 0
        self.equip_no_detect_time = 0

        # --- [新增] 缓存的游戏窗口坐标和监测优化 ---
        self.cached_game_window_rect = None
        self.last_window_check_time = 0
        self.window_check_interval = 15.0  # 装备检测频率较低，可以设置更长的间隔

        # --- [新增] 状态跟踪，用于即时响应变化 ---
        self.last_stable_equip_count = 0 

        # --- 图像哈希相关 ---
        self.equip_last_image_hash = None
        self.hash_threshold = 7

        # --- UI元素 ---
        self.equip_overlay_windows = []

    def _load_equip_whitelist(self):
        """从数据库加载装备白名单。"""
        try:
            conn = sqlite3.connect(config.EQUIP_DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM items")
            whitelist = [row[0] for row in cursor.fetchall()]
            conn.close()
            return whitelist
        except sqlite3.Error as e:
            messagebox.showerror("数据库错误", f"无法从数据库加载装备名称列表：{e}")
            return []

    def start_equip_ocr(self, event=None):
        """启动或停止装备OCR。"""
        if self.equip_ocr_running:
            self.stop_equip_ocr()
            messagebox.showinfo("装备OCR", "装备评分已关闭")
        else:
            if self.app.debug_mode:
                print("启动装备OCR...")
            logging.info("启动装备OCR功能。") # 日志记录
            
            # [新增] 启动时立即获取和缓存游戏窗口坐标
            self._get_game_window_rect_cached(force_update=True)
            
            self.equip_ocr_running = True
            self.equip_start_time = time.time()
            self.equip_no_detect_time = time.time()
            threading.Thread(target=self.run_equip_ocr_loop, daemon=True).start()
            messagebox.showinfo("装备OCR", "装备评分已开启")

    def stop_equip_ocr(self):
        """停止装备OCR循环"""
        self.equip_ocr_running = False
        logging.info("停止装备OCR功能。") # 日志记录
        self.clear_equip_results()
        self.equip_last_image_hash = None # 清除哈希，以便下次能立即启动
        if self.app.debug_mode:
            print("装备OCR已停止，并清理窗口和状态")
            
    def is_running(self):
        """检查装备OCR循环是否正在运行"""
        return self.equip_ocr_running

    def run_equip_ocr_loop(self):
        """[重构] 装备OCR识别的主循环，采用新的两步走策略。"""
        if self.app.debug_mode:
            print("启动装备OCR...")
        
        while self.equip_ocr_running:
            try:
                # 安全退出机制
                if time.time() - self.equip_start_time > 35:
                    self.stop_equip_ocr()
                    break

                # [新增] 实时获取游戏窗口坐标，实现动态适配
                game_window_rect = self._get_game_window_rect_cached()
                # [优化] 移除频繁的日志打印，只在缓存更新时才记录
                # logging.info(f"[Equip] 获取游戏窗口坐标: {game_window_rect}")
                # if self.app.debug_mode:
                #     print(f"[EquipManager] 游戏窗口坐标: {game_window_rect}")

                # 步骤一："视觉计数器" - 通过轮廓分析确定装备数量
                # [修改] 使用新的坐标转换函数
                contour_x1, contour_y1, contour_x2, contour_y2 = utils.convert_relative_to_absolute(
                    config.EQUIP_CONTOUR_REGION_RELATIVE, game_window_rect
                )
                # [优化] 移除频繁的坐标打印，只在调试模式且首次运行时打印
                # if self.app.debug_mode:
                #     print(f"[EquipManager] 轮廓检测区域绝对坐标: ({contour_x1}, {contour_y1}, {contour_x2}, {contour_y2})")
                
                contour_img = ocr_handler.capture_screen((contour_x1, contour_y1, contour_x2, contour_y2))
                
                equip_count = ocr_handler.find_equipment_contours(contour_img)

                # [新增] 用户提出的关键修正：将6个或更多的错误计数强制修正为5
                if equip_count >= 6:
                    if self.app.debug_mode:
                        print(f"[EquipManager] 原始计数为 {equip_count}，强制修正为 5 进行尝试。")
                    logging.info(f"[Equip] 轮廓计数过多 (原始: {equip_count})，强制修正为 5 以进行识别。")
                    equip_count = 5

                if self.app.debug_mode:
                    print(f"[EquipManager] 视觉计数器检测到 {equip_count} 个装备。")
                
                # 将日志级别从DEBUG提升到INFO，确保被记录
                logging.info(f"[Equip] 视觉计数器检测到 {equip_count} 个装备轮廓。")

                if equip_count > 0:
                    # 步骤二："精准识别器" - 根据数量选择对应的OCR区域
                    ocr_regions = config.EQUIP_OCR_REGIONS_BY_COUNT.get(equip_count)
                    if not ocr_regions:
                        if self.app.debug_mode:
                            print(f"[EquipManager] 警告: 无法为 {equip_count} 个装备找到预设的OCR区域。")
                        time.sleep(0.2)
                        continue

                    # 并行截图与预处理
                    images_to_process = []
                    with ThreadPoolExecutor(max_workers=equip_count) as executor:
                        future_to_img = {
                            executor.submit(
                                ocr_handler.capture_screen, 
                                utils.convert_relative_to_absolute(rel_region, game_window_rect)
                            ): i 
                            for i, rel_region in enumerate(ocr_regions)
                        }
                        # 按顺序收集截图结果
                        captured_images = [None] * equip_count
                        for future in as_completed(future_to_img):
                            index = future_to_img[future]
                            try:
                                captured_images[index] = future.result()
                            except Exception as e:
                                print(f"截图时出错: {e}")

                    for img in captured_images:
                        if img:
                            images_to_process.append(ocr_handler.equip_preprocess_image(img))

                    # 步骤A: 并行进行精准OCR
                    raw_text_parts, _ = ocr_handler.ocr_recognize(images_to_process, psm=7)
                    if self.app.debug_mode:
                        print(f"[EquipManager] 原始OCR识别结果: {raw_text_parts}")
                    logging.info(f"[Equip] OCR原始识别文本: {raw_text_parts}")

                    # 步骤B: 对OCR结果进行模糊匹配，获取有效装备名称
                    matched_names = self.equip_query.match_equipment(raw_text_parts, self.equip_whitelist)
                    if self.app.debug_mode:
                        print(f"[EquipManager] 匹配后的有效装备: {matched_names}")
                    logging.info(f"[Equip] 匹配后的有效装备: {matched_names}")

                    # 步骤C: 验证与渲染
                    # [重构] 验证逻辑：计算有效匹配的数量
                    valid_matches = [name for name in matched_names if name is not None]
                    
                    # 阈值设为60%，例如5个框至少要识别出3个
                    if len(valid_matches) >= equip_count * 0.6: 
                        self.equip_no_detect_time = time.time()
                        # [重构] 传递完整的、包含None的列表，以保持UI布局稳定
                        self.app.after(0, self.update_or_create_equip_windows, matched_names, equip_count)
                        self.last_stable_equip_count = equip_count # 状态现在由检测到的轮廓数决定
                    else:
                        # 如果匹配数量不足，也视作未检测到
                        if self.app.debug_mode:
                            print(f"[EquipManager] 匹配数量不足 ({len(valid_matches)}/{equip_count})，抛弃本次结果。")
                        # [优化] 如果当前稳定状态不为0，说明是从有装备变为无装备，立即清理
                        if self.last_stable_equip_count > 0:
                            self.app.after(0, self.clear_equip_results)
                            self.last_stable_equip_count = 0 # 清理后重置状态
                else: # equip_count == 0
                    # [优化] 如果之前有装备，现在视觉计数器返回0，立即清理
                    if self.last_stable_equip_count > 0:
                        self.app.after(0, self.clear_equip_results)
                        self.last_stable_equip_count = 0
                
                time.sleep(0.2) # 降低循环频率

            except Exception as e:
                if self.app.debug_mode:
                    print(f"装备OCR循环出错: {e}")
                logging.error(f"Equip OCR 主循环异常: {e}", exc_info=True)
                time.sleep(1)

    def update_or_create_equip_windows(self, matched_names, total_count):
        """
        [重构] 更新或创建装备信息窗口。
        - 布局始终由 `total_count` (轮廓检测数量) 决定。
        - `matched_names` 是一个包含None的列表。
        """
        # 使用新的、专门用于评级UI布局的坐标，由轮廓数决定
        regions = config.EQUIP_RATING_REGIONS_BY_COUNT.get(total_count, [])
        
        # [新增] 获取游戏窗口坐标用于位置计算
        game_window_rect = self._get_game_window_rect_cached()
        logging.info(f"[Equip] 更新装备窗口时获取游戏窗口坐标: {game_window_rect}")
        
        # 如果窗口数量与期望的总数不匹配，先清理重建
        if len(self.equip_overlay_windows) != total_count:
            self.clear_equip_results()
            for _ in range(total_count):
                rating_window = tk.Toplevel(self.app)
                rating_window.overrideredirect(True)
                rating_window.attributes('-alpha', 0.7)
                rating_window.attributes('-topmost', True)
                rating_label = ttk.Label(rating_window, text="", anchor="center")
                rating_label.pack(expand=True, fill=tk.BOTH)
                self.equip_overlay_windows.append(rating_window)

        # 更新窗口内容和位置
        for i, name in enumerate(matched_names):
            if i >= len(regions) or i >= len(self.equip_overlay_windows):
                continue

            # [修改] 使用新的坐标转换函数
            x1, y1, x2, y2 = utils.convert_relative_to_absolute(regions[i], game_window_rect)
            width = x2 - x1
            height = y2 - y1
            # [优化] 移除频繁的坐标打印
            # if self.app.debug_mode:
            #     print(f"[EquipManager] 装备窗口 {i} 绝对坐标: ({x1}, {y1}) 尺寸: {width}x{height}")
            
            rating_window = self.equip_overlay_windows[i]
            rating_label = rating_window.winfo_children()[0]
            
            rating_window.geometry(f"{width}x{height}+{x1}+{y1}")

            # 如果name是None，则显示占位符"?"
            if name is None:
                rating_label.config(text="?", style="EquipRank.TLabel")
            else:
                result = self.equip_query.smart_query(name)
                rating = result.get('rating')
                if rating:
                    rating_label.config(text=rating, style=f"EquipRank{rating}.TLabel")
                else:
                    rating_label.config(text="?", style="EquipRank.TLabel")

            rating_window.deiconify()
            rating_window.lift()

    def clear_equip_results(self):
        """销毁所有装备窗口。"""
        for window in self.equip_overlay_windows:
            window.destroy()
        self.equip_overlay_windows = []
        self.last_stable_equip_count = 0 # [新增] 确保清理后稳定状态也被重置

    def destroy(self):
        """销毁EquipManager资源。"""
        print("正在销毁装备管理器资源...")
        self.equip_ocr_running = False
        self.app.after(0, self.clear_equip_results)

    def _get_game_window_rect_cached(self, force_update=False):
        """
        [新增] 获取缓存的游戏窗口坐标，减少频繁的API调用。
        
        Args:
            force_update (bool): 是否强制更新缓存
            
        Returns:
            tuple: (x, y, width, height) 游戏窗口坐标
        """
        current_time = time.time()
        
        # 判断是否需要更新缓存
        if (force_update or 
            self.cached_game_window_rect is None or 
            current_time - self.last_window_check_time > self.window_check_interval):
            
            # 获取新的窗口坐标
            self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
            self.last_window_check_time = current_time
            
            if self.app.debug_mode:
                print(f"[EquipManager] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
            logging.info(f"[Equip] 更新游戏窗口坐标缓存: {self.cached_game_window_rect}")
        
        return self.cached_game_window_rect

# ==============================================================================
# RoundManager - 轮次检测功能的总管理器
# ==============================================================================

class RoundManager:
    """
    轮次检测功能的总管理器。
    封装了与轮次识别相关的所有状态和业务逻辑。
    """
    def __init__(self, app_ref, hex_manager_ref):
        """
        初始化RoundManager。

        Args:
            app_ref (TFTAssistantApp): 主应用TFTAssistantApp的实例引用。
            hex_manager_ref (HexManager): HexManager的实例引用，用于跨模块通信。
        """
        self.app = app_ref
        self.hex_manager = hex_manager_ref
        
        # --- 状态变量 ---
        self.round_ocr_running = False
        self.last_round_image_hashes = [None, None]
        self.current_round = None
        self.previous_round = None
        self.round_check_time = 0
        self.target_round_detected = False

        # --- [新增] 缓存的游戏窗口坐标，与轮次强制检测同步 ---
        self.cached_game_window_rect = None

    def start_round_ocr(self):
        """启动轮次OCR检测"""
        if not self.round_ocr_running:
            self.round_ocr_running = True
            threading.Thread(target=self.run_round_ocr_loop, daemon=True).start()
            logging.info("轮次OCR检测已启动。")
            if self.app.debug_mode:
                print("轮次OCR检测已启动")

    def stop_round_ocr(self):
        """停止轮次OCR检测"""
        self.round_ocr_running = False
        logging.info("轮次OCR检测已停止。")
        if self.app.debug_mode:
            print("轮次OCR检测已停止")

    def run_round_ocr_loop(self):
        """轮次OCR检测循环，结合哈希变化和周期性检查。"""
        first_run = True
        # last_forced_check_time用于周期性强制检查
        last_forced_check_time = time.time()

        while self.round_ocr_running:
            try:
                current_time = time.time()
                force_check = (current_time - last_forced_check_time) > 10 # 每10秒强制检查一次

                # [优化] 实现用户要求的分辨率监测策略：
                # 每次触发强制轮次监测时，同步进行分辨率监测
                if force_check or self.cached_game_window_rect is None:
                    # [新增] 获取游戏窗口坐标，与强制检测同步
                    self.cached_game_window_rect = utils.get_game_window_rect(use_client_area=True)
                    last_forced_check_time = current_time
                    
                    if self.app.debug_mode:
                        print("[RoundManager] 触发周期性强制检查...")
                    logging.info(f"[Round] 周期性强制检查 - 游戏窗口坐标: {self.cached_game_window_rect}")

                # 截图和哈希检查逻辑
                for i, region in enumerate(config.ROUND_REGION_RELATIVE):
                    # [修改] 使用缓存的窗口坐标
                    x1, y1, x2, y2 = utils.convert_relative_to_absolute(region, self.cached_game_window_rect)
                    # [优化] 移除频繁的坐标打印，这是导致日志过多的主要原因
                    # if self.app.debug_mode:
                    #     print(f"[RoundManager] 轮次检测区域 {i} 绝对坐标: ({x1}, {y1}, {x2}, {y2})")
                    
                    img = ocr_handler.capture_screen((x1, y1, x2, y2))
                    
                    if not img:
                        continue

                    current_hash = utils.calculate_image_hash(img)
                    hash_diff = utils.hash_distance(current_hash, self.last_round_image_hashes[i])
                        
                    # 触发条件：哈希值变化 或 达到强制检查时间
                    if hash_diff > config.ROUND_HASH_THRESHOLD or force_check:
                        self.last_round_image_hashes[i] = current_hash
                        processed = self._preprocess_round_image(img)
                        # 直接调用pytesseract，避免双重预处理和错误的文本清理
                        round_text = pytesseract.image_to_string(processed, config=r'--oem 3 --psm 6 -l chi_sim').strip()
                        
                        if round_text:
                            logging.info(f"[Round] 区域 {i} 检测到文本变化，识别为: '{round_text}'")
                            # 只要识别出文本就处理，并跳出内层循环，避免重复处理
                            self._process_round_result(round_text)
                            break # 跳出对两个区域的遍历
                
                time.sleep(0.2)
                
            except Exception as e:
                if self.app.debug_mode:
                    print(f"轮次检测循环出错: {e}")
                logging.error(f"Round OCR 主循环异常: {e}", exc_info=True)
                time.sleep(1) # 出错时休息1秒
        
        self.last_round_image_hashes = [None, None]

    def _preprocess_round_image(self, image):
        """轮次图像预处理，优化轮次数字识别"""
        img_gray = image.convert('L')
        img_contrast = ImageEnhance.Contrast(img_gray).enhance(2.0)
        img_binary = img_contrast.point(lambda x: 0 if x < 150 else 255)
        return img_binary.resize((img_binary.width * 2, img_binary.height * 2), Image.Resampling.LANCZOS)

    def _process_round_result(self, round_text):
        """处理轮次OCR结果"""
        if self.app.debug_mode:
            print(f"轮次OCR原始结果: [{round_text}]")
        
        self.previous_round = self.current_round
        # 使用专为轮次设计的清理逻辑，只保留数字和'-'
        cleaned_text = re.sub(r'[^0-9\-]', '', round_text)
        self.current_round = cleaned_text if cleaned_text else None
        
        if self.app.debug_mode:
            print(f"清理后的轮次: {self.current_round}")
        
        if self.current_round in config.TARGET_ROUNDS:
            if not self.target_round_detected: # 仅在首次检测到目标轮次时记录
                logging.info(f"检测到目标轮次: {self.current_round}。准备触发海克斯OCR。")
            if self.app.debug_mode:
                print(f"检测到目标轮次: {self.current_round}")
            self.target_round_detected = True
        
        elif self.target_round_detected and self.current_round != self.previous_round:
            if self.app.debug_mode:
                print(f"目标轮次已结束，触发海克斯OCR")
            logging.info(f"目标轮次 {self.previous_round} 已结束 (新轮次: {self.current_round})，自动触发海克斯OCR。")
            self.target_round_detected = False
            if not self.hex_manager.is_running():
                # 修复：调用start_hex_ocr并禁止弹窗
                self.hex_manager.start_hex_ocr(show_popup=False)
                
    def is_running(self):
        """检查轮次OCR是否正在运行。"""
        return self.round_ocr_running

    def get_current_round(self):
        """获取当前识别的轮次。"""
        return self.current_round

# 未来可以添加 EquipManager 等 
# 未来可以添加 EquipManager 等 