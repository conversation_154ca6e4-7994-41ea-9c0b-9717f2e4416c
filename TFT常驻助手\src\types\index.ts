// 页面常量定义
export const PAGE_CONSTANTS = {
  PAGE_COMP_LIST: 0,
  PAGE_HERO_LIST: 1,
  PAGE_ITEM_LIST: 2,
  PAGE_HEX_LIST: 3,
  PAGE_COMP_DETAIL: 4,
  PAGE_HERO_DETAIL: 5,
  PAGE_ITEM_DETAIL: 6,
} as const;

// 页面类型
export type PageType = typeof PAGE_CONSTANTS[keyof typeof PAGE_CONSTANTS];

// 导航历史记录项
export interface NavigationHistoryItem {
  pageIndex: number;
  dataKey: string;
}

// 主应用状态接口
export interface MainAppState {
  currentPage: number;
  isMinimized: boolean;
  historyStack: NavigationHistoryItem[];
  viewsLoaded: Record<number, boolean>;
}

// 全局数据缓存接口
export interface GlobalDataCache {
  heroInfoMap: Record<string, any>;
  traitIconMap: Record<string, string>;
  itemInfoMap: Record<string, any>;
  queryCache: Record<string, any>;
}

// 数据查询结果接口
export interface QueryResult {
  data: any[];
  error?: string;
}

// 英雄信息接口
export interface HeroInfo {
  cn_name: string;
  en_name: string;
  cost: number;
  traits: string[];
  icon_path: string;
  [key: string]: any;
}

// 装备信息接口
export interface ItemInfo {
  name: string;
  en_name: string;
  icon_path: string;
  components: string[];
  [key: string]: any;
}

// 羁绊信息接口
export interface TraitInfo {
  name: string;
  icon_path: string;
  [key: string]: any;
}

// 阵容信息接口
export interface CompInfo {
  name: string;
  tier: string;
  traits: string[];
  heroes: string[];
  [key: string]: any;
}

// 海克斯信息接口
export interface HexInfo {
  name: string;
  tier: string;
  description: string;
  icon_path: string;
  [key: string]: any;
}