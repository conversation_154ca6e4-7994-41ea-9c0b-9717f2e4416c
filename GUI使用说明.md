# GUI总爬取程序使用说明

## 功能概述

修改后的 `GUI总爬取程序.py` 集成了以下功能：
1. **数据爬取** - 爬取海克斯、羁绊、装备、英雄、英雄装备、阵容数据
2. **数据库重建** - 爬取完成后自动重建完整数据库
3. **制品库上传** - 将数据库上传到制品库进行版本管理

## 新增功能

### 1. 数据库重建选项
- 位置：爬取选项区域
- 功能：勾选后，数据爬取完成会自动重建数据库
- 默认：已勾选
- 要求：需要 `数据库建立.py` 模块可用

### 2. 制品库配置区域
- 显示当前 CODING_TOKEN 状态
- 显示制品库配置信息
- 提供刷新Token状态按钮

### 3. 新增按钮

#### 重建数据库按钮
- 功能：手动重建数据库
- 位置：第二行按钮区域
- 状态：需要数据库建立模块可用

#### 上传数据库按钮  
- 功能：将数据库上传到制品库
- 位置：第二行按钮区域
- 状态：需要设置 CODING_TOKEN 环境变量

#### 刷新Token状态按钮
- 功能：重新检查 CODING_TOKEN 环境变量状态
- 用途：在运行时设置了环境变量后刷新状态

## 使用流程

### 基本数据爬取流程
1. 选择要爬取的模块
2. 配置爬取选项（强制更新、下载图标、自动重建数据库）
3. 点击"开始爬取"
4. 等待爬取完成
5. 如果勾选了自动重建，会自动执行数据库重建

### 手动数据库重建
1. 确保已有爬取的数据文件
2. 点击"重建数据库"按钮
3. 确认操作
4. 等待重建完成

### 上传到制品库
1. 设置 CODING_TOKEN 环境变量
2. 点击"刷新Token状态"按钮确认状态
3. 确保数据库文件存在（如不存在会提示重建）
4. 点击"上传数据库"按钮
5. 确认上传操作
6. 等待上传完成

## 环境变量设置

### Windows
```cmd
set CODING_TOKEN=your_token_here
```

### Linux/Mac
```bash
export CODING_TOKEN=your_token_here
```

### Python脚本中设置（不推荐）
```python
import os
os.environ['CODING_TOKEN'] = 'your_token_here'
```

## 依赖要求

### 必需依赖
- `tkinter` - GUI界面
- `requests` - 制品库上传
- `hashlib` - 文件校验

### 可选依赖
- `数据库建立.py` - 数据库重建功能
- 各爬取模块 - 对应的数据爬取功能

## 状态指示

### Token状态
- ✓ 已设置（绿色）- 可以使用上传功能
- ✗ 未设置（红色）- 无法使用上传功能

### 按钮状态
- 启用 - 功能可用
- 禁用 - 缺少依赖或配置

### 进度显示
- 进度条 - 显示当前操作进度
- 状态文本 - 显示当前操作状态
- 日志区域 - 显示详细的操作日志

## 错误处理

### 常见问题
1. **CODING_TOKEN未设置** - 设置环境变量后点击刷新
2. **数据库文件不存在** - 程序会提示是否重建数据库
3. **网络连接问题** - 检查网络连接和制品库访问权限
4. **权限问题** - 确保有文件读写权限

### 日志查看
- 所有操作都会在日志区域显示详细信息
- 支持自动滚动、手动滚动、清空日志等操作
- 错误信息会以弹窗形式显示并记录在日志中

## 注意事项

1. **数据完整性** - 建议在数据爬取完成后再进行数据库重建
2. **网络稳定性** - 上传大文件时确保网络连接稳定
3. **存储空间** - 确保本地和制品库都有足够存储空间
4. **版本管理** - 每次上传都会生成新的版本号
5. **备份建议** - 重要数据建议先备份再进行操作 