@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: ============================================================================
:: 构建脚本 - 弈秒决打包脚本 (v4.1 - .spec 最终版)
:: ============================================================================
::
:: 功能:
::   - 使用项目根目录下的 .spec 文件进行稳定、可重复的构建。
::   - 清理 (clean), 构建 (python), 创建安装包 (installer)
::
:: ============================================================================

:: 检查用户是否指定了任务，默认为 "all"
set "TASK=all"
if not "%1"=="" set "TASK=%1"

:: 根据任务跳转到对应的标签
goto :task_%TASK%


:task_all
    echo [任务] 执行所有构建步骤...
    call :do_clean
    call :do_build_python
    call :do_build_installer
    call :do_summary
    goto :eof

:task_clean
    echo [任务] 只执行清理...
    call :do_clean
    echo 清理完成!
    goto :eof

:task_python
    echo [任务] 只构建Python可执行文件...
    call :do_build_python
    echo Python可执行文件构建完成!
    goto :eof

:task_installer
    echo [任务] 只创建安装包...
    call :do_build_installer
    echo 安装包创建完成!
    goto :eof

echo [错误] 无效的任务: "%TASK%". 有效任务: all, clean, python, installer
goto :eof


:: ============================================================================
::                        构建任务具体实现
:: ============================================================================

:do_clean
    echo [1/3] 清理旧的构建文件和残留进程...
    echo      - 正在尝试终止残留的 YimiaoJue.exe 进程...
    taskkill /F /IM YimiaoJue.exe /T > nul 2>&1
    echo      - 正在删除旧的构建目录...
    if exist "build" ( rmdir /s /q build )
    if exist "dist" ( rmdir /s /q dist )
    echo      - 清理完成
    echo.
    goto :eof

:do_build_python
    echo [2/3] 正在使用 .spec 文件构建程序...

    if not exist "YimiaoJue.spec" (
        echo [错误] 找不到 YimiaoJue.spec 文件!
        pause
        goto :eof
    )
    if not exist "updater.spec" (
        echo [错误] 找不到 updater.spec 文件!
        pause
        goto :eof
    )

    echo   - 正在构建主程序 (YimiaoJue)...
    python -m PyInstaller YimiaoJue.spec --noconfirm
    if !errorlevel! neq 0 (
        echo [错误] 构建主程序失败，请检查日志。
        pause
        goto :eof
    )

    echo   - 正在构建更新器 (updater)...
    python -m PyInstaller updater.spec --noconfirm
    if !errorlevel! neq 0 (
        echo [错误] 构建更新器失败，请检查日志。
        pause
        goto :eof
    )
    
    echo      - Python可执行文件构建成功
    echo.
    goto :eof

:do_build_installer
    REM 配置变量
    set "APP_VERSION=0.2.5"
    set "INNO_SCRIPT=YimiaoJue_Installer.iss"

    echo [3/3] 正在创建最终的安装包 (版本 %APP_VERSION%)...
    if not exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
        echo [错误] 找不到 Inno Setup 编译器。
        echo        请确保 Inno Setup 已安装在默认路径。
        pause
        goto :eof
    )
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" /DMyAppVersion="%APP_VERSION%" "%INNO_SCRIPT%"
    if !errorlevel! neq 0 (
        echo [错误] 创建安装包失败，请检查 Inno Setup 输出日志
        pause
        goto :eof
    )
    echo      - 安装包创建成功
    echo.
    goto :eof

:do_summary
    echo ==================================================
    echo.
    echo           构建完成! (.spec 模式)
    echo.
    echo   主程序: dist/YimiaoJue/ (目录模式)
    echo   更新器: dist/updater.exe (单文件)
    echo   安装包: dist/YimiaoJue_Setup_v%APP_VERSION%.exe
    echo.
    echo ==================================================
    pause
    goto :eof

endlocal