import { describe, it, expect, beforeEach } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useAppStore } from '../app';
import { PAGE_CONSTANTS } from '@/types';

describe('App Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('应该正确初始化状态', () => {
    const store = useAppStore();
    
    expect(store.currentPage).toBe(PAGE_CONSTANTS.PAGE_COMP_LIST);
    expect(store.isMinimized).toBe(false);
    expect(store.historyStack).toEqual([]);
    expect(store.viewsLoaded).toEqual({});
    expect(store.hasHistory).toBe(false);
    expect(store.isDetailPage).toBe(false);
    expect(store.isMainPage).toBe(true);
  });

  it('应该正确切换主页面', () => {
    const store = useAppStore();
    
    store.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
    
    expect(store.currentPage).toBe(PAGE_CONSTANTS.PAGE_HERO_LIST);
    expect(store.historyStack).toEqual([]);
    expect(store.viewsLoaded[PAGE_CONSTANTS.PAGE_HERO_LIST]).toBe(true);
  });

  it('应该正确导航到详情页面', () => {
    const store = useAppStore();
    
    // 先设置一个主页面
    store.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
    
    // 导航到详情页面
    store.navigateToDetail(PAGE_CONSTANTS.PAGE_HERO_DETAIL, 'test_hero');
    
    expect(store.currentPage).toBe(PAGE_CONSTANTS.PAGE_HERO_DETAIL);
    expect(store.historyStack).toHaveLength(1);
    expect(store.historyStack[0]).toEqual({
      pageIndex: PAGE_CONSTANTS.PAGE_HERO_LIST,
      dataKey: ''
    });
    expect(store.isDetailPage).toBe(true);
    expect(store.isMainPage).toBe(false);
  });

  it('应该正确返回上一页', () => {
    const store = useAppStore();
    
    // 设置初始状态
    store.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
    store.navigateToDetail(PAGE_CONSTANTS.PAGE_HERO_DETAIL, 'test_hero');
    
    // 返回上一页
    store.goBack();
    
    expect(store.currentPage).toBe(PAGE_CONSTANTS.PAGE_HERO_LIST);
    expect(store.historyStack).toHaveLength(0);
  });

  it('应该正确切换窗口状态', () => {
    const store = useAppStore();
    
    expect(store.isMinimized).toBe(false);
    
    store.toggleWindow();
    expect(store.isMinimized).toBe(true);
    
    store.toggleWindow();
    expect(store.isMinimized).toBe(false);
  });

  it('应该正确重置状态', () => {
    const store = useAppStore();
    
    // 修改一些状态
    store.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
    store.navigateToDetail(PAGE_CONSTANTS.PAGE_HERO_DETAIL, 'test_hero');
    store.setMinimized(true);
    
    // 重置状态
    store.resetState();
    
    expect(store.currentPage).toBe(PAGE_CONSTANTS.PAGE_COMP_LIST);
    expect(store.isMinimized).toBe(false);
    expect(store.historyStack).toEqual([]);
    expect(store.viewsLoaded).toEqual({});
  });
});