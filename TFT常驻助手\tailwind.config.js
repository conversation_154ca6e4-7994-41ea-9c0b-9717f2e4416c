/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // 复刻常量与配置.py中的颜色配置
        'window-border': '#0F172A',
        'window-bg': '#1E293B',
        'control-bar-bg': '#0F172A',
        'section-bg': '#293548',
        'item-row-hover': '#334155',
        'border-color': '#4A5568',
        'text-light': '#E2E8F0',
        'text-medium': '#A0AEC0',
        'text-dark': '#1A202C',
        'text-highlight': '#8EBBFF',
        'text-error': '#FF6B6B',
        'tier-s': '#FF5A5F',
        'tier-a': '#FFA756',
        'tier-b': '#FFD700',
        'tier-c': '#F3FF59',
        'tier-d': '#8AFF40',
        'cost-1': '#D3D3D3',
        'cost-2': '#32CD32',
        'cost-3': '#00BFFF',
        'cost-4': '#BF00FF',
        'cost-5': '#FFD700',
        // shadcn/vue 兼容性颜色
        border: '#4A5568',
        input: '#334155',
        ring: '#8EBBFF',
        background: '#1E293B',
        foreground: '#E2E8F0',
        primary: {
          DEFAULT: '#8EBBFF',
          foreground: '#1A202C'
        },
        secondary: {
          DEFAULT: '#293548',
          foreground: '#E2E8F0'
        },
        destructive: {
          DEFAULT: '#FF6B6B',
          foreground: '#E2E8F0'
        },
        muted: {
          DEFAULT: '#293548',
          foreground: '#A0AEC0'
        },
        accent: {
          DEFAULT: '#334155',
          foreground: '#E2E8F0'
        },
        popover: {
          DEFAULT: '#1E293B',
          foreground: '#E2E8F0'
        },
        card: {
          DEFAULT: '#1E293B',
          foreground: '#E2E8F0'
        }
      },
      fontSize: {
        'xs': '11px',
        'sm': '12px',
        'base': '13px',
        'lg': '14px',
        'xl': '16px'
      },
      width: {
        '15': '60px', // TierLabel 中等尺寸
        '20': '80px', // HeroCard 头像尺寸
      },
      height: {
        'control-bar': '32px',
        'nav-bar': '40px',
        '15': '60px', // TierLabel 中等尺寸
        '20': '80px', // HeroCard 头像尺寸
      },
      borderRadius: {
        lg: '8px',
        md: '6px',
        sm: '4px'
      }
    },
  },
  plugins: [],
}