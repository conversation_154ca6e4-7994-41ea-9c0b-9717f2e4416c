# 视图模块/装备详情视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
                               QSpacerItem, QSizePolicy, QFrame, QPushButton, QStyle)
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QCursor, QFont

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_HIGHLIGHT,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ITEM_ROW_BG_HOVER,
                       ICON_SIZE_XLARGE, ICON_SIZE_LARGE, SECTION_BG_COLOR, CONTROL_BAR_BG)
from 自定义组件 import IconLabel, ClickableLabel, SearchLineEdit # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

class 装备详情视图(QWidget):
    """显示装备详细信息的视图"""
    hero_selected = Signal(str) # 点击英雄时发出信号，参数为英雄中文名

    def __init__(self, parent=None):
        super().__init__(parent)
        self.item_name = ""
        self.item_base_data = {} # 存储装备基础信息
        self.hero_stats_data = [] # 存储适用英雄的原始统计数据
        self.filtered_hero_stats_data = [] # 存储筛选和排序后的英雄数据
        self.current_sort_column = 'play_rate' # 默认按出场率排序
        self.current_sort_order = Qt.SortOrder.DescendingOrder # 默认降序

        self.header_widgets = {} # 存储表头标签

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(15, 10, 15, 15) # 调整边距
        self.main_layout.setSpacing(12) # 调整间距
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 顶部装备信息区域 ---
        self.item_info_frame = QFrame()
        self.item_info_frame.setStyleSheet(f"background-color: {SECTION_BG_COLOR}; border-radius: 6px; padding: 10px;")
        item_info_layout = QHBoxLayout(self.item_info_frame)
        item_info_layout.setContentsMargins(5, 5, 5, 5)
        item_info_layout.setSpacing(15)

        self.item_icon_label = IconLabel(icon_size=ICON_SIZE_XLARGE, icon_type='item', placeholder_text='?')
        item_info_layout.addWidget(self.item_icon_label)

        item_detail_layout = QVBoxLayout()
        item_detail_layout.setSpacing(4)
        self.item_name_label = QLabel("装备名称")
        self.item_name_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {TEXT_COLOR_LIGHT};")
        # 统计数据行
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(15) # 统计数据间距
        self.avg_place_label = QLabel("平均排名: --")
        self.top1_rate_label = QLabel("登顶率: --")
        self.play_rate_label = QLabel("出场率: --") # 新增出场率显示
        for label in [self.avg_place_label, self.top1_rate_label, self.play_rate_label]:
            label.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size: 10px;")
        stats_layout.addWidget(self.play_rate_label)
        stats_layout.addWidget(self.avg_place_label)
        stats_layout.addWidget(self.top1_rate_label)
        stats_layout.addStretch()

        item_detail_layout.addWidget(self.item_name_label)
        item_detail_layout.addLayout(stats_layout)
        item_info_layout.addLayout(item_detail_layout)
        item_info_layout.addStretch()
        self.main_layout.addWidget(self.item_info_frame)

        # --- 适用英雄区域 ---
        heroes_section_layout = QVBoxLayout()
        heroes_section_layout.setSpacing(5)

        # 标题和搜索框行
        title_search_layout = QHBoxLayout()
        title_label = QLabel("适用英雄统计")
        title_label.setStyleSheet(f"font-size: 14px; font-weight: bold; color: {TEXT_COLOR_LIGHT};")
        self.hero_search_box = SearchLineEdit(placeholder_text="搜索英雄名称...")
        self.hero_search_box.searchChanged.connect(self.filter_heroes)
        title_search_layout.addWidget(title_label)
        title_search_layout.addStretch()
        title_search_layout.addWidget(self.hero_search_box)
        heroes_section_layout.addLayout(title_search_layout)

        # --- 新增：固定英雄列表表头区域 ---
        self.heroes_header_frame = QFrame()
        self.heroes_header_frame.setStyleSheet(f"background-color: {CONTROL_BAR_BG}; border: 1px solid {BORDER_COLOR}; border-radius: 4px;")
        self.heroes_header_frame.setFixedHeight(45)  # 固定表头高度
        self.heroes_header_layout = QVBoxLayout(self.heroes_header_frame)
        self.heroes_header_layout.setContentsMargins(0, 0, 0, 0)
        self.heroes_header_layout.setSpacing(0)
        heroes_section_layout.addWidget(self.heroes_header_frame)
        self.heroes_header_frame.hide()  # 初始隐藏，等数据加载完成后显示

        # 英雄列表滚动区域
        self.heroes_scroll_area = QScrollArea()
        self.heroes_scroll_area.setWidgetResizable(True)
        self.heroes_scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE + f" border: 1px solid {BORDER_COLOR}; border-radius: 4px; background-color: {CONTROL_BAR_BG};") # 加边框和背景
        self.heroes_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.heroes_content_widget = QWidget()
        self.heroes_content_layout = QVBoxLayout(self.heroes_content_widget)
        self.heroes_content_layout.setContentsMargins(0, 0, 0, 0) # 内部无边距
        self.heroes_content_layout.setSpacing(1) # 行间距

        self.heroes_scroll_area.setWidget(self.heroes_content_widget)
        heroes_section_layout.addWidget(self.heroes_scroll_area, 1) # 占据主要空间

        self.main_layout.addLayout(heroes_section_layout, 1) # 英雄区域占据剩余空间

        # 初始加载提示
        self.loading_label = QLabel("请先选择一个装备", alignment=Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 14px; padding: 30px;")
        self.heroes_content_layout.addWidget(self.loading_label)


    def load_item_data(self, item_name):
        """加载指定装备的数据"""
        if not item_name or self.item_name == item_name:
            return # 防止重复加载或无效加载
        self.item_name = item_name
        print(f"开始加载装备详情: {self.item_name}")

        # 重置状态
        self.hero_search_box.clear()
        self.current_sort_column = 'play_rate'
        self.current_sort_order = Qt.SortOrder.DescendingOrder
        self.item_base_data = {}
        self.hero_stats_data = []
        self.filtered_hero_stats_data = []
        self.update_ui() # 清空旧内容并显示加载中

        # 异步加载装备基础信息
        sql_base = "SELECT name, icon_path, avg_placement, top1_rate, play_rate FROM items WHERE name = ?"
        query_key_base = f"item_base_{item_name}"
        execute_query_async(
            sql_base, (item_name,),
            on_success=self.on_base_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key_base
        )

        # 异步加载适用英雄统计信息
        sql_heroes = """
            SELECT es.hero_cn_name, es.play_rate, es.avg_place, es.top4_rate, es.top1_rate, h.icon_path
            FROM equipment_hero_stats es
            JOIN heroes h ON es.hero_cn_name = h.cn_name
            WHERE es.item_name = ?
        """
        query_key_heroes = f"item_heroes_{item_name}"
        execute_query_async(
            sql_heroes, (item_name,),
            on_success=self.on_hero_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key_heroes
        )

    def on_base_data_loaded(self, results):
        """装备基础信息加载成功"""
        if results:
            self.item_base_data = results[0]
            self.update_item_info_ui() # 更新顶部信息
        else:
            print(f"警告: 未找到装备 {self.item_name} 的基础数据。")
            self.item_name_label.setText(f"{self.item_name} (未找到)")
            # 其他字段保持默认或清空

    def on_hero_data_loaded(self, results):
        """适用英雄数据加载成功"""
        self.hero_stats_data = results if results else []
        self.filter_heroes("") # 触发初始排序和显示

    def on_data_load_error(self, error_message):
        """数据加载失败"""
        print(f"加载装备 {self.item_name} 数据失败: {error_message}")
        # 可以在英雄列表区域显示错误
        self.display_hero_list_error(f"加载英雄统计失败:\n{error_message}")
        # 如果是基础信息加载失败，顶部也应提示
        if not self.item_base_data:
             self.item_name_label.setText(f"{self.item_name} (加载错误)")

    def update_item_info_ui(self):
        """更新顶部的装备信息显示"""
        if not self.item_base_data: return

        self.item_name_label.setText(self.item_base_data.get('name', 'N/A'))
        icon_path = self.item_base_data.get('icon_path')
        self.item_icon_label.set_icon(icon_path, self.item_base_data.get('name'))

        avg_place = self.item_base_data.get('avg_placement')
        self.avg_place_label.setText(f"平均排名: {avg_place:.2f}" if avg_place is not None else "平均排名: --")
        
        top1_rate = self.item_base_data.get('top1_rate')
        self.top1_rate_label.setText(f"登顶率: {top1_rate:.1f}%" if top1_rate is not None else "登顶率: --")
        
        play_rate = self.item_base_data.get('play_rate')
        if play_rate is not None:
            # 判断数据范围，大于1的直接显示，小于1的使用百分比格式
            if play_rate > 1:
                self.play_rate_label.setText(f"出场率: {play_rate:.1f}%")
            else:
                self.play_rate_label.setText(f"出场率: {play_rate:.1%}")
        else:
            self.play_rate_label.setText("出场率: --")


    def filter_heroes(self, search_term):
        """根据搜索框内容筛选英雄列表"""
        search_term = search_term.strip().lower()

        if not search_term:
            self.filtered_hero_stats_data = list(self.hero_stats_data)
        else:
            self.filtered_hero_stats_data = [
                hero for hero in self.hero_stats_data
                if search_term in hero.get('hero_cn_name', '').lower()
            ]
        self.sort_heroes()
        # self.update_hero_list_ui() # sort_heroes 会调用

    def sort_heroes(self):
        """根据当前排序规则对 filtered_hero_stats_data 进行排序"""
        print(f"开始排序英雄: 列={self.current_sort_column}, 顺序={self.current_sort_order}")
        try:
            self.filtered_hero_stats_data.sort(
                key=lambda x: x.get(self.current_sort_column) if x.get(self.current_sort_column) is not None else (float('-inf') if self.current_sort_order == Qt.SortOrder.DescendingOrder else float('inf')),
                reverse=(self.current_sort_order == Qt.SortOrder.DescendingOrder)
            )
        except Exception as e:
            print(f"英雄排序时发生错误: {e}")
            # 可以添加回退排序逻辑

        self.update_hero_list_ui() # 排序后更新列表

    @Slot(str)
    def handle_sort_request(self, column_key):
        """处理英雄列表表头点击事件"""
        print(f"收到英雄列表排序请求: {column_key}")
        if self.current_sort_column == column_key:
            # 如果点击的是当前排序列，切换排序顺序
            self.current_sort_order = Qt.SortOrder.AscendingOrder if self.current_sort_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
        else:
            # 如果点击的是新列，设置新列为排序列，并根据列名设置默认排序规则
            self.current_sort_column = column_key
            # --- 修改开始：设置各列默认排序 ---
            # "好的数据"排在前面
            if column_key == 'avg_place':
                # 平均排名：越小越好 -> 升序
                self.current_sort_order = Qt.SortOrder.AscendingOrder
            else:
                # 出场率、前四率、登顶率：越大越好 -> 降序
                self.current_sort_order = Qt.SortOrder.DescendingOrder
            # --- 修改结束 ---
        self.update_header_styles()
        self.sort_heroes()

    def update_header_styles(self):
        """更新英雄列表表头样式"""
        sort_indicator = " ▼" if self.current_sort_order == Qt.SortOrder.DescendingOrder else " ▲"
        base_style = f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: 10px; font-weight: bold;"
        selected_style = f"color: {TEXT_COLOR_LIGHT}; font-size: 10px; font-weight: bold;"

        for key, label in self.header_widgets.items():
            text = label.property("original_text")
            if key == self.current_sort_column:
                label.setStyleSheet(selected_style)
                label.setText(text + sort_indicator)
            else:
                label.setStyleSheet(base_style)
                label.setText(text)

    def update_ui(self):
        """更新整个装备详情视图 (通常在加载新装备时调用)"""
        self.update_item_info_ui()
        self.update_hero_list_ui()

    def update_hero_list_ui(self):
        """更新英雄列表显示"""
        # 清空现有内容
        while self.heroes_content_layout.count():
            item = self.heroes_content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()

        if not self.filtered_hero_stats_data:
            status_text = "无适用英雄数据"
            if not self.hero_stats_data:
                status_text = "加载中..." if not self.item_base_data else "暂无英雄统计数据"
            elif self.hero_search_box.text().strip():
                status_text = "未找到匹配的英雄"

            status_label = QLabel(status_text, alignment=Qt.AlignmentFlag.AlignCenter)
            status_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 12px; padding: 20px;")
            self.heroes_content_layout.addWidget(status_label)
            self.heroes_header_frame.hide()  # 隐藏表头
            return

        # 显示表头和数据
        self.heroes_header_frame.show()  # 显示固定表头
        self.update_heroes_header()

        # 添加英雄数据行（不再包含表头）
        for hero_data in self.filtered_hero_stats_data:
            hero_row = self.create_hero_list_row(hero_data)
            if hero_row:
                self.heroes_content_layout.addWidget(hero_row)

    def update_heroes_header(self):
        """更新固定英雄列表表头"""
        # 清空现有表头内容
        while self.heroes_header_layout.count():
            item = self.heroes_header_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 创建新的表头
        header_widget = self.create_hero_list_row(is_header=True)
        if header_widget:
            self.heroes_header_layout.addWidget(header_widget)
            # 更新表头样式
            self.update_header_styles()

    def display_hero_list_error(self, message):
         """在英雄列表区域显示错误"""
         while self.heroes_content_layout.count():
            item = self.heroes_content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()
         error_label = QLabel(message, alignment=Qt.AlignmentFlag.AlignCenter)
         error_label.setStyleSheet(f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: 12px; padding: 20px;")
         error_label.setWordWrap(True)
         self.heroes_content_layout.addWidget(error_label)

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置装备详情视图到初始状态")
        
        # 重置英雄搜索框
        if hasattr(self, 'hero_search_box') and self.hero_search_box:
            self.hero_search_box.clear()  # 这会触发搜索信号，自动重新筛选
        
        # 重置排序状态为默认
        self.current_sort_column = 'play_rate'
        self.current_sort_order = Qt.SortOrder.DescendingOrder
        
        # 重置滚动位置（如果滚动区域可见）
        if hasattr(self, 'heroes_scroll_area') and self.heroes_scroll_area and self.heroes_scroll_area.isVisible():
            self.heroes_scroll_area.verticalScrollBar().setValue(0)
        
        # 如果有英雄数据，重新应用筛选和排序
        if hasattr(self, 'hero_stats_data') and self.hero_stats_data:
            self.filter_heroes("")  # 空字符串会显示所有英雄并应用默认排序

    def create_hero_list_row(self, hero_data=None, is_header=False):
        """创建英雄列表中的一行 (表头或数据行)"""
        row_widget = QWidget()
        row_widget.setMinimumHeight(40)
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(8, 3, 8, 3)
        row_layout.setSpacing(10)

        # --- 列定义 ---
        col_keys = ['icon', 'hero_cn_name', 'play_rate', 'avg_place', 'top4_rate', 'top1_rate']
        col_widths = {
            'icon': ICON_SIZE_LARGE - 4,
            'hero_cn_name': -1, # 自动伸展
            'play_rate': 65,
            'avg_place': 65,
            'top4_rate': 65,
            'top1_rate': 65
        }
        col_alignments = {
            'icon': Qt.AlignmentFlag.AlignCenter,
            'hero_cn_name': Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter,
            'play_rate': Qt.AlignmentFlag.AlignCenter,
            'avg_place': Qt.AlignmentFlag.AlignCenter,
            'top4_rate': Qt.AlignmentFlag.AlignCenter,
            'top1_rate': Qt.AlignmentFlag.AlignCenter
        }
        col_titles = {
            'icon': '', # 图标列标题为空
            'hero_cn_name': '英雄',
            'play_rate': '出场率',
            'avg_place': '平均排名',
            'top4_rate': '前四率',
            'top1_rate': '登顶率'
        }

        # --- 创建列控件 ---
        col_widgets = {}
        for key in col_keys:
            # --- 修改开始 ---
            # 如果是表头并且列是'icon'，则跳过创建，用一个占位符或者干脆不添加
            if is_header and key == 'icon':
                # 添加一个固定宽度的 SpacerItem 来占据图标的位置，但不可见
                spacer = QSpacerItem(col_widths['icon'], 0, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)
                row_layout.addSpacerItem(spacer)
                continue # 跳过后续对此列的处理
            # --- 修改结束 ---

            if is_header and key != 'icon': # 表头非图标列
                label = ClickableLabel(data=key)
                label.clicked.connect(self.handle_sort_request)
                label.setProperty("original_text", col_titles[key])
                self.header_widgets[key] = label
            else: # 数据行或数据行的图标列
                if key == 'icon':
                    label = IconLabel(icon_size=col_widths['icon'], icon_type='hero', placeholder_text='?')
                else:
                    label = QLabel()

            label.setAlignment(col_alignments[key])
            if col_widths[key] > 0:
                label.setFixedWidth(col_widths[key])

            col_widgets[key] = label
            row_layout.addWidget(label, 1 if col_widths[key] == -1 else 0)

        # --- 填充内容 ---
        if is_header:
            header_style = f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: 10px; font-weight: bold;"
            # 现在 header_widgets 里已经没有 'icon' 了
            for key, label in self.header_widgets.items():
                 label.setText(col_titles[key])
                 label.setStyleSheet(header_style)
                 label.setCursor(Qt.CursorShape.PointingHandCursor)
        elif hero_data:
            hero_name = hero_data.get('hero_cn_name', 'N/A')
            row_widget.setCursor(Qt.CursorShape.PointingHandCursor)
            # 使用 lambda 确保传递正确的 hero_name
            row_widget.mousePressEvent = lambda event, name=hero_name: self.hero_selected.emit(name) if event.button() == Qt.MouseButton.LeftButton else None
            row_widget.enterEvent = lambda event, w=row_widget: w.setStyleSheet(f"background-color: {ITEM_ROW_BG_HOVER}; border-radius: 4px;")
            row_widget.leaveEvent = lambda event, w=row_widget: w.setStyleSheet("background-color: transparent;")

            # 图标
            icon_path = hero_data.get('icon_path') # 从 JOIN 查询结果获取
            if 'icon' in col_widgets: # 确保图标控件存在（数据行一定存在）
                col_widgets['icon'].set_icon(icon_path, hero_name)

            # 名称 (可点击)
            name_label = ClickableLabel(data=hero_name)
            name_label.setText(hero_name)
            name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: 11px;")
            name_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
            name_label.clicked.connect(lambda data: self.hero_selected.emit(data))
            if 'hero_cn_name' in col_widgets: # 确保名称控件存在
                old_label = col_widgets['hero_cn_name']
                row_layout.replaceWidget(old_label, name_label)
                old_label.deleteLater()
                col_widgets['hero_cn_name'] = name_label


            # 统计数据
            value_style = f"color: {TEXT_COLOR_LIGHT}; font-size: 10px;"
            play_rate = hero_data.get('play_rate')
            if play_rate is not None:
                col_widgets['play_rate'].setText(f"{play_rate:.1f}%")
            else:
                col_widgets['play_rate'].setText("--")
            col_widgets['play_rate'].setStyleSheet(value_style)

            avg_place = hero_data.get('avg_place')
            col_widgets['avg_place'].setText(f"{avg_place:.2f}" if avg_place is not None else "--")
            col_widgets['avg_place'].setStyleSheet(value_style)

            top4_rate = hero_data.get('top4_rate')
            if top4_rate is not None:
                # 数据本身就是百分号大小，只需要加上%符号
                col_widgets['top4_rate'].setText(f"{top4_rate:.1f}%")
            else:
                col_widgets['top4_rate'].setText("--")
            col_widgets['top4_rate'].setStyleSheet(value_style)

            top1_rate = hero_data.get('top1_rate')
            col_widgets['top1_rate'].setText(f"{top1_rate:.1f}%" if top1_rate is not None else "--")
            col_widgets['top1_rate'].setStyleSheet(value_style)


        return row_widget