#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系统测试工具

用于测试和验证虚拟16:9边框方案的可视化工具。
可以实时显示游戏窗口检测结果和海克斯区域框。
"""

import tkinter as tk
from tkinter import ttk, messagebox
import win32gui
import win32api
import sys
import time

# 导入配置文件中的海克斯区域坐标
import config

class CoordinateTestTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("坐标系统测试工具")
        self.root.geometry("500x400")
        
        # 测试框列表，用于显示海克斯区域
        self.test_boxes = []
        self.virtual_frame_box = None  # 虚拟16:9边框显示
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🎮 坐标系统测试工具", 
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="窗口信息", padding="10")
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.info_text = tk.Text(info_frame, height=12, width=60, 
                                font=("Consolas", 9), state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 捕获窗口按钮
        self.capture_btn = ttk.Button(button_frame, text="🔍 捕获游戏窗口并显示方框", 
                                     command=self.capture_and_display, 
                                     style="Accent.TButton")
        self.capture_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 隐藏方框按钮
        self.hide_btn = ttk.Button(button_frame, text="❌ 隐藏所有方框", 
                                  command=self.hide_all_boxes)
        self.hide_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 退出按钮
        exit_btn = ttk.Button(button_frame, text="🚪 退出", 
                             command=self.on_exit)
        exit_btn.pack(side=tk.RIGHT)
        
        # 选项区域
        options_frame = ttk.LabelFrame(main_frame, text="显示选项", padding="10")
        options_frame.pack(fill=tk.X)
        
        self.show_virtual_frame = tk.BooleanVar(value=True)
        self.show_hex_boxes = tk.BooleanVar(value=True)
        self.use_client_area = tk.BooleanVar(value=True)  # 新增：使用客户端区域
        
        ttk.Checkbutton(options_frame, text="显示虚拟16:9边框", 
                       variable=self.show_virtual_frame).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="显示海克斯区域框", 
                       variable=self.show_hex_boxes).pack(anchor=tk.W)
        ttk.Checkbutton(options_frame, text="使用游戏画面区域（而非整个窗口）", 
                       variable=self.use_client_area).pack(anchor=tk.W)
        
    def get_game_window_rect(self):
        """获取游戏窗口坐标（支持整个窗口或仅画面区域）"""
        try:
            window_title = "League of Legends (TM) Client"
            hwnd = win32gui.FindWindow(None, window_title)
            
            if hwnd:
                if self.use_client_area.get():
                    # 获取游戏画面区域（客户端区域）
                    return self.get_game_client_rect(hwnd)
                else:
                    # 获取整个窗口区域
                    left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                    
                    # 检查异常坐标（最小化状态）
                    if left < -10000 or top < -10000 or (right - left) < 100 or (bottom - top) < 100:
                        raise ValueError("窗口可能已最小化")
                    
                    width = right - left
                    height = bottom - top
                    return (left, top, width, height)
            else:
                # 如果没找到游戏窗口，使用主屏幕
                screen_width = win32api.GetSystemMetrics(0)
                screen_height = win32api.GetSystemMetrics(1)
                return (0, 0, screen_width, screen_height)
                
        except Exception as e:
            print(f"获取游戏窗口时出错: {e}")
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)
            return (0, 0, screen_width, screen_height)
    
    def get_game_client_rect(self, hwnd):
        """获取游戏画面的实际区域（客户端区域）"""
        try:
            # 获取客户端区域大小
            client_rect = win32gui.GetClientRect(hwnd)
            client_width = client_rect[2] - client_rect[0]
            client_height = client_rect[3] - client_rect[1]
            
            # 获取客户端区域在屏幕上的位置
            # ClientToScreen将客户端坐标转换为屏幕坐标
            client_left, client_top = win32gui.ClientToScreen(hwnd, (0, 0))
            
            # 检查异常坐标
            if client_left < -10000 or client_top < -10000 or client_width < 100 or client_height < 100:
                raise ValueError("客户端区域异常")
            
            return (client_left, client_top, client_width, client_height)
            
        except Exception as e:
            print(f"获取客户端区域失败: {e}")
            # 降级到整个窗口
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top
            return (left, top, width, height)
    
    def create_virtual_16_9_area(self, game_rect):
        """创建虚拟16:9区域（修正版：统一以高度为基准）"""
        game_x, game_y, game_width, game_height = game_rect
        
        # 计算中心点
        center_x = game_x + game_width / 2
        center_y = game_y + game_height / 2
        
        # 修正：统一以游戏窗口高度为基准构造16:9虚拟框
        # 虚拟框高度 = 游戏窗口高度
        virtual_height = game_height
        # 虚拟框宽度 = 高度 * (16/9)
        virtual_width = virtual_height * (16 / 9)
        
        # 虚拟框在游戏窗口中水平居中，垂直对齐
        virtual_x = center_x - virtual_width / 2
        virtual_y = game_y  # 顶部对齐，因为高度完全相同
        
        return (virtual_x, virtual_y, virtual_width, virtual_height)
    
    def convert_relative_to_absolute(self, relative_coords, virtual_rect):
        """将相对坐标转换为绝对坐标（基于虚拟16:9区域）"""
        virtual_x, virtual_y, virtual_width, virtual_height = virtual_rect
        x1_ratio, y1_ratio, x2_ratio, y2_ratio = relative_coords
        
        abs_x1 = int(virtual_x + virtual_width * x1_ratio)
        abs_y1 = int(virtual_y + virtual_height * y1_ratio)
        abs_x2 = int(virtual_x + virtual_width * x2_ratio)
        abs_y2 = int(virtual_y + virtual_height * y2_ratio)
        
        return (abs_x1, abs_y1, abs_x2, abs_y2)
    
    def create_test_box(self, x1, y1, x2, y2, color="red", width=3, label=""):
        """创建测试用的方框"""
        box = tk.Toplevel(self.root)
        box.overrideredirect(True)
        box.attributes('-topmost', True)
        box.attributes('-transparentcolor', 'white')
        box.config(bg='white')
        box.attributes('-disabled', 1)  # 让鼠标事件穿透
        
        # 计算尺寸
        box_width = x2 - x1
        box_height = y2 - y1
        
        # 创建带边框的Frame
        frame = tk.Frame(box, 
                        highlightbackground=color, 
                        highlightcolor=color, 
                        highlightthickness=width, 
                        bg='white')
        frame.pack(expand=True, fill=tk.BOTH)
        
        # 如果有标签，添加文字
        if label:
            text_label = tk.Label(frame, text=label, 
                                 bg='white', fg=color, 
                                 font=("Arial", 10, "bold"))
            text_label.place(x=5, y=5)
        
        box.geometry(f"{box_width}x{box_height}+{x1}+{y1}")
        return box
    
    def update_info_display(self, game_rect, virtual_rect):
        """更新信息显示"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        game_x, game_y, game_width, game_height = game_rect
        virtual_x, virtual_y, virtual_width, virtual_height = virtual_rect
        
        current_ratio = game_width / game_height
        virtual_ratio = virtual_width / virtual_height
        
        # 区域类型说明
        area_type = "🎯 游戏画面区域" if self.use_client_area.get() else "🪟 整个窗口区域"
        area_desc = "（去除标题栏和边框）" if self.use_client_area.get() else "（包含标题栏和边框）"
        
        info = f"""{area_type} {area_desc}：
位置: ({game_x}, {game_y})
尺寸: {game_width} × {game_height}
比例: {current_ratio:.3f} ({game_width}:{game_height})

📐 虚拟16:9区域：
位置: ({virtual_x:.0f}, {virtual_y:.0f})
尺寸: {virtual_width:.0f} × {virtual_height:.0f}
比例: {virtual_ratio:.3f} (目标: 1.778)

🔧 适配方案：
统一以游戏高度为基准，水平居中
虚拟框宽度 = 游戏高度 × (16/9) = {virtual_width:.0f}

💡 提示：
勾选"使用游戏画面区域"可获得更精确的坐标
适用于窗口化模式，去除标题栏影响

⏰ 更新时间: {time.strftime('%H:%M:%S')}
"""
        
        self.info_text.insert(1.0, info)
        self.info_text.config(state=tk.DISABLED)
    
    def capture_and_display(self):
        """捕获窗口并显示方框"""
        try:
            # 先隐藏之前的方框
            self.hide_all_boxes()
            
            # 获取游戏窗口坐标
            game_rect = self.get_game_window_rect()
            
            # 如果使用客户端区域，同时获取整个窗口区域用于对比
            window_rect = None
            if self.use_client_area.get():
                try:
                    window_title = "League of Legends (TM) Client"
                    hwnd = win32gui.FindWindow(None, window_title)
                    if hwnd:
                        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                        window_rect = (left, top, right - left, bottom - top)
                except:
                    pass
            
            # 创建虚拟16:9区域
            virtual_rect = self.create_virtual_16_9_area(game_rect)
            
            # 更新信息显示
            self.update_info_display(game_rect, virtual_rect)
            
            # 显示整个窗口边界（绿色框，仅在使用客户端区域时显示）
            if self.use_client_area.get() and window_rect:
                wx, wy, ww, wh = window_rect
                window_box = self.create_test_box(
                    wx, wy, wx + ww, wy + wh,
                    color="green", width=3, label="整个窗口边界"
                )
                self.test_boxes.append(window_box)
            
            # 显示虚拟边框
            if self.show_virtual_frame.get():
                vx, vy, vw, vh = virtual_rect
                self.virtual_frame_box = self.create_test_box(
                    int(vx), int(vy), int(vx + vw), int(vy + vh),
                    color="blue", width=4, label="虚拟16:9边框"
                )
            
            # 显示海克斯区域框
            if self.show_hex_boxes.get():
                # 定义所有需要测试的区域、标签和颜色
                test_regions = {
                    "蓝色按钮": {
                        "coords": (1163/2560, 1301/1440, 1398/2560, 1347/1440),
                        "color": "cyan",
                        "label": "蓝色按钮"
                    },
                    "刷新按钮1": {
                        "coords": (668/2560, 1117/1440, 807/2560, 1186/1440),
                        "color": "orange",
                        "label": "刷新1"
                    },
                    "刷新按钮2": {
                        "coords": (1211/2560, 1117/1440, 1350/2560, 1186/1440),
                        "color": "orange",
                        "label": "刷新2"
                    },
                    "刷新按钮3": {
                        "coords": (1753/2560, 1117/1440, 1892/2560, 1186/1440),
                        "color": "orange",
                        "label": "刷新3"
                    },
                    "果实位置": {
                        "coords": (900/2560, 1110/1440, 1540/2560, 1190/1440),
                        "color": "magenta",
                        "label": "果实"
                    },
                    "装备位置": {
                        "coords": (955/2560, 1110/1440, 1383/2560, 1180/1440),
                        "color": "yellow",
                        "label": "装备"
                    }
                }

                for key, region_info in test_regions.items():
                    abs_coords = self.convert_relative_to_absolute(region_info["coords"], virtual_rect)
                    x1, y1, x2, y2 = abs_coords
                    
                    box = self.create_test_box(x1, y1, x2, y2,
                                             color=region_info["color"],
                                             width=2,
                                             label=region_info["label"])
                    self.test_boxes.append(box)
            
            # 成功提示信息增强
            area_type = "游戏画面区域" if self.use_client_area.get() else "整个窗口"
            messagebox.showinfo("完成", f"已显示坐标框！\n{area_type}: {game_rect[2]}×{game_rect[3]}")
            
        except Exception as e:
            messagebox.showerror("错误", f"捕获窗口时出错：{e}")
    
    def hide_all_boxes(self):
        """隐藏所有测试方框"""
        for box in self.test_boxes:
            try:
                box.destroy()
            except:
                pass
        self.test_boxes.clear()
        
        if self.virtual_frame_box:
            try:
                self.virtual_frame_box.destroy()
            except:
                pass
            self.virtual_frame_box = None
    
    def on_exit(self):
        """退出程序"""
        self.hide_all_boxes()
        self.root.destroy()
    
    def run(self):
        """运行测试工具"""
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_exit)
        
        # 显示使用说明
        messagebox.showinfo("使用说明", 
                           "🎮 坐标系统测试工具\n\n"
                           "1. 启动云顶之弈游戏\n"
                           "2. 选择检测模式：\n"
                           "   ✅ 游戏画面区域：仅游戏内容（推荐）\n"
                           "   ⬜ 整个窗口：包含标题栏和边框\n"
                           "3. 点击'捕获游戏窗口'按钮\n"
                           "4. 观察显示的方框位置\n"
                           "5. 可以切换游戏分辨率后重新捕获测试\n\n"
                           "📝 方框说明：\n"
                           "🟦 蓝色框：虚拟16:9边框\n"
                           "🟥 红色框：海克斯识别区域\n"
                           "�� 绿色框：整个窗口边界（对比用）")
        
        self.root.mainloop()

if __name__ == "__main__":
    # 检查依赖
    try:
        import win32gui
        import win32api
    except ImportError:
        print("错误：缺少 pywin32 库")
        print("请运行：pip install pywin32")
        sys.exit(1)
    
    # 启动测试工具
    tool = CoordinateTestTool()
    tool.run() 