#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软件更新系统完整测试脚本
包含上传和下载的完整流程测试
"""

import os
import json
import requests
from qiniu import Auth, put_file_v2

class UpdateSystemTester:
    def __init__(self):
        # 从环境变量获取七牛云密钥
        self.access_key = os.getenv('QINIU_ACCESS_KEY')
        self.secret_key = os.getenv('QINIU_SECRET_KEY')
        self.bucket_name = 'yimiaojue'
        
        # 配置服务器地址
        self.config_server_url = 'http://updater.yuxianglei.com/yimiaojue_config.json'
        
        # 检查环境变量
        if not self.access_key or not self.secret_key:
            raise ValueError("请先设置环境变量 QINIU_ACCESS_KEY 和 QINIU_SECRET_KEY")
    
    def upload_test_file(self, content, key):
        """上传测试文件到七牛云"""
        print(f"\n=== 开始上传测试 ===")
        print(f"目标Key: {key}")
        
        # 创建本地临时文件
        local_file = 'temp_upload_test.txt'
        try:
            with open(local_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 创建本地测试文件: {local_file}")
            
            # 构建鉴权对象并上传
            q = Auth(self.access_key, self.secret_key)
            token = q.upload_token(self.bucket_name, key, 3600)
            
            ret, info = put_file_v2(token, key, local_file)
            
            if info.status_code == 200:
                print(f"✓ 上传成功！")
                print(f"  Hash: {ret.get('hash')}")
                print(f"  Key: {ret.get('key')}")
                return True
            else:
                print(f"✗ 上传失败: {info.text_body}")
                return False
                
        except Exception as e:
            print(f"✗ 上传异常: {e}")
            return False
        finally:
            # 清理本地文件
            if os.path.exists(local_file):
                os.remove(local_file)
    
    def download_test_file(self, key, expected_content):
        """通过配置服务器下载并验证文件"""
        print(f"\n=== 开始下载测试 ===")
        print(f"目标Key: {key}")
        
        try:
            # 步骤1: 获取配置
            print("1. 获取配置文件...")
            response = requests.get(self.config_server_url, timeout=10)
            response.raise_for_status()
            config_data = response.json()
            print("✓ 配置获取成功")
            
            # 步骤2: 解析下载地址
            print("2. 解析下载地址...")
            download_base_url = config_data.get('release', {}).get('download_base_url')
            if not download_base_url:
                raise ValueError("配置中未找到下载基地址")
            
            if not download_base_url.startswith('https://'):
                download_base_url = 'https://' + download_base_url
            
            print(f"✓ 下载基地址: {download_base_url}")
            
            # 步骤3: 下载文件
            print("3. 下载文件...")
            final_url = f"{download_base_url.rstrip('/')}/{key}"
            print(f"   完整URL: {final_url}")
            
            response = requests.get(final_url, timeout=30)
            response.raise_for_status()
            
            # 步骤4: 验证内容
            print("4. 验证文件内容...")
            # 确保正确处理UTF-8编码
            downloaded_content = response.content.decode('utf-8')
            
            if downloaded_content == expected_content:
                print("✓ 内容验证通过！")
                return True
            else:
                print("✗ 内容验证失败！")
                print(f"  预期: {expected_content}")
                print(f"  实际: {downloaded_content}")
                return False
                
        except Exception as e:
            print(f"✗ 下载测试失败: {e}")
            return False
    
    def test_version_info_flow(self):
        """测试版本信息流程"""
        print(f"\n=== 测试版本信息流程 ===")
        
        # 创建测试版本信息（使用英文避免编码问题）
        version_info = {
            "version": "1.0.0-test",
            "build_time": "2025-01-29T10:00:00Z",
            "changelog": [
                "Test version update feature",
                "Verify upload and download pipeline"
            ],
            "files": {
                "main_executable": "YimiaoJue.exe",
                "size": 1024000,
                "hash": "test_hash_value"
            }
        }
        
        version_content = json.dumps(version_info, ensure_ascii=True, indent=2)
        version_key = "test/version-test-new.json"
        
        # 测试上传版本信息
        if self.upload_test_file(version_content, version_key):
            # 对于JSON文件，使用特殊的验证方法
            return self.download_and_verify_json(version_key, version_info)
        
        return False
    
    def download_and_verify_json(self, key, expected_json):
        """下载并验证JSON文件内容"""
        print(f"\n=== 开始JSON下载测试 ===")
        print(f"目标Key: {key}")
        
        try:
            # 步骤1-3: 获取配置并下载（与普通文件相同）
            print("1. 获取配置文件...")
            response = requests.get(self.config_server_url, timeout=10)
            response.raise_for_status()
            config_data = response.json()
            print("✓ 配置获取成功")
            
            print("2. 解析下载地址...")
            download_base_url = config_data.get('release', {}).get('download_base_url')
            if not download_base_url:
                raise ValueError("配置中未找到下载基地址")
            
            if not download_base_url.startswith('https://'):
                download_base_url = 'https://' + download_base_url
            
            print(f"✓ 下载基地址: {download_base_url}")
            
            print("3. 下载JSON文件...")
            final_url = f"{download_base_url.rstrip('/')}/{key}"
            print(f"   完整URL: {final_url}")
            
            response = requests.get(final_url, timeout=30)
            response.raise_for_status()
            
            # 步骤4: 验证JSON内容
            print("4. 验证JSON内容...")
            downloaded_json = response.json()  # 直接解析为JSON对象
            
            if downloaded_json == expected_json:
                print("✓ JSON内容验证通过！")
                return True
            else:
                print("✗ JSON内容验证失败！")
                print(f"  预期: {expected_json}")
                print(f"  实际: {downloaded_json}")
                return False
                
        except Exception as e:
            print(f"✗ JSON下载测试失败: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整的测试流程"""
        print("=" * 60)
        print("软件更新系统完整测试")
        print("=" * 60)
        
        test_results = []
        
        # 测试1: 基础文件上传下载
        print("\n【测试1】基础文件上传下载")
        test_content = "你好，七牛云！这是软件更新系统的测试文件。"
        test_key = "test/system_test.txt"
        
        upload_success = self.upload_test_file(test_content, test_key)
        if upload_success:
            download_success = self.download_test_file(test_key, test_content)
            test_results.append(("基础文件测试", download_success))
        else:
            test_results.append(("基础文件测试", False))
        
        # 测试2: 版本信息流程
        print("\n【测试2】版本信息流程")
        version_success = self.test_version_info_flow()
        test_results.append(("版本信息测试", version_success))
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 所有测试通过！软件更新系统已就绪！")
        else:
            print("❌ 部分测试失败，请检查配置和网络连接。")
        print("=" * 60)
        
        return all_passed

if __name__ == "__main__":
    try:
        tester = UpdateSystemTester()
        tester.run_complete_test()
    except Exception as e:
        print(f"测试初始化失败: {e}")