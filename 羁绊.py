#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import asyncio
import json
import random
import time
import traceback
import shutil
from pathlib import Path
import aiohttp
from playwright.async_api import async_playwright, TimeoutError

class TraitScraper:
    """云顶之弈羁绊数据爬虫"""
    
    def __init__(self, base_dir=None, max_concurrent=3, min_delay=0.5, max_delay=1.0):
        # 基础目录设置
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.trait_icons_dir = os.path.join(self.base_dir, "羁绊图标")
        self.mapping_dir = os.path.join(self.base_dir, "映射文件")
        
        # 创建必要的目录
        os.makedirs(self.trait_icons_dir, exist_ok=True)
        os.makedirs(self.mapping_dir, exist_ok=True)
        
        # 爬虫设置
        self.max_concurrent = max_concurrent
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.traits_url = "https://www.metatft.com/traits"
        self.base_icon_url = "https://cdn.metatft.com/file/metatft/traits/base.png"
        
        # 数据存储
        self.trait_data = {}
        self.trait_mapping = {}
        
        # 加载现有的映射数据
        self._load_existing_mappings()
        
    def _load_existing_mappings(self):
        """加载现有的羁绊映射文件"""
        mapping_file = os.path.join(self.mapping_dir, "羁绊映射.json")
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.trait_mapping = json.load(f)
                    print(f"成功加载现有羁绊映射数据: {len(self.trait_mapping)} 条记录")
            except Exception as e:
                print(f"加载羁绊映射文件失败: {e}")
                self.trait_mapping = {}
    
    async def add_random_delay(self):
        """添加随机延迟，避免请求过快"""
        delay = random.uniform(self.min_delay, self.max_delay)
        await asyncio.sleep(delay)
    
    def _clear_trait_icons_dir(self):
        """
        清空羁绊图标目录 - 已弃用
        改为检查图标完整性，不删除现有图标
        """
        print("使用图标完整性检查替代清空图标目录...")
        os.makedirs(self.trait_icons_dir, exist_ok=True)
        return True
    
    async def verify_missing_trait_icons(self):
        """
        验证羁绊图标映射中的图片是否实际存在，只下载缺失的图片
        
        返回:
            list: 缺失图标的羁绊信息列表
        """
        print("验证羁绊图标完整性...")
        missing_icons = []
        
        # 确保羁绊图标目录存在
        os.makedirs(self.trait_icons_dir, exist_ok=True)
        
        # 检查基础图标是否存在
        base_icon_path = os.path.join(self.trait_icons_dir, "base.png")
        if not os.path.exists(base_icon_path):
            print("羁绊基础图标缺失，需要下载")
            missing_icons.append({"type": "base", "path": base_icon_path, "url": self.base_icon_url})
        
        # 如果映射文件为空，则无法检查
        if not self.trait_mapping:
            return missing_icons
        
        # 检查每个羁绊的图标
        for trait_name, trait_info in self.trait_mapping.items():
            if "icon_path" in trait_info:
                # 获取图标的绝对路径
                icon_path = trait_info["icon_path"]
                absolute_path = os.path.abspath(os.path.join(self.base_dir, icon_path))
                
                # 检查文件是否存在
                if not os.path.exists(absolute_path) or not os.path.isfile(absolute_path):
                    print(f"发现缺失的羁绊图标: {trait_name}")
                    # 如果有URL，添加到缺失列表
                    if "icon_url" in trait_info:
                        missing_icons.append({
                            "type": "trait",
                            "name": trait_name,
                            "path": absolute_path,
                            "url": trait_info["icon_url"]
                        })
        
        print(f"验证完成，发现 {len(missing_icons)} 个缺失的羁绊图标")
        return missing_icons
    
    def _extract_trait_name_from_href(self, href):
        """从href中提取羁绊的英文名称"""
        if not href:
            return None
            
        # 从路径中提取英文名
        # 如 /traits/Academy 提取 Academy
        parts = href.split('/')
        if len(parts) > 0:
            return parts[-1]
        return None
    
    async def _download_image(self, url, file_path, max_retries=2):
        """下载图片并保存到指定路径"""
        for attempt in range(max_retries + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.read()
                            os.makedirs(os.path.dirname(file_path), exist_ok=True)
                            with open(file_path, 'wb') as f:
                                f.write(content)
                            return True
                        else:
                            print(f"下载图片失败，状态码: {response.status}, URL: {url}")
                            if attempt < max_retries:
                                await asyncio.sleep(1)
                            continue
            except Exception as e:
                print(f"下载图片异常: {e}, URL: {url}")
                if attempt < max_retries:
                    await asyncio.sleep(1)
                continue
        return False
    
    async def _download_base_icon(self):
        """下载羁绊基础图标"""
        base_icon_path = os.path.join(self.trait_icons_dir, "base.png")
        return await self._download_image(self.base_icon_url, base_icon_path)
    
    async def scrape_traits(self):
        """爬取羁绊数据主函数"""
        print("开始爬取羁绊数据...")
        
        # 确保图标目录存在并检查缺失图标
        self._clear_trait_icons_dir() # 这个函数已经被修改为只创建目录，不清空
        
        async with async_playwright() as playwright:
            # 配置浏览器选项
            browser_args = [
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-accelerated-2d-canvas",
                "--disable-infobars",
                "--disable-extensions",
                "--block-new-web-contents",
                "--no-default-browser-check",
                "--js-flags=--max-old-space-size=512",
                f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
            ]
            
            try:
                # 启动浏览器
                browser = await playwright.chromium.launch(
                    headless=True,
                    args=browser_args,
                    handle_sigint=True,
                    handle_sigterm=True,
                    handle_sighup=True,
                    slow_mo=20  # 降低slowmo值，提高速度
                )
                print("浏览器启动成功")
                
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    locale='zh-CN',
                    timezone_id='Asia/Shanghai',
                    bypass_csp=True,
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
                )
                
                page = await context.new_page()
                
                # 设置超时和加载策略
                page.set_default_navigation_timeout(90000)  # 降低等待时间
                page.set_default_timeout(45000)  # 降低等待时间
                
                try:
                    # 访问羁绊页面
                    print(f"正在访问网页: {self.traits_url}")
                    
                    try:
                        # 先使用domcontentloaded策略
                        response = await page.goto(
                            self.traits_url, 
                            wait_until="domcontentloaded",
                            timeout=45000  # 降低等待时间
                        )
                        
                        if not response:
                            print("警告: 页面导航未返回响应对象")
                        elif not response.ok:
                            print(f"警告: 页面响应状态码非成功: {response.status}")
                        else:
                            print(f"页面导航成功，状态码: {response.status}")
                            
                    except TimeoutError as e:
                        print(f"页面导航超时: {e}")
                        print(f"错误详情: {traceback.format_exc()}")
                        
                    except Exception as e:
                        print(f"页面导航发生异常: {e}")
                        print(f"错误详情: {traceback.format_exc()}")
                    
                    # 等待基本内容加载
                    print("等待页面基本内容加载...")
                    try:
                        await page.wait_for_load_state("domcontentloaded", timeout=20000)  # 降低等待时间
                        print("DOM内容已加载")
                    except Exception as e:
                        print(f"等待DOM内容加载失败: {e}")
                    
                    # 增加额外等待时间让页面充分渲染
                    print("等待JavaScript渲染...")
                    await asyncio.sleep(2)  # 减少等待时间
                    
                    # 设置中文语言
                    print("尝试将语言设置为中文...")
                    try:
                        # 使用JavaScript设置localStorage中的language值为zh_cn
                        await page.evaluate("localStorage.setItem('language', 'zh_cn');")
                        print("已设置localStorage中的language为zh_cn")
                        
                        # 刷新页面以应用新的语言设置
                        await page.reload(wait_until="domcontentloaded", timeout=20000)  # 降低等待时间
                        print("已刷新页面以应用语言设置")
                        
                        # 验证语言是否已切换
                        current_language = await page.evaluate("localStorage.getItem('language');")
                        print(f"当前语言设置: {current_language}")
                        
                        # 再次等待以确保页面渲染完成
                        await asyncio.sleep(2)  # 减少等待时间
                    except Exception as e:
                        print(f"设置中文语言时出错: {e}")
                        print(traceback.format_exc())
                        
                    # 并行下载图片的函数
                    async def process_trait_row(idx, row):
                        try:
                            # 按照用户提供的选择器获取羁绊信息
                            name_link_selector = "td:nth-child(1) > div > a"
                            name_link = await row.query_selector(name_link_selector)
                            
                            if not name_link:
                                print(f"行 {idx} 没有找到羁绊链接，跳过")
                                return None
                            
                            # 获取href，提取英文名
                            href = await name_link.get_attribute("href")
                            trait_en_name = self._extract_trait_name_from_href(href)
                            
                            # 删除英文名中的连字符，例如street-demon改为streetdemon
                            if trait_en_name:
                                trait_en_name = trait_en_name.replace("-", "")
                            
                            # 获取中文名称 - 修改获取方法
                            # 从a标签的文本内容中获取
                            trait_name = await name_link.text_content()
                            # 清理文本内容，去除前后空格和引号
                            trait_name = trait_name.strip().strip('"').strip("'")
                            
                            # 如果仍然没有获取到中文名，尝试从图片alt属性获取
                            if not trait_name:
                                # 尝试从图像的alt属性获取
                                img_selector = "div > div > div > img.TraitIcon"
                                img_element = await name_link.query_selector(img_selector)
                                if img_element:
                                    alt_text = await img_element.get_attribute("alt")
                                    if alt_text:
                                        trait_name = alt_text

                            # 如果依然没有中文名，使用英文名称代替
                            if not trait_name:
                                print(f"行 {idx} 无法获取羁绊中文名，使用英文名代替")
                                trait_name = trait_en_name
                            
                            # 检查中文名是否包含有效内容
                            if len(trait_name.strip()) <= 1:  # 如果只有一个字符或空白，认为无效
                                # 从图片的alt属性获取
                                alt_selector = "div div img.TraitIcon"
                                alt_img = await name_link.query_selector(alt_selector)
                                if alt_img:
                                    alt_text = await alt_img.get_attribute("alt")
                                    if alt_text and len(alt_text.strip()) > 1:
                                        trait_name = alt_text
                            
                            # 获取中心图标
                            center_icon_selector = "td:nth-child(1) > div > a > div > div > div > img"
                            center_icon = await row.query_selector(center_icon_selector)
                            
                            if not center_icon:
                                print(f"行 {idx} 没有找到羁绊中心图标，跳过")
                                return None
                            
                            center_icon_url = await center_icon.get_attribute("src")
                            
                            if not center_icon_url:
                                print(f"行 {idx} 羁绊中心图标没有URL，跳过")
                                return None
                            
                            # 下载中心图标
                            center_icon_file_name = f"{trait_name}_center.png"
                            center_icon_file_path = os.path.join(self.trait_icons_dir, center_icon_file_name)
                            
                            # 确保文件名合法
                            center_icon_file_path = self._sanitize_filename(center_icon_file_path)
                            
                            relative_center_path = os.path.relpath(center_icon_file_path, self.base_dir)
                            
                            # 下载中心图标
                            await self._download_image(center_icon_url, center_icon_file_path)
                            
                            # 获取羁绊等级信息
                            # 查找第5列，内含羁绊等级信息
                            level_cell_selector = "td:nth-child(5)"
                            level_cell = await row.query_selector(level_cell_selector)
                            
                            levels_data = []
                            
                            if level_cell:
                                try:
                                    # 获取单元格HTML内容进行分析
                                    html_content = await level_cell.inner_html()
                                    
                                    # 按顺序查找所有级别的出现
                                    level_types = []
                                    
                                    # 创建一个辅助函数，在HTML中查找特定类型的所有出现位置
                                    def find_all_occurrences(html, substr):
                                        positions = []
                                        pos = html.find(substr)
                                        while pos != -1:
                                            positions.append(pos)
                                            pos = html.find(substr, pos + 1)
                                        return positions
                                        
                                    # 查找所有等级类型出现的位置
                                    bronze_positions = find_all_occurrences(html_content, "CompTraitRowBar bronze")
                                    silver_positions = find_all_occurrences(html_content, "CompTraitRowBar silver")
                                    gold_positions = find_all_occurrences(html_content, "CompTraitRowBar gold")
                                    plat_positions = find_all_occurrences(html_content, "CompTraitRowBar plat")
                                    unique_positions = find_all_occurrences(html_content, "CompTraitRowBar unique")
                                    
                                    # 合并所有位置，带上类型信息
                                    all_positions = []
                                    for pos in bronze_positions:
                                        all_positions.append((pos, "bronze"))
                                    for pos in silver_positions:
                                        all_positions.append((pos, "silver"))
                                    for pos in gold_positions:
                                        all_positions.append((pos, "gold"))
                                    for pos in plat_positions:
                                        all_positions.append((pos, "plat"))
                                    for pos in unique_positions:
                                        all_positions.append((pos, "unique"))
                                    
                                    # 按位置排序，这样可以按照它们在HTML中出现的顺序处理
                                    all_positions.sort()
                                    
                                    # 按顺序添加等级，并尝试提取对应的数字
                                    for idx, (pos, level_type) in enumerate(all_positions, 1):
                                        # 尝试从当前位置向后查找匹配的TraitNumber和数字
                                        level_number = None
                                        try:
                                            # 在当前位置后搜索TraitNumber
                                            trait_num_start = html_content.find("TraitNumber", pos)
                                            # 如果找到了TraitNumber标签并且它在合理范围内（不超过下一个CompTraitRowBar出现的位置）
                                            if trait_num_start != -1:
                                                # 检查是否在合理范围内
                                                next_bar_pos = html_content.find("CompTraitRowBar", pos + 1)
                                                if next_bar_pos == -1 or trait_num_start < next_bar_pos:
                                                    # 找到TraitNumber后的>符号
                                                    gt_idx = html_content.find(">", trait_num_start)
                                                    if gt_idx != -1:
                                                        # 找到>后的<符号
                                                        lt_idx = html_content.find("<", gt_idx)
                                                        if lt_idx != -1:
                                                            # 提取数字文本
                                                            number_text = html_content[gt_idx+1:lt_idx].strip()
                                                            if number_text.isdigit():
                                                                level_number = int(number_text)
                                                                print(f"羁绊 {trait_name} 等级 {level_type} 提取到数字: {level_number}")
                                        except Exception as e:
                                            print(f"尝试提取 {trait_name} 等级 {level_type} 的数字时出错: {e}")
                                            
                                        # 添加到等级数据中
                                        levels_data.append({
                                            "level": idx,
                                            "level_name": level_type,
                                            "level_number": level_number
                                        })
                                        print(f"羁绊 {trait_name} 通过HTML分析添加等级 {level_type}")
                                    
                                    print(f"通过HTML分析找到 {len(levels_data)} 个等级")
                                
                                except Exception as e:
                                    print(f"获取羁绊 {trait_name} 的等级信息时出错: {e}")
                                    print(traceback.format_exc())
                            
                            # 创建羁绊数据
                            trait_data = {
                                "name": trait_name,
                                "en_name": trait_en_name,
                                "trait_id": trait_en_name,  # 添加trait_id，与用户需求一致
                                "center_icon_url": center_icon_url,
                                "base_icon_url": self.base_icon_url,
                                "center_icon_path": relative_center_path.replace("\\", "/"),
                                "base_icon_path": "羁绊图标/base.png",
                                "levels": levels_data  # 添加等级数据
                            }
                            
                            print(f"已处理羁绊 [{idx}]: {trait_name}, 英文名: {trait_en_name}, 等级数: {len(levels_data)}")
                            return (trait_name, trait_data)
                        
                        except Exception as e:
                            print(f"处理羁绊行 {idx} 时出错: {e}")
                            print(traceback.format_exc())
                            return None

                    # 查找羁绊表格行
                    print("查找羁绊表格行...")
                    
                    # 按照用户提供的选择器查找羁绊行
                    traits_row_selector = "#content-wrap > div.MetaTFTLayout > div.container > div:nth-child(2) > div > div.StatTableContainer > figure > table > tbody > tr"
                    
                    try:
                        trait_rows = await page.query_selector_all(traits_row_selector)
                        
                        if not trait_rows or len(trait_rows) == 0:
                            print(f"没有找到羁绊行，选择器: {traits_row_selector}")
                            # 尝试截图
                            await page.screenshot(path="traits_debug_screenshot.png", full_page=True)
                            print("已保存页面截图到 traits_debug_screenshot.png")
                            return 0
                        
                        print(f"找到 {len(trait_rows)} 个可能的羁绊行")
                        
                        # 创建任务列表，跳过表头行
                        tasks = []
                        for idx, row in enumerate(trait_rows):
                            if idx == 0:  # 跳过表头行
                                continue
                            tasks.append(process_trait_row(idx, row))
                        
                        # 并发执行所有任务，每次最多执行max_concurrent个
                        trait_count = 0
                        for i in range(0, len(tasks), self.max_concurrent):
                            batch = tasks[i:i+self.max_concurrent]
                            results = await asyncio.gather(*batch)
                            
                            # 处理结果
                            for result in results:
                                if result:
                                    trait_name, trait_data = result
                                    self.trait_data[trait_name] = trait_data
                                    trait_count += 1
                            
                            # 在批次之间添加短暂延迟，避免请求过快
                            if i + self.max_concurrent < len(tasks):
                                await asyncio.sleep(0.2)
                        
                        print(f"共找到并处理了 {trait_count} 个羁绊")
                        
                        # 保存映射文件
                        await self.save_mapping()
                        
                        # 检查并下载缺失的图标
                        print("检查羁绊图标完整性...")
                        missing_icons = await self.verify_missing_trait_icons()
                        if missing_icons:
                            print(f"发现 {len(missing_icons)} 个缺失的羁绊图标，开始下载...")
                            for icon_info in missing_icons:
                                if icon_info["type"] == "base":
                                    # 下载基础图标
                                    await self._download_image(icon_info["url"], icon_info["path"])
                                else:
                                    # 下载羁绊图标
                                    await self._download_image(icon_info["url"], icon_info["path"])
                                # 添加随机延迟
                                await self.add_random_delay()
                            print("羁绊图标补全完成")
                        else:
                            print("所有羁绊图标已存在，无需下载")
                        
                    except Exception as e:
                        print(f"处理羁绊数据时出错: {e}")
                        print(traceback.format_exc())
                        
                        # 保存页面以便调试
                        try:
                            content = await page.content()
                            with open("traits_error_page.html", "w", encoding="utf-8") as f:
                                f.write(content)
                            print("已保存错误页面到 traits_error_page.html")
                            
                            # 尝试截图
                            await page.screenshot(path="traits_error_screenshot.png", full_page=True)
                            print("已保存错误页面截图到 traits_error_screenshot.png")
                        except Exception as screenshot_error:
                            print(f"保存调试信息失败: {screenshot_error}")
                
                except Exception as e:
                    print(f"爬取羁绊数据时发生错误: {e}")
                    print(traceback.format_exc())
                
                finally:
                    # 自动关闭浏览器
                    try:
                        await browser.close()
                        print("浏览器已关闭")
                    except Exception as close_error:
                        print(f"关闭浏览器时出错: {close_error}")
            
            except Exception as e:
                print(f"启动浏览器过程中出错: {e}")
                print(traceback.format_exc())
                try:
                    await browser.close()
                except:
                    pass
        
        return len(self.trait_data)
    
    def _sanitize_filename(self, file_path):
        """确保文件名合法"""
        dir_name = os.path.dirname(file_path)
        base_name = os.path.basename(file_path)
        # 替换Windows文件名中不允许的字符
        for char in ['\\', '/', ':', '*', '?', '"', '<', '>', '|']:
            base_name = base_name.replace(char, '_')
        return os.path.join(dir_name, base_name)
    
    def convert_to_traits_format(self, trait_name, level_index=None):
        """将羁绊数据转换为目标格式 [{"trait_id": "controller", "level": 1}, ...]
        
        Args:
            trait_name: 羁绊名称
            level_index: 羁绊等级索引，如果不指定则取最高等级
            
        Returns:
            dict: 格式化后的羁绊数据，如 {"trait_id": "controller", "level": 1}
        """
        if trait_name not in self.trait_data:
            return None
            
        trait_info = self.trait_data[trait_name]
        if "trait_id" not in trait_info or "levels" not in trait_info or not trait_info["levels"]:
            return None
            
        # 获取羁绊ID
        trait_id = trait_info["trait_id"]
        
        # 如果未指定等级，则使用最高等级
        if level_index is None:
            level_index = len(trait_info["levels"])
        
        # 确保等级索引在有效范围内
        if level_index < 1 or level_index > len(trait_info["levels"]):
            level_index = len(trait_info["levels"])
            
        # 返回格式化的羁绊数据
        return {
            "trait_id": trait_id,
            "level": level_index
        }
    
    def get_traits_list(self, trait_levels_dict):
        """将多个羁绊及其对应等级转换为目标格式列表
        
        Args:
            trait_levels_dict: 字典，键为羁绊名称，值为等级索引，如果值为None则使用最高等级
            
        Returns:
            list: 格式化后的羁绊数据列表，如 [{"trait_id": "controller", "level": 1}, ...]
        """
        result = []
        for trait_name, level_index in trait_levels_dict.items():
            trait_data = self.convert_to_traits_format(trait_name, level_index)
            if trait_data:
                result.append(trait_data)
        return result
    
    async def save_mapping(self):
        """保存羁绊映射数据到JSON文件"""
        try:
            mapping_file = os.path.join(self.mapping_dir, "羁绊映射.json")
            
            # 保存更新后的数据
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.trait_data, f, ensure_ascii=False, indent=2)
            
            print(f"已保存羁绊映射数据到: {mapping_file}")
            return True
        except Exception as e:
            print(f"保存羁绊映射数据时出错: {e}")
            return False

async def run_trait_scraper(base_dir=None, max_concurrent=5, min_delay=0.5, max_delay=1.0):
    """运行羁绊爬虫的入口函数"""
    max_retries = 3
    
    for retry in range(max_retries):
        try:
            print(f"爬取尝试 {retry+1}/{max_retries}")
            scraper = TraitScraper(
                base_dir=base_dir,
                max_concurrent=max_concurrent,  # 增加并发数
                min_delay=min_delay,  # 减少延迟
                max_delay=max_delay   # 减少延迟
            )
            
            count = await scraper.scrape_traits()
            print(f"爬取完成，共获取 {count} 个羁绊数据")
            
            if count > 0:
                return True
            else:
                print(f"未获取到羁绊数据，将在3秒后重试...")  # 减少等待时间
                await asyncio.sleep(3)  # 减少等待时间
        except Exception as e:
            print(f"运行羁绊爬虫时出错: {e}")
            print(traceback.format_exc())
            if retry < max_retries - 1:
                print(f"将在3秒后重试...")  # 减少等待时间
                await asyncio.sleep(3)  # 减少等待时间
    
    print(f"已尝试 {max_retries} 次，爬取失败")
    return False

# 添加同步版本的入口函数，方便外部导入使用
def get_trait_data(base_dir=None, max_concurrent=5, min_delay=0.5, max_delay=1.0, force_update=False):
    """获取羁绊数据的同步函数，返回羁绊映射数据
    
    Args:
        base_dir: 基础目录
        max_concurrent: 最大并发数
        min_delay: 最小延迟
        max_delay: 最大延迟
        force_update: 是否强制更新数据（删除映射文件重新爬取，但保留图标）
        
    Returns:
        tuple: (是否成功, 羁绊映射数据)
    """
    try:
        # 确定基础目录
        current_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        mapping_file = os.path.join(current_dir, "映射文件", "羁绊映射.json")
        
        # 如果强制更新，则删除映射文件
        if force_update and os.path.exists(mapping_file):
            try:
                os.remove(mapping_file)
                print("强制更新模式：已删除旧的羁绊映射文件")
            except Exception as e:
                print(f"删除旧的羁绊映射文件时出错: {e}")
        
        # 如果文件已经存在且不是强制更新，直接加载
        if not force_update and os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    trait_data = json.load(f)
                print(f"从本地文件加载了 {len(trait_data)} 条羁绊数据")
                return True, trait_data
            except Exception as e:
                print(f"读取本地羁绊数据失败，将重新获取: {e}")
        
        # 使用asyncio运行异步函数
        success = asyncio.run(run_trait_scraper(
            base_dir=base_dir,
            max_concurrent=max_concurrent,
            min_delay=min_delay,
            max_delay=max_delay
        ))
        
        # 如果成功获取，读取新生成的文件
        if success and os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                trait_data = json.load(f)
            return True, trait_data
        else:
            return False, {}
    
    except Exception as e:
        print(f"获取羁绊数据时出错: {e}")
        return False, {}

# 如果直接运行此脚本
if __name__ == "__main__":
    asyncio.run(run_trait_scraper()) 