# 需求文档

## 介绍

本规范概述了TFT（云顶之弈）助手应用程序的优化需求。该系统目前通过感知哈希监控提供基于OCR的装备和海克斯强化识别，并支持自动触发。优化重点是改进窗口管理、时序机制、日志系统和更新流程，以提高性能、灵活性和可维护性。

## 需求

### 需求1：装备评分窗口管理

**用户故事：** 作为用户，我希望装备评分窗口在不再需要时能够正确消失，以便界面保持整洁且不干扰游戏体验。

#### 验收标准

1. 当不再检测到装备时，系统应立即隐藏所有装备评分窗口
2. 当装备OCR停止运行时，系统应在200毫秒内清除所有装备覆盖窗口
3. 当装备数量从非零变为零时，系统应自动触发窗口清理
4. 如果装备窗口无法消失，系统应在下一个OCR周期强制清理
5. 当装备管理器被销毁时，系统应确保所有窗口都被正确释放

### 需求2：动态OCR时序系统

**用户故事：** 作为用户，我希望OCR系统仅在需要时运行，并在检测到内容时自动延长，以便节约系统资源同时保持响应性。

#### 验收标准

1. 当感知哈希触发OCR时，系统应以5秒超时开始
2. 如果在5秒内检测到有效结果，系统应将超时延长另外5秒
3. 当连续5秒未检测到有效输出时，系统应自动停止OCR
4. 当OCR产生有效结果时，系统应重置5秒延长计时器
5. 如果OCR被手动触发，系统应使用10秒初始超时而不是5秒
6. 当超时到期且无结果时，系统应记录停止原因并清理资源

### 需求3：优化的日志系统

**用户故事：** 作为开发者和用户，我希望为不同的系统组件提供独立、专注的日志记录，以便我能够轻松诊断问题和监控系统行为。

#### 验收标准

1. 当应用程序启动时，yimiaojue.log应记录所有工具路径和配置信息
2. 当OCR操作开始时，ocr_debug.log应记录详细的OCR处理信息
3. 当应用程序初始化时，系统应记录所有加载的模块路径和版本到yimiaojue.log
4. 当配置文件被加载时，系统应记录文件路径和关键设置到yimiaojue.log
5. 当OCR引擎启动时，ocr_debug.log应记录引擎初始化和处理详情
6. 如果日志记录失败，系统应继续运行而不崩溃
7. 当日志文件超过大小限制时，系统应自动轮转日志
8. 当启用调试模式时，应将额外的诊断信息记录到相应文件

### 需求4：解耦的更新系统

**用户故事：** 作为用户，我希望更新器和主应用程序是可以独立更新的独立可执行文件，以便更新更可靠和灵活。

#### 验收标准

1. 当系统打包时，updater.exe和主应用程序应是独立的可执行文件
2. 当需要更新器发布时，它们应能够独立于主应用程序部署
3. 当主应用程序频繁更新时，更新器应保持稳定和兼容
4. 当文件夹结构发生变化时，更新器应自动适应
5. 当添加新的子程序时，更新器应无需修改即可处理它们
6. 如果更新包结构发生变化，更新器应检测并适应新格式
7. 当应用更新时，系统应处理目录和单文件版本

### 需求5：增强的更新流程

**用户故事：** 作为用户，我希望有一个更可靠的更新流程，具有手动重启控制，以便更新成功完成而不出现自动重启问题。

#### 验收标准

1. 当更新下载完成时，系统应显示手动重启按钮而不是自动重启
2. 当用户点击重启时，系统应在启动新版本前执行旧版本清理
3. 当backup_old_version清理失败时，系统应在下次重启时重试清理
4. 当更新过程遇到错误时，系统应提供清晰的错误消息和恢复选项
5. 当应用更新时，系统应在继续前验证文件完整性
6. 如果更新失败，系统应自动从备份恢复
7. 当启动重启时，系统应确保在继续前释放所有文件锁
8. 当旧版本清理发生时，系统应处理基于目录和文件的先前版本

### 需求6：改进的更新兼容性

**用户故事：** 作为开发者，我希望更新器能够处理各种部署场景和未来变化，以便更新系统在应用程序发展时保持健壮。

#### 验收标准

1. 当文件夹格式发生变化时，更新器应自动检测并处理新结构
2. 当添加新的可执行文件时，更新器应将它们包含在更新过程中
3. 当依赖文件发生变化时，更新器应更新所有必需的组件
4. 如果更新包包含新的文件类型，更新器应正确处理它们
5. 当更新过程遇到未知文件结构时，系统应记录警告并安全继续
6. 当出现兼容性问题时，更新器应提供回退机制
7. 当未来应用程序发生变化时，更新器应至少保持2个版本的向后兼容性

### 需求8：版本回档和历史管理

**用户故事：** 作为用户和开发者，我希望系统能够保留历史版本并支持回档，以便在新版本出现问题时能够快速恢复。

#### 验收标准

1. 当发布新版本时，系统应在服务器上保留最近2个版本的完整安装包
2. 当Beta版本发布时，系统应独立保留最近2个Beta版本
3. 当正式版本发布时，系统应独立保留最近2个正式版本
4. 当用户需要回档时，更新器应能够下载并安装指定的历史版本
5. 当版本回档发生时，系统应正确处理版本号降级和兼容性
6. 当打包上传工具运行时，应自动管理版本历史并清理过期版本
7. 当版本信息更新时，应同时更新Beta和正式版本的历史记录

### 需求7：资源管理和性能优化

**用户故事：** 作为用户，我希望系统高效使用资源并快速响应，以便不影响游戏性能或系统稳定性。

#### 验收标准

1. 当哈希监控检测到无变化时，应跳过OCR处理以节省资源
2. 当OCR操作完成时，应立即释放临时资源和清理不必要的线程
3. 当应用程序空闲时，后台监控应使用最少的资源
4. 当不主动需要OCR时，系统应最小化CPU和内存使用
5. 当图像哈希缓存命中时，应避免重复的OCR计算
6. 当窗口坐标缓存有效时，应避免重复的窗口坐标获取调用