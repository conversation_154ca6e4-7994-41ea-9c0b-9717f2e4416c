import win32gui
import time
import sys

def get_game_window_rect():
    """
    查找并返回云顶之弈游戏窗口的矩形信息 (位置和大小)
    """
    # 窗口标题，根据PowerShell的输出，这个是游戏进程的主窗口
    # FindWindow函数的第二个参数是窗口标题
    window_title = "League of Legends (TM) Client"
    
    # 查找指定标题的窗口句柄 (handle)
    # 句柄是Windows系统中每个窗口的唯一标识符
    hwnd = win32gui.FindWindow(None, window_title)
    
    # 如果hwnd不是0，说明找到了窗口
    if hwnd:
        print(f"成功！已找到游戏窗口。")
        print(f"  - 窗口标题: '{window_title}'")
        print(f"  - 窗口句柄: {hwnd}")
        
        try:
            # 获取窗口的屏幕坐标 (left, top, right, bottom)
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            
            # 计算宽度和高度
            width = right - left
            height = bottom - top
            
            print(f"  - 窗口位置 (左上角坐标): ({left}, {top})")
            print(f"  - 窗口尺寸 (宽x高): {width} x {height}")

            if width <= 0 or height <= 0:
                print("\n警告：获取到的窗口尺寸异常，可能是窗口被最小化了。")

            return (left, top, right, bottom)
        except Exception as e:
            print(f"错误：获取窗口位置或尺寸时发生异常: {e}")
            return None
    else:
        # 如果没找到窗口
        print(f"\n失败：未找到标题为 '{window_title}' 的窗口。")
        print("请检查：")
        print("  1. 云顶之弈游戏是否已经启动（不是只开着客户端）。")
        print("  2. 游戏设置是否为【无边框】或【窗口】模式，【全屏独占】模式可能无法获取。")
        return None

if __name__ == "__main__":
    print("--- 游戏窗口分辨率捕获测试 ---")
    
    # 运行前先检查pywin32是否安装
    try:
        import win32gui
    except ImportError:
        print("\n[错误] 缺少必要的库 'pywin32'。")
        print("请先在命令行中运行: pip install pywin32")
        input("\n按回车键退出...")
        sys.exit(1)

    print("\n测试即将开始，请在10秒内确保云顶之弈游戏正在运行...")
    
    # 留出时间让用户切换窗口
    for i in range(10, 0, -1):
        print(f"{i}...", end=' ', flush=True)
        time.sleep(1)
    
    print("\n\n开始捕获...")
    get_game_window_rect()
    
    # 防止脚本执行完后窗口直接关闭，方便用户查看结果
    print("\n------------------------------")
    input("测试完成，按回车键退出。") 