<template>
  <Primitive
    :as="as"
    :as-child="asChild"
    :class="cn(buttonVariants({ variant, size }), props.class)"
    v-bind="$attrs"
  >
    <slot />
  </Primitive>
</template>

<script setup lang="ts">
import { Primitive } from 'radix-vue'
import { type VariantProps, cva } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-control-bar-bg text-text-light hover:bg-item-row-hover',
        destructive: 'bg-red-500 text-white hover:bg-red-600',
        outline: 'border border-border-color bg-transparent hover:bg-item-row-hover hover:text-text-light',
        secondary: 'bg-section-bg text-text-light hover:bg-item-row-hover',
        ghost: 'hover:bg-item-row-hover hover:text-text-light',
        link: 'text-text-highlight underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)

export interface ButtonProps {
  variant?: VariantProps<typeof buttonVariants>['variant']
  size?: VariantProps<typeof buttonVariants>['size']
  as?: string
  asChild?: boolean
  class?: string
}

const props = withDefaults(defineProps<ButtonProps>(), {
  as: 'button',
})
</script>
