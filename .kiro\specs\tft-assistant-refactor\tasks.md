# 实施计划

- [x] 1. 项目初始化和基础配置






  - 在项目根目录创建TFT常驻助手文件夹
  - 在TFT常驻助手文件夹中创建Tauri项目结构
  - 配置Vue 3 + TypeScript + Tailwind CSS开发环境
  - 设置Vite构建配置和开发服务器
  - _需求: 1.1, 1.2, 1.4_

- [x] 2. 数据库操作模块迁移





  - [x] 2.1 创建Rust数据库操作模块




    - 实现SQLite连接和查询功能
    - 创建异步查询命令接口
    - 实现查询缓存机制
    - _需求: 1.3, 5.1, 5.2, 5.3_


  - [ ] 2.2 实现核心数据查询命令



    - 创建阵容列表查询命令
    - 创建英雄列表查询命令  
    - 创建装备列表查询命令
    - 创建海克斯列表查询命令
    - _需求: 4.1, 4.2, 4.3, 4.4_

  - [x] 2.3 实现详情数据查询命令

    - 创建阵容详情查询命令
    - 创建英雄详情查询命令
    - 创建装备详情查询命令
    - _需求: 4.5, 4.6_

- [x] 3. 窗口管理模块实现




  - [x] 3.1 配置Tauri窗口属性


    - 设置无边框窗口配置
    - 配置窗口默认尺寸和最小尺寸
    - 设置窗口置顶属性
    - _需求: 2.1_

  - [x] 3.2 实现窗口控制命令


    - 创建窗口展开/收起切换命令
    - 实现窗口拖拽支持
    - 创建窗口位置管理命令
    - _需求: 2.2, 2.3, 2.6_

- [x] 4. 状态管理和数据缓存





  - [x] 4.1 创建主应用状态管理


    - 实现页面状态管理 (currentPage, isMinimized)
    - 实现导航历史记录管理 (historyStack)
    - 实现视图加载状态跟踪 (viewsLoaded)
    - _需求: 3.3, 3.4, 3.5_

  - [x] 4.2 实现数据缓存状态管理


    - 创建全局数据缓存 (hero/trait/item info maps)
    - 实现查询结果缓存机制
    - 创建数据预加载逻辑
    - _需求: 5.1, 5.2, 5.3_

- [ ] 5. 核心UI组件开发





  - [x] 5.1 实现控制条组件


    - 创建返回按钮 (动态宽度显示/隐藏)
    - 实现展开/收起指示器组件
    - 创建关闭按钮
    - 实现拖拽区域处理
    - _需求: 2.2, 2.3, 2.4, 2.5_

  - [x] 5.2 实现导航栏组件


    - 创建四个导航按钮 (阵容/英雄/装备/海克斯)
    - 实现导航按钮选中状态管理
    - 实现导航点击切换逻辑
    - _需求: 3.1, 3.2, 3.3, 3.4_

  - [x] 5.3 实现主窗口布局组件


    - 创建主窗口容器结构
    - 实现控制条、导航栏、内容区域布局
    - 实现窗口收起时的布局切换
    - _需求: 2.1, 2.6_

- [ ] 6. 自定义组件库开发
  - [ ] 6.1 实现图标标签组件
    - 创建IconLabel组件 (支持图标显示和占位符)
    - 实现图标缓存和加载逻辑
    - 支持不同图标类型 (hero/item/trait/hex)
    - 实现点击交互功能
    - _需求: 6.1, 6.2_

  - [ ] 6.2 实现可点击标签组件
    - 创建ClickableLabel组件
    - 实现悬停效果和点击交互
    - 支持数据绑定和事件发射
    - _需求: 6.2_

  - [ ] 6.3 实现滚动和搜索组件
    - 创建水平滚动区域组件 (支持鼠标滚轮)
    - 实现搜索输入框组件 (带清除按钮和防抖)
    - _需求: 6.3, 6.4_

- [ ] 7. 列表视图组件实现
  - [ ] 7.1 实现阵容列表视图
    - 创建阵容列表组件结构
    - 实现阵容数据加载和显示
    - 实现阵容项点击跳转详情功能
    - _需求: 4.1_

  - [ ] 7.2 实现英雄列表视图
    - 创建英雄列表组件结构
    - 实现英雄数据加载和显示 (图标、费用、羁绊)
    - 实现英雄项点击跳转详情功能
    - _需求: 4.2_

  - [ ] 7.3 实现装备列表视图
    - 创建装备列表组件结构
    - 实现装备数据加载和显示 (图标、合成路径)
    - 实现装备项点击跳转详情功能
    - _需求: 4.3_

  - [ ] 7.4 实现海克斯列表视图
    - 创建海克斯列表组件结构
    - 实现海克斯数据加载和显示 (图标、评级、描述)
    - _需求: 4.4_

- [ ] 8. 详情视图组件实现
  - [ ] 8.1 实现阵容详情视图
    - 创建阵容详情组件结构
    - 实现阵容详细信息显示
    - 实现英雄配置和装备推荐展示
    - 实现相关项目点击跳转功能
    - _需求: 4.5_

  - [ ] 8.2 实现英雄详情视图
    - 创建英雄详情组件结构
    - 实现英雄属性、技能信息显示
    - 实现推荐装备和羁绊信息展示
    - 实现相关项目点击跳转功能
    - _需求: 4.6_

  - [ ] 8.3 实现装备详情视图
    - 创建装备详情组件结构
    - 实现装备属性和合成路径显示
    - 实现适用英雄和相关装备展示
    - 实现相关项目点击跳转功能
    - _需求: 4.6_

- [ ] 9. 样式系统实现
  - [ ] 9.1 配置Tailwind主题
    - 配置颜色系统 (复刻常量与配置.py中的颜色)
    - 配置字体大小和权重
    - 配置间距和尺寸常量
    - _需求: 7.1, 7.2, 7.4_

  - [ ] 9.2 实现全局样式
    - 创建滚动条样式
    - 实现评级徽章样式
    - 创建图标占位符样式
    - _需求: 7.1, 7.3_

- [ ] 10. 页面导航和路由逻辑
  - [ ] 10.1 实现页面切换逻辑
    - 实现主页面切换功能 (switchMainPage)
    - 实现详情页导航功能 (navigateToDetail)
    - 实现页面历史记录管理
    - _需求: 3.3, 3.5_

  - [ ] 10.2 实现返回导航逻辑
    - 实现返回按钮功能 (goBack)
    - 实现历史记录栈管理
    - 实现返回按钮显示/隐藏逻辑
    - _需求: 2.4, 3.4_

- [ ] 11. 数据预加载和缓存优化
  - [ ] 11.1 实现全局数据预加载
    - 实现英雄信息预加载
    - 实现羁绊图标预加载
    - 实现装备信息预加载
    - _需求: 5.1_

  - [ ] 11.2 实现图标缓存管理
    - 实现图标LRU缓存机制
    - 实现缓存大小限制和清理
    - 实现图标加载失败处理
    - _需求: 6.1_

- [ ] 12. 集成测试和调试
  - [ ] 12.1 组件集成测试
    - 测试主窗口和控制条交互
    - 测试导航栏和页面切换
    - 测试列表和详情页跳转
    - _需求: 2.1, 3.1, 4.1_

  - [ ] 12.2 数据流测试
    - 测试数据库查询和缓存
    - 测试数据预加载功能
    - 测试图标加载和缓存
    - _需求: 5.1, 5.2, 6.1_

- [ ] 13. 性能优化和最终调试
  - [ ] 13.1 性能优化
    - 优化组件渲染性能
    - 优化数据库查询性能
    - 优化内存使用和缓存策略
    - _需求: 1.4_

  - [ ] 13.2 最终测试和打包
    - 进行完整功能测试
    - 验证与原应用的功能一致性
    - 构建生产版本并测试打包体积
    - _需求: 1.4_