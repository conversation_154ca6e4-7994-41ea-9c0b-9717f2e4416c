# 海克斯说明_简化版.py
import os
import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

def fetch_hex_descriptions_simple(debug_mode=True, manual_control=True):
    """简化版海克斯强化说明获取"""

    # 配置Chrome选项 - 与测试脚本相同
    chrome_options = Options()
    if not debug_mode:
        chrome_options.add_argument("--headless=new")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument('--ignore-certificate-errors')
    chrome_options.add_argument('--ignore-ssl-errors')
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = None
    all_hex_data = {}

    try:
        # 初始化WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)

        # 访问网页
        url = "https://lol.qq.com/tft/#/hex"
        print(f"正在访问: {url}")
        driver.get(url)

        # 等待页面加载
        time.sleep(10)
        print("页面加载完成")

        if manual_control:
            print("\n" + "="*60)
            print("🔧 手动控制模式")
            print("="*60)
            print("请在浏览器中手动切换到您想要的赛季")
            print("切换完成后，请回到控制台")
            print("按 Enter 键开始爬取...")
            print("按 'q' + Enter 键退出程序")
            print("="*60)

            user_input = input().strip().lower()
            if user_input == 'q':
                print("用户选择退出程序")
                return None

            print("开始爬取海克斯强化说明...")
        
        # 获取所有强化等级按钮
        print("\n🔍 检测强化等级按钮...")
        level_buttons = driver.find_elements(By.CSS_SELECTOR, "a.btn-type")

        if not level_buttons:
            print("❌ 未找到强化等级按钮")
            return None

        print(f"✅ 找到 {len(level_buttons)} 个强化等级")

        # 显示所有等级
        print("\n📋 检测到的强化等级:")
        for i, button in enumerate(level_buttons):
            level_name = button.text.strip()
            print(f"  {i+1}. {level_name}")

        # 过滤掉"强化符文"，只保留一、二、三级强化符文
        valid_levels = []
        for i, button in enumerate(level_buttons):
            level_name = button.text.strip()
            if "一级" in level_name or "二级" in level_name or "三级" in level_name:
                valid_levels.append((i, button, level_name))

        print(f"\n🎯 将爬取 {len(valid_levels)} 个有效等级:")
        for _, _, level_name in valid_levels:
            print(f"  - {level_name}")

        print("="*50)

        # 遍历有效的强化等级
        for idx, (original_index, _, level_name) in enumerate(valid_levels):
            try:
                # 重新获取按钮列表以避免stale element问题
                level_buttons = driver.find_elements(By.CSS_SELECTOR, "a.btn-type")
                if original_index >= len(level_buttons):
                    break

                button = level_buttons[original_index]

                print(f"\n📂 [{idx+1}/{len(valid_levels)}] 处理强化等级: {level_name}")

                # 点击等级按钮
                driver.execute_script("arguments[0].click();", button)
                time.sleep(3)  # 等待内容加载

                # 获取当前等级的所有海克斯强化
                hex_items = driver.find_elements(By.CSS_SELECTOR, ".synergy-list-body > div")
                print(f"   📊 找到 {len(hex_items)} 个海克斯项目")
                
                level_data = []
                
                for j, hex_item in enumerate(hex_items):
                    try:
                        # 获取海克斯名称
                        hex_name = ""
                        try:
                            name_element = hex_item.find_element(By.CSS_SELECTOR, "span.hex-name")
                            hex_name = name_element.text.strip()
                        except:
                            print(f"  项目 {j+1}: 无法获取名称")
                            continue
                        
                        # 获取海克斯说明
                        description = ""
                        try:
                            desc_element = hex_item.find_element(By.CSS_SELECTOR, "div.synergy-txt")
                            description = desc_element.text.strip()
                        except:
                            description = "暂无详细说明"
                        
                        # 保存数据
                        hex_data = {
                            "name": hex_name,
                            "description": description,
                            "level": level_name
                        }
                        level_data.append(hex_data)
                        print(f"     ✓ {hex_name}: {description[:30]}...")

                    except Exception as item_e:
                        print(f"     ✗ 项目 {j+1} 处理失败: {item_e}")
                        continue

                all_hex_data[level_name] = level_data
                print(f"   ✅ 完成 {level_name}: 共 {len(level_data)} 个海克斯")
                print(f"   进度: {idx+1}/{len(valid_levels)} 等级完成")

            except Exception as level_e:
                print(f"处理等级 {level_name} 失败: {level_e}")
                continue
        
        return all_hex_data
        
    except Exception as e:
        print(f"获取海克斯说明失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    
    finally:
        if driver:
            driver.quit()

def save_to_json(data, output_path):
    """保存数据为JSON文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {output_path}")
        return True
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

def create_flat_mapping(hex_data):
    """创建扁平化映射"""
    mapping = {}
    for level_name, items in hex_data.items():
        for item in items:
            name = item["name"]
            mapping[name] = {
                "description": item["description"],
                "level": item["level"]
            }
    return mapping

def main():
    """主函数"""
    print("=== 海克斯强化说明获取程序 ===")
    print("🎮 手动控制版本")
    print()

    # 设置输出路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, "海克斯数据")
    mapping_dir = os.path.join(current_dir, "映射文件")

    descriptions_file = os.path.join(data_dir, "海克斯说明.json")
    mapping_file = os.path.join(mapping_dir, "海克斯说明映射.json")

    # 删除旧数据文件
    print("🗑️ 清理旧数据...")
    if os.path.exists(descriptions_file):
        os.remove(descriptions_file)
        print(f"   删除旧文件: {descriptions_file}")
    if os.path.exists(mapping_file):
        os.remove(mapping_file)
        print(f"   删除旧文件: {mapping_file}")

    # 获取数据
    hex_data = fetch_hex_descriptions_simple(debug_mode=True, manual_control=True)

    if not hex_data:
        print("获取数据失败或用户取消")
        return False
    
    # 创建扁平化映射
    mapping_data = create_flat_mapping(hex_data)
    
    # 保存数据
    save_to_json(hex_data, descriptions_file)
    save_to_json(mapping_data, mapping_file)
    
    # 统计信息
    total_count = sum(len(items) for items in hex_data.values())
    print(f"\n=== 统计信息 ===")
    print(f"总共获取了 {total_count} 个海克斯强化说明")
    print(f"涵盖 {len(hex_data)} 个强化等级:")
    
    for level_name, items in hex_data.items():
        print(f"  - {level_name}: {len(items)} 个")
    
    print(f"\n数据文件: {descriptions_file}")
    print(f"映射文件: {mapping_file}")
    print("\n获取完成！")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("程序执行失败")
    else:
        print("程序执行成功")
