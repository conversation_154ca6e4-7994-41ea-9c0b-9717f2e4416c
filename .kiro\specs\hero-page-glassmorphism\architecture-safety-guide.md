# UI重构架构安全指南

## 架构影响分析

### 🔴 高风险区域 - 必须保护

#### 1. Tauri API调用层
```typescript
// 必须保留的核心导入
import { invoke } from '@tauri-apps/api/tauri'
import { appWindow } from '@tauri-apps/api/window'

// 必须保留的数据库命令调用
const loadHeroes = async () => {
  try {
    const result = await invoke('get_hero_list')  // ⚠️ 不能删除
    return result
  } catch (error) {
    console.error('加载英雄数据失败:', error)
  }
}
```

#### 2. 窗口管理功能
```typescript
// 必须保留的窗口控制函数
const toggleWindow = async () => {
  await invoke('toggle_window_size')  // ⚠️ 不能删除
}

const startDrag = async () => {
  await invoke('start_drag')  // ⚠️ 不能删除
}

const closeApp = async () => {
  await invoke('close_app')  // ⚠️ 不能删除
}
```

#### 3. 数据库命令接口
```rust
// Rust端命令 - 不能修改
#[command]
pub async fn get_hero_list() -> QueryResult  // ⚠️ 接口不能变

#[command] 
pub async fn get_hero_detail(hero_name: String) -> QueryResult  // ⚠️ 接口不能变
```

### 🟡 中风险区域 - 谨慎修改

#### 1. Pinia Store结构
```typescript
// 可以修改内部实现，但接口要保持兼容
export const useHeroesStore = defineStore('heroes', () => {
  // 状态可以重构，但要保持向后兼容
  const heroes = ref<Hero[]>([])
  const loading = ref(false)
  
  // 方法接口要保持稳定
  const loadHeroes = async () => {
    // 内部实现可以改变
  }
  
  return {
    heroes,      // ⚠️ 导出接口不能变
    loading,     // ⚠️ 导出接口不能变
    loadHeroes   // ⚠️ 导出接口不能变
  }
})
```

#### 2. 组件事件系统
```typescript
// 事件名称要保持一致
const emit = defineEmits<{
  heroClick: [heroName: string]  // ⚠️ 事件名不能变
  filterChange: [costs: number[]]  // ⚠️ 事件名不能变
}>()
```

### 🟢 低风险区域 - 可以自由修改

#### 1. UI模板代码
```vue
<template>
  <!-- 这部分可以完全重写 -->
  <div class="hero-page">
    <!-- 新的Glassmorphism UI -->
  </div>
</template>
```

#### 2. CSS样式
```css
/* 所有样式都可以重写 */
.hero-card {
  /* 新的Glassmorphism样式 */
}
```

## 安全重构策略

### 第一步：创建架构保护层
```typescript
// composables/useHeroData.ts - 创建数据访问抽象层
export const useHeroData = () => {
  const loadHeroes = async () => {
    // 封装Tauri调用，保护上层组件
    return await invoke('get_hero_list')
  }
  
  const loadHeroDetail = async (heroName: string) => {
    return await invoke('get_hero_detail', { heroName })
  }
  
  return {
    loadHeroes,
    loadHeroDetail
  }
}
```

### 第二步：创建窗口控制抽象层
```typescript
// composables/useWindowControl.ts
export const useWindowControl = () => {
  const toggleSize = async () => {
    await invoke('toggle_window_size')
  }
  
  const startDrag = async () => {
    await invoke('start_drag')
  }
  
  const closeApp = async () => {
    await invoke('close_app')
  }
  
  return {
    toggleSize,
    startDrag,
    closeApp
  }
}
```

### 第三步：渐进式UI替换
```vue
<!-- HeroPage.vue -->
<script setup>
// 保留所有核心逻辑
import { useHeroData } from '@/composables/useHeroData'
import { useWindowControl } from '@/composables/useWindowControl'
import { useHeroesStore } from '@/stores/heroes'

const heroData = useHeroData()
const windowControl = useWindowControl()
const heroesStore = useHeroesStore()

// 保留所有数据加载逻辑
onMounted(async () => {
  await heroesStore.loadHeroes()
})
</script>

<template>
  <!-- 只替换这部分UI -->
  <div class="glassmorphism-hero-page">
    <!-- 全新的UI实现 -->
  </div>
</template>
```

## 测试验证清单

### 架构完整性测试
- [ ] Tauri命令调用正常
- [ ] 数据库查询返回正确数据
- [ ] 窗口控制功能正常
- [ ] 路由导航正常
- [ ] 状态管理同步正常

### 功能回归测试
- [ ] 英雄数据加载正常
- [ ] 搜索筛选功能正常
- [ ] 英雄详情跳转正常
- [ ] 窗口展开收起正常
- [ ] 窗口拖拽正常
- [ ] 应用关闭正常

### 性能测试
- [ ] 页面加载速度正常
- [ ] 内存使用量正常
- [ ] CPU使用率正常
- [ ] 动画流畅度正常

## 常见问题和解决方案

### 问题1：Tauri命令调用失败
```typescript
// 错误：删除了invoke导入
// import { invoke } from '@tauri-apps/api/tauri'  // 被误删

// 解决：确保导入存在
import { invoke } from '@tauri-apps/api/tauri'
```

### 问题2：窗口控制失效
```typescript
// 错误：删除了窗口API导入
// import { appWindow } from '@tauri-apps/api/window'  // 被误删

// 解决：恢复窗口API导入
import { appWindow } from '@tauri-apps/api/window'
```

### 问题3：数据加载失败
```typescript
// 错误：修改了命令名称
// await invoke('get_heroes')  // 错误的命令名

// 解决：使用正确的命令名
await invoke('get_hero_list')  // 正确的命令名
```

### 问题4：状态管理失效
```typescript
// 错误：修改了store接口
// const { heroData } = useHeroesStore()  // 错误的属性名

// 解决：使用正确的接口
const { heroes } = useHeroesStore()  // 正确的属性名
```

## 回滚策略

如果重构过程中出现问题，可以按以下步骤回滚：

1. **恢复备份文件**：将备份的组件文件恢复
2. **检查导入语句**：确保所有必要的导入都存在
3. **验证命令调用**：确保Tauri命令调用正确
4. **测试核心功能**：验证数据加载和窗口控制正常

## 总结

UI重构的关键是**只替换视觉层，保护逻辑层**：

- ✅ **可以改变**：HTML模板、CSS样式、组件结构
- ⚠️ **谨慎修改**：状态管理、事件系统、数据流
- ❌ **不能改变**：Tauri API调用、数据库命令、窗口控制API

通过创建抽象层和渐进式替换，可以最大程度地降低架构风险，确保重构过程的安全性。