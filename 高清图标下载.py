import json
import os
import requests
import time
import threading

# --- 配置 ---
HERO_MAP_FILE = '映射文件/英雄装备映射.json' # 英雄数据映射文件路径
ITEM_MAP_FILE = '映射文件/装备分类映射.json' # 装备数据映射文件路径 (更新以匹配新的分类文件名)
REQUEST_TIMEOUT = 15  # 请求超时时间（秒）
RETRY_DELAY = 2       # 重试间隔时间（秒）
MAX_RETRIES = 3       # 最大重试次数

# --- 日志记录 ---
# 允许外部传入一个日志函数，例如GUI的日志框更新函数
# 默认为 print
log_function = print

def set_logger(logger):
    """设置日志记录函数"""
    global log_function
    if callable(logger):
        log_function = logger

# --- 核心下载逻辑 ---

def _load_json(filepath):
    """加载JSON文件。"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
            log_function(f"成功加载JSON文件: {filepath}")
            return data
    except FileNotFoundError:
        log_function(f"错误: 文件未找到 {filepath}")
    except json.JSONDecodeError as e:
        log_function(f"错误: 解析JSON文件失败 {filepath} - {e}")
    except Exception as e:
        log_function(f"错误: 读取文件时发生未知错误 {filepath}: {e}")
    return None

def _ensure_dir(filepath):
    """确保文件所在的目录存在，如果不存在则创建。"""
    directory = os.path.dirname(filepath)
    if directory and not os.path.exists(directory):
        try:
            os.makedirs(directory)
            log_function(f"创建目录: {directory}")
        except OSError as e:
            log_function(f"错误: 创建目录失败 {directory} - {e}")
            return False
    return True

def _download_and_save_image(url, save_path):
    """直接下载图片原始数据并保存到指定路径，支持增量下载。"""
    if not _ensure_dir(save_path):
        return False

    # 检查文件是否已存在且完整
    if os.path.exists(save_path):
        try:
            # 检查文件大小是否合理 (大于100字节)
            if os.path.getsize(save_path) > 100:
                log_function(f"  图标已存在且完整，跳过下载: {save_path}")
                return True
            else:
                log_function(f"  图标文件可能损坏，将重新下载: {save_path}")
                # 删除损坏的文件
                os.remove(save_path)
        except OSError as e:
            log_function(f"  检查图标文件时出错，将重新下载: {e}")
            # 如果检查失败，尝试删除文件
            try:
                os.remove(save_path)
            except:
                pass

    attempt = 0
    while attempt < MAX_RETRIES:
        try:
            log_function(f"  尝试下载 (第 {attempt + 1}/{MAX_RETRIES} 次): {url}")
            response = requests.get(url, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            with open(save_path, 'wb') as f:
                f.write(response.content)
            log_function(f"  成功下载并保存原始文件到: {save_path}")
            return True
        except requests.exceptions.Timeout:
            log_function(f"  错误: 下载超时 (尝试 {attempt + 1}/{MAX_RETRIES})")
        except requests.exceptions.RequestException as e:
            log_function(f"  错误: 下载失败 (尝试 {attempt + 1}/{MAX_RETRIES}) - {e}")
        except IOError as e:
            log_function(f"  错误: 文件写入失败 (尝试 {attempt + 1}/{MAX_RETRIES}) - {e}")
            return False # 文件错误不重试
        except Exception as e:
            log_function(f"  错误: 处理过程中发生未知错误 (尝试 {attempt + 1}/{MAX_RETRIES}) - {e}")
            return False # 未知错误不重试

        attempt += 1
        if attempt < MAX_RETRIES:
            log_function(f"  将在 {RETRY_DELAY} 秒后重试...")
            time.sleep(RETRY_DELAY)

    log_function(f"错误: 达到最大重试次数，未能下载并保存图片 {url} -> {save_path}")
    return False

# --- 可外部调用的公共函数 ---

def download_hero_icons(base_dir='.'):
    """
    下载英雄图标的主函数。
    :param base_dir: 基础目录，用于解析相对路径。
    :return: (成功数量, 失败数量)
    """
    log_function("--- 开始处理英雄图标 ---")
    hero_map_path = os.path.join(base_dir, HERO_MAP_FILE)
    hero_data = _load_json(hero_map_path)

    if not hero_data:
        log_function("未能加载英雄数据，跳过英雄图标处理。")
        return 0, 0

    download_count = 0
    fail_count = 0

    for hero_name, data in hero_data.items():
        original_url = data.get('icon_url')
        # 英雄图标路径在JSON中可能是 'img_path'
        save_path_rel = data.get('img_path') or data.get('icon_path')

        if original_url and save_path_rel:
            modified_url = original_url.split('?')[0]
            # 确保保存路径是绝对路径
            save_path_abs = os.path.join(base_dir, save_path_rel)
            
            log_function(f"处理英雄: {hero_name} | URL: {modified_url} | 保存至: {save_path_abs}")
            if _download_and_save_image(modified_url, save_path_abs):
                download_count += 1
            else:
                fail_count += 1
        else:
            log_function(f"警告: 英雄 {hero_name} 的数据不完整，已跳过。")
            fail_count += 1
            
    log_function(f"--- 英雄图标处理完成 --- (成功: {download_count}, 失败: {fail_count})")
    return download_count, fail_count


def download_item_icons(base_dir='.'):
    """
    下载装备图标的主函数。
    :param base_dir: 基础目录，用于解析相对路径。
    :return: (成功数量, 失败数量)
    """
    log_function("--- 开始处理装备图标 ---")
    item_map_path = os.path.join(base_dir, ITEM_MAP_FILE)
    item_data = _load_json(item_map_path)
    
    if not item_data:
        log_function("未能加载装备数据，跳过装备图标处理。")
        return 0, 0

    items_to_process = item_data.get('processed_equipment')
    if not isinstance(items_to_process, dict):
        log_function("错误: 装备数据文件格式不正确或缺少 'processed_equipment' 键。")
        return 0, 0

    log_function(f"成功解析 '装备分类映射.json'，找到 {len(items_to_process)} 个装备。")
    download_count = 0
    fail_count = 0

    for item_name, data in items_to_process.items():
        original_url = data.get('icon_url')
        save_path_rel = data.get('icon_path')
        if original_url and save_path_rel:
            # 移除cdn-cgi的尺寸参数，保留format=auto
            modified_url = original_url.replace('width=48,height=48,', '')
            # 确保保存路径是绝对路径
            save_path_abs = os.path.join(base_dir, save_path_rel)

            log_function(f"处理装备: {item_name} | URL: {modified_url} | 保存至: {save_path_abs}")
            if _download_and_save_image(modified_url, save_path_abs):
                download_count += 1
            else:
                fail_count += 1
        else:
            log_function(f"警告: 装备 {item_name} 的数据不完整，已跳过。")
            fail_count += 1
            
    log_function(f"--- 装备图标处理完成 --- (成功: {download_count}, 失败: {fail_count})")
    return download_count, fail_count

# --- 主程序入口 (用于独立测试) ---
if __name__ == "__main__":
    print("这是一个函数库，不应直接运行。")
    print("但为了方便测试，我们将执行一次英雄和装备的图标下载...")
    
    # 获取脚本所在的目录，作为基础目录
    base_directory = os.path.dirname(os.path.abspath(__file__))
    
    print("\n--- 测试下载英雄图标 ---")
    download_hero_icons(base_dir=base_directory)
    
    print("\n--- 测试下载装备图标 ---")
    download_item_icons(base_dir=base_directory)

    print("\n--- 测试完成 ---") 