/**
 * 性能监控工具
 * 用于监控和对比数据加载性能
 */

interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  cacheHit?: boolean
  dataSize?: number
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map()
  private enabled: boolean = true

  /**
   * 开始性能监控
   */
  start(name: string, metadata?: { cacheHit?: boolean; dataSize?: number }) {
    if (!this.enabled) return

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      cacheHit: metadata?.cacheHit,
      dataSize: metadata?.dataSize
    })
    
    console.log(`🚀 [性能监控] 开始: ${name}`)
  }

  /**
   * 结束性能监控
   */
  end(name: string, metadata?: { dataSize?: number }) {
    if (!this.enabled) return

    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`⚠️ [性能监控] 未找到监控项: ${name}`)
      return
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration
    if (metadata?.dataSize) {
      metric.dataSize = metadata.dataSize
    }

    const cacheStatus = metric.cacheHit ? '缓存命中' : '数据库查询'
    const dataInfo = metric.dataSize ? ` (${metric.dataSize}条数据)` : ''
    
    console.log(`✅ [性能监控] 完成: ${name} - ${duration.toFixed(2)}ms - ${cacheStatus}${dataInfo}`)

    // 性能警告
    if (duration > 100) {
      console.warn(`⚠️ [性能警告] ${name} 耗时过长: ${duration.toFixed(2)}ms`)
    }
  }

  /**
   * 获取性能报告
   */
  getReport(): PerformanceMetric[] {
    return Array.from(this.metrics.values()).filter(m => m.duration !== undefined)
  }

  /**
   * 清除所有监控数据
   */
  clear() {
    this.metrics.clear()
  }

  /**
   * 启用/禁用监控
   */
  setEnabled(enabled: boolean) {
    this.enabled = enabled
  }

  /**
   * 打印性能报告
   */
  printReport() {
    const report = this.getReport()
    if (report.length === 0) {
      console.log('📊 [性能报告] 暂无数据')
      return
    }

    console.group('📊 [性能报告]')
    
    // 按耗时排序
    const sortedReport = report.sort((a, b) => (b.duration || 0) - (a.duration || 0))
    
    sortedReport.forEach(metric => {
      const cacheStatus = metric.cacheHit ? '🟢 缓存' : '🔴 数据库'
      const dataInfo = metric.dataSize ? ` (${metric.dataSize}条)` : ''
      console.log(`${metric.name}: ${metric.duration?.toFixed(2)}ms ${cacheStatus}${dataInfo}`)
    })

    // 统计信息
    const totalTime = sortedReport.reduce((sum, m) => sum + (m.duration || 0), 0)
    const cacheHits = sortedReport.filter(m => m.cacheHit).length
    const cacheHitRate = (cacheHits / sortedReport.length * 100).toFixed(1)
    
    console.log(`总耗时: ${totalTime.toFixed(2)}ms`)
    console.log(`缓存命中率: ${cacheHitRate}%`)
    console.groupEnd()
  }
}

// 导出单例实例
export const performanceMonitor = new PerformanceMonitor()

// 便捷函数
export const startPerf = (name: string, metadata?: { cacheHit?: boolean; dataSize?: number }) => {
  performanceMonitor.start(name, metadata)
}

export const endPerf = (name: string, metadata?: { dataSize?: number }) => {
  performanceMonitor.end(name, metadata)
}

export const perfReport = () => {
  performanceMonitor.printReport()
}
