# 软件更新系统配置文档

## 系统架构概述

我们的软件更新系统采用三层架构：

1. **ECS服务器** - 作为配置中心（公告板）
2. **七牛云存储** - 存储实际的更新文件
3. **客户端软件** - 通过配置中心获取下载地址

## 配置信息

### 环境变量设置

在系统中设置以下环境变量：

```bash
# Windows PowerShell
$env:QINIU_ACCESS_KEY = "你的七牛云AccessKey"
$env:QINIU_SECRET_KEY = "你的七牛云SecretKey"

# Windows CMD
set QINIU_ACCESS_KEY=你的七牛云AccessKey
set QINIU_SECRET_KEY=你的七牛云SecretKey
```

### 配置服务器JSON结构

ECS服务器上的配置文件 (`http://updater.yuxianglei.com/yimiaojue_config.json`)：

```json
{
  "release": {
    "version_url": "https://download.yuxianglei.com/version.json",
    "download_base_url": "https://download.yuxianglei.com/"
  },
  "beta": {
    "version_url": "https://download.yuxianglei.com/version-beta.json",
    "download_base_url": "https://download.yuxianglei.com/"
  }
}
```

## 工作流程

### 上传流程

1. 使用环境变量中的密钥创建七牛云认证对象
2. 生成上传Token
3. 使用 `put_file_v2` 函数上传文件到指定Key
4. 文件自动同步到CDN（download.yuxianglei.com）

### 下载流程

1. 客户端访问配置服务器获取配置信息
2. 解析配置获取 `download_base_url`
3. 拼接完整下载URL：`{download_base_url}/{file_key}`
4. 下载并验证文件

## 测试脚本

### 基础测试脚本

- `上传测试.py` - 测试文件上传功能
- `下载测试.py` - 测试文件下载功能
- `软件更新系统测试.py` - 完整的系统测试

### 运行测试

```bash
# 运行完整测试
python 软件更新系统测试.py
```

## 版本信息格式

推荐的版本信息JSON格式：

```json
{
  "version": "1.0.0",
  "build_time": "2025-01-29T10:00:00Z",
  "changelog": [
    "新增功能描述",
    "修复的问题描述"
  ],
  "files": {
    "main_executable": "YimiaoJue.exe",
    "size": 1024000,
    "hash": "文件哈希值"
  }
}
```

## 安全注意事项

1. **密钥安全**：绝不在代码中硬编码密钥，始终使用环境变量
2. **Token有效期**：上传Token设置合理的有效期（建议1小时）
3. **文件验证**：下载后验证文件完整性（哈希值）
4. **HTTPS**：所有网络传输使用HTTPS协议

## 故障排除

### 常见问题

1. **BadToken错误**：检查环境变量是否正确设置
2. **编码问题**：确保文件以UTF-8编码保存和读取
3. **网络超时**：适当设置请求超时时间
4. **缓存问题**：使用不同的文件Key避免CDN缓存

### 调试方法

1. 检查环境变量：`echo $env:QINIU_ACCESS_KEY`
2. 运行测试脚本验证链路
3. 查看七牛云控制台确认文件上传状态
4. 使用浏览器直接访问下载URL测试

## 后续扩展

1. **版本比较**：实现版本号比较逻辑
2. **增量更新**：支持差分更新减少下载量
3. **多平台支持**：为不同操作系统提供不同的更新包
4. **回滚机制**：支持更新失败时的版本回滚
5. **更新进度**：显示下载和安装进度

## 总结

整个软件更新系统已经完全打通，包括：

✅ 文件上传到七牛云存储  
✅ 通过配置服务器获取下载地址  
✅ 文件下载和内容验证  
✅ 支持文本文件和JSON配置文件  
✅ 完整的测试覆盖  

系统已就绪，可以开始集成到实际的软件更新流程中。