# 设计文档

## 概述

本设计文档基于需求文档，详细描述了TFT助手应用程序优化的技术实现方案。主要优化包括：装备窗口管理机制、动态OCR时序系统、日志系统重构、更新器解耦、增强的更新流程、兼容性改进、性能优化和版本回档系统。

## 架构

### 整体架构变更

```mermaid
graph TB
    subgraph "更新器进程"
        A[updater.exe] --> B[版本检查]
        B --> C[下载管理]
        C --> D[安装管理]
        D --> E[启动主程序]
    end
    
    subgraph "主程序进程"
        F[YimiaoJue.exe] --> G[TriggerManager]
        G --> H[HexManager]
        G --> I[EquipManager]
        H --> J[动态时序控制]
        I --> J
        J --> K[资源管理]
    end
    
    subgraph "日志系统"
        L[yimiaojue.log] --> M[配置和启动信息]
        N[ocr_debug.log] --> O[OCR处理详情]
    end
    
    subgraph "版本管理"
        P[版本服务器] --> Q[正式版历史]
        P --> R[Beta版历史]
        Q --> S[最近2个版本]
        R --> T[最近2个版本]
    end
```

## 组件和接口

### 1. 装备窗口管理器 (EquipWindowManager)

#### 设计原理
当前装备窗口无法正常消失的问题主要源于窗口生命周期管理不当和清理时机不准确。

#### 核心组件

```python
class EnhancedEquipManager:
    def __init__(self, app_ref):
        self.window_cleanup_handler = WindowCleanupHandler()
        self.window_state_tracker = WindowStateTracker()
        
    def update_or_create_equip_windows(self, matched_names, total_count):
        # 增强的窗口更新逻辑
        pass
        
    def clear_equip_results(self):
        # 强制清理所有窗口
        self.window_cleanup_handler.force_cleanup()
        
class WindowCleanupHandler:
    def __init__(self):
        self.cleanup_timer = None
        self.force_cleanup_flag = False
        
    def schedule_cleanup(self, delay_ms=200):
        """延迟清理窗口，避免闪烁"""
        pass
        
    def force_cleanup(self):
        """立即强制清理所有窗口"""
        pass
```

#### 关键改进点

1. **即时清理机制**：当装备数量变为0时，立即触发窗口清理
2. **延迟清理策略**：使用200ms延迟避免窗口闪烁
3. **强制清理保障**：在OCR停止时强制清理所有残留窗口
4. **状态跟踪**：跟踪窗口状态，确保清理完整性

### 2. 动态OCR时序系统 (DynamicOCRTimer)

#### 设计原理
替换固定的50秒运行时长，实现基于结果反馈的动态延长机制。

#### 核心组件

```python
class DynamicOCRTimer:
    def __init__(self, initial_timeout=5, manual_timeout=10):
        self.initial_timeout = initial_timeout
        self.manual_timeout = manual_timeout
        self.extension_timeout = 5
        self.last_result_time = None
        self.is_manual_trigger = False
        
    def start_timer(self, manual=False):
        """启动计时器"""
        self.is_manual_trigger = manual
        timeout = self.manual_timeout if manual else self.initial_timeout
        self.reset_timer(timeout)
        
    def extend_timer(self):
        """检测到有效结果时延长计时器"""
        self.last_result_time = time.time()
        self.reset_timer(self.extension_timeout)
        
    def should_continue(self):
        """检查是否应该继续OCR"""
        if self.last_result_time is None:
            return time.time() - self.start_time < self.current_timeout
        return time.time() - self.last_result_time < self.extension_timeout
```

#### 时序逻辑流程

```mermaid
stateDiagram-v2
    [*] --> 等待触发
    等待触发 --> 手动启动 : 用户点击
    等待触发 --> 自动启动 : 哈希触发
    
    手动启动 --> OCR运行 : 10秒超时
    自动启动 --> OCR运行 : 5秒超时
    
    OCR运行 --> 检测结果 : 每次循环
    检测结果 --> 延长时间 : 有有效结果
    检测结果 --> 超时检查 : 无有效结果
    
    延长时间 --> OCR运行 : +5秒
    超时检查 --> 停止OCR : 超时
    超时检查 --> OCR运行 : 未超时
    
    停止OCR --> [*] : 清理资源
```

### 3. 日志系统重构 (LoggingSystemRedesign)

#### 设计原理
明确分离yimiaojue.log和ocr_debug.log的职责，优化日志内容和性能。

#### 日志分工设计

```python
class EnhancedLoggingSystem:
    def __init__(self):
        self.main_logger = self.setup_main_logger()  # yimiaojue.log
        self.ocr_logger = self.setup_ocr_logger()    # ocr_debug.log
        
    def setup_main_logger(self):
        """配置主日志记录器 - 记录配置和启动信息"""
        logger = logging.getLogger('MAIN')
        # 配置为记录：
        # - 应用启动信息
        # - 模块加载路径
        # - 配置文件路径和关键设置
        # - 系统状态变化
        return logger
        
    def setup_ocr_logger(self):
        """配置OCR调试日志记录器 - 记录OCR处理详情"""
        logger = logging.getLogger('OCR_DEBUG')
        # 配置为记录：
        # - OCR引擎初始化
        # - 图像处理过程
        # - 识别结果和置信度
        # - 性能指标
        return logger
```

#### 日志内容规划

**yimiaojue.log 内容：**
- 应用启动时间和版本信息
- 所有模块的加载路径和版本
- 配置文件路径和关键配置项
- 数据库连接信息
- 管理器初始化状态
- 触发器启动/停止事件
- 系统错误和异常

**ocr_debug.log 内容：**
- OCR引擎启动和配置
- 图像截取和预处理详情
- 识别结果和置信度分数
- 哈希计算和比较结果
- 性能计时信息
- OCR错误和重试逻辑

### 4. 更新器解耦系统 (DecoupledUpdaterSystem)

#### 设计原理
将updater.exe和主程序完全分离，通过可扩展的JSON配置支持未来的结构变化。

#### 可扩展配置设计

**update_manifest.json** - 标准更新清单（跟随每个更新包）：

```json
{
  "manifest_version": "1.0",
  "update_info": {
    "from_version": "1.1.9",
    "to_version": "1.2.0",
    "update_type": "incremental",
    "release_channel": "release"
  },
  
  "compatibility": {
    "min_updater_version": "3.0",
    "supported_source_versions": ["1.1.8", "1.1.9"],
    "breaking_changes": false,
    "migration_required": true
  },
  
  "operations": [
    {
      "type": "backup_current",
      "description": "备份当前版本",
      "target_patterns": ["YimiaoJue/", "config.ini", "*.log"]
    },
    {
      "type": "cleanup_deprecated",
      "description": "清理废弃文件",
      "patterns": [
        "YimiaoJue/modules/tesseract1/",
        "YimiaoJue/managers备份.py",
        "YimiaoJue/**/*.pyc",
        "YimiaoJue/**/__pycache__/"
      ]
    },
    {
      "type": "extract_files",
      "description": "解压新文件",
      "source": "YimiaoJue/",
      "target": "YimiaoJue/",
      "overwrite": true,
      "preserve_config": ["config.ini", "config.json"]
    },
    {
      "type": "migrate_data",
      "description": "迁移数据文件",
      "migrations": [
        {
          "from": "tft_data.db",
          "to": "YimiaoJue/tft_data.db",
          "backup": true
        }
      ]
    },
    {
      "type": "update_config",
      "description": "更新配置文件",
      "config_updates": [
        {
          "file": "YimiaoJue/config.ini",
          "section": "OCR",
          "key": "engine",
          "value": "rapidocr"
        }
      ]
    }
  ],
  
  "post_install": {
    "verify_files": [
      "YimiaoJue/ocr查询.py",
      "YimiaoJue/managers/equip_manager.py",
      "YimiaoJue/modules/tft_data.db"
    ],
    "startup_test": {
      "executable": "python",
      "args": ["YimiaoJue/ocr查询.py", "--version"],
      "expected_output_pattern": "弈秒决.*1\\.2\\.0"
    }
  },
  
  "rollback_info": {
    "critical_files": [
      "YimiaoJue/tft_data.db",
      "YimiaoJue/config.ini"
    ],
    "rollback_instructions": "如果更新失败，请从backup_old_version恢复"
  }
}
```

**app_structure.json** - 应用程序结构定义（可选，用于复杂场景）：

```json
{
  "version": "1.0",
  "app_info": {
    "name": "弈秒决",
    "main_executable": "ocr查询.py",
    "python_executable": "python.exe",
    "working_directory": "YimiaoJue",
    "startup_args": [],
    "entry_point": "python"
  },
  "deployment": {
    "type": "directory",
    "structure": {
      "YimiaoJue/": {
        "type": "directory",
        "required": true,
        "contents": {
          // 核心模块
          "ocr查询.py": {"type": "file", "required": true, "main": true},
          "config.py": {"type": "file", "required": true},
          "config.ini": {"type": "file", "required": true},
          "config.json": {"type": "file", "required": false},
          "ocr_handler.py": {"type": "file", "required": true},
          "data_query.py": {"type": "file", "required": true},
          "utils.py": {"type": "file", "required": true},
          "logger_setup.py": {"type": "file", "required": true},
          "instance_checker.py": {"type": "file", "required": true},
          
          // 管理器模块
          "managers/": {
            "type": "directory", 
            "required": true,
            "contents": {
              "__init__.py": {"type": "file", "required": true},
              "equip_manager.py": {"type": "file", "required": true},
              "hex_manager.py": {"type": "file", "required": true},
              "trigger_manager.py": {"type": "file", "required": true}
            }
          },
          
          // 资源模块
          "modules/": {
            "type": "directory",
            "required": true,
            "contents": {
              "app.ico": {"type": "file", "required": false},
              "hash_bases.json": {"type": "file", "required": true},
              "tft_data.db": {"type": "file", "required": true},
              "local_version.json": {"type": "file", "required": true},
              "version.json": {"type": "file", "required": false},
              "fonts/": {"type": "directory", "required": false}
            }
          },
          
          // 数据文件
          "tft_data.db": {"type": "file", "required": true},
          "update_config.json": {"type": "file", "required": false},
          
          // 更新系统
          "updater.py": {"type": "file", "required": false},
          "upload_tool.py": {"type": "file", "required": false},
          
          // 工具文件
          "get_game_window_size.py": {"type": "file", "required": false},
          "边框测试工具.py": {"type": "file", "required": false},
          
          // 运行时目录
          "logs/": {"type": "directory", "required": false, "runtime": true},
          "__pycache__/": {"type": "directory", "required": false, "runtime": true}
        }
      },
      
      // 可选测试模块
      "YimiaoJue/ocr效果测试/": {
        "type": "directory",
        "required": false,
        "contents": {
          "*.py": {"type": "pattern", "required": false},
          "*.png": {"type": "pattern", "required": false},
          "hash_bases.json": {"type": "file", "required": false}
        }
      }
    }
  },
  
  "runtime_requirements": {
    "python_version": "3.8+",
    "required_packages": [
      "tkinter", "PIL", "opencv-python", "imagehash", 
      "sqlite3", "requests", "packaging", "rapidocr-onnxruntime"
    ],
    "deprecated_packages": ["tesseract", "pytesseract"]
  },
  
  "compatibility": {
    "min_updater_version": "3.0",
    "migration_rules": {
      "from_single_file": {
        "backup_files": ["YimiaoJue.exe", "main.exe", "ocr查询.exe"],
        "cleanup_files": ["*.pyc", "__pycache__/", "*.log"]
      },
      "from_old_structure": {
        "file_mappings": {
          "main.py": "ocr查询.py",
          "old_config.ini": "config.ini"
        },
        "directory_mappings": {
          "old_managers/": "managers/",
          "resources/": "modules/"
        }
      }
    }
  },
  
  "extensions": {
    "optional_components": [
      {
        "name": "OCR效果测试工具",
        "path": "ocr效果测试/",
        "required": false,
        "description": "用于测试OCR效果的工具和测试图片"
      },
      {
        "name": "上传工具",
        "path": "upload_tool.py",
        "required": false,
        "description": "版本打包上传工具"
      }
    ],
    "future_modules": {
      "ai_advisor": {
        "path": "ai_modules/",
        "main_file": "ai_advisor.py",
        "dependencies": ["tensorflow", "numpy"]
      }
    }
  }
}
```

#### 标准更新器兼容性架构

基于软件更新的最佳实践，更新器应该遵循以下标准流程：

```python
class StandardUpdater:
    """标准更新器 - 遵循软件更新最佳实践"""
    
    def __init__(self):
        self.updater_version = "3.0"
        self.manifest_handler = UpdateManifestHandler()
        self.compatibility_layer = CompatibilityLayer()
        self.rollback_manager = RollbackManager()
        
    def execute_update_cycle(self):
        """执行标准更新周期"""
        try:
            # 1. 预检查阶段
            self.pre_update_checks()
            
            # 2. 下载阶段
            update_package = self.download_update_package()
            
            # 3. 验证阶段
            self.verify_update_package(update_package)
            
            # 4. 兼容性检查
            self.check_compatibility(update_package)
            
            # 5. 备份阶段
            backup_info = self.create_backup()
            
            # 6. 安装阶段
            self.install_update(update_package, backup_info)
            
            # 7. 验证安装
            self.verify_installation()
            
            # 8. 清理阶段
            self.cleanup_update_files()
            
        except UpdateException as e:
            self.handle_update_failure(e)

class UpdateManifestHandler:
    """更新清单处理器 - 处理版本间的差异描述"""
    
    def __init__(self):
        self.manifest_version = "1.0"
        
    def load_update_manifest(self, update_package_path):
        """加载更新清单"""
        manifest_path = os.path.join(update_package_path, "update_manifest.json")
        
        if not os.path.exists(manifest_path):
            # 如果没有清单，使用传统的全量更新方式
            return self.generate_legacy_manifest(update_package_path)
            
        with open(manifest_path, 'r', encoding='utf-8') as f:
            manifest = json.load(f)
            
        return self.validate_manifest(manifest)
    
    def generate_legacy_manifest(self, package_path):
        """为旧版本生成兼容性清单"""
        return {
            "manifest_version": "1.0",
            "update_type": "full_replacement",
            "target_structure": "YimiaoJue/",
            "compatibility": {
                "min_version": "1.0.0",
                "migration_required": True
            },
            "operations": [
                {
                    "type": "backup_current",
                    "target": "YimiaoJue/"
                },
                {
                    "type": "extract_all",
                    "source": ".",
                    "target": "YimiaoJue/"
                },
                {
                    "type": "cleanup_deprecated",
                    "patterns": ["tesseract1/", "*.pyc", "__pycache__/"]
                }
            ]
        }

class CompatibilityLayer:
    """兼容性层 - 处理不同版本间的兼容性"""
    
    def __init__(self):
        self.supported_transitions = {
            # 从单文件版本到目录版本
            "single_to_directory": {
                "detect": lambda: os.path.exists("YimiaoJue.exe") and not os.path.exists("YimiaoJue/"),
                "migrate": self.migrate_single_to_directory
            },
            # 从旧目录结构到新目录结构
            "old_directory_to_new": {
                "detect": lambda: os.path.exists("YimiaoJue/") and self.has_deprecated_files(),
                "migrate": self.migrate_old_directory_structure
            },
            # 从Python脚本版本到打包版本
            "script_to_executable": {
                "detect": lambda: os.path.exists("YimiaoJue/ocr查询.py") and not os.path.exists("YimiaoJue/YimiaoJue.exe"),
                "migrate": self.migrate_script_to_executable
            }
        }
    
    def detect_current_version_type(self):
        """检测当前版本类型"""
        for transition_name, transition_info in self.supported_transitions.items():
            if transition_info["detect"]():
                return transition_name.split("_to_")[0]
        
        return "unknown"
    
    def plan_migration(self, current_type, target_manifest):
        """规划迁移策略"""
        target_type = target_manifest.get("target_structure_type", "directory")
        transition_key = f"{current_type}_to_{target_type}"
        
        if transition_key in self.supported_transitions:
            return self.supported_transitions[transition_key]["migrate"]
        
        # 如果没有直接的迁移路径，使用通用迁移
        return self.generic_migration
    
    def has_deprecated_files(self):
        """检查是否存在废弃文件"""
        deprecated_patterns = [
            "YimiaoJue/modules/tesseract1/",
            "YimiaoJue/managers备份.py",
            "YimiaoJue/*.pyc"
        ]
        
        for pattern in deprecated_patterns:
            if glob.glob(pattern):
                return True
        return False
    
    def migrate_single_to_directory(self, backup_info, target_manifest):
        """从单文件版本迁移到目录版本"""
        # 1. 备份单文件版本
        single_files = ["YimiaoJue.exe", "main.exe", "config.ini"]
        for file in single_files:
            if os.path.exists(file):
                shutil.copy2(file, f"backup_old_version/{file}")
        
        # 2. 创建目录结构
        os.makedirs("YimiaoJue", exist_ok=True)
        
        # 3. 迁移配置文件
        if os.path.exists("config.ini"):
            shutil.copy2("config.ini", "YimiaoJue/config.ini")
        
        # 4. 清理旧文件
        for file in single_files:
            if os.path.exists(file):
                os.remove(file)

class RollbackManager:
    """回滚管理器 - 处理更新失败时的回滚"""
    
    def __init__(self):
        self.backup_directory = "backup_old_version"
        self.rollback_log = []
        
    def create_rollback_point(self):
        """创建回滚点"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        rollback_info = {
            "timestamp": timestamp,
            "backup_path": f"{self.backup_directory}_{timestamp}",
            "current_structure": self.analyze_current_structure()
        }
        
        # 创建备份
        if os.path.exists("YimiaoJue"):
            shutil.copytree("YimiaoJue", rollback_info["backup_path"])
        
        return rollback_info
    
    def execute_rollback(self, rollback_info):
        """执行回滚操作"""
        try:
            # 1. 清理当前安装
            if os.path.exists("YimiaoJue"):
                shutil.rmtree("YimiaoJue")
            
            # 2. 恢复备份
            if os.path.exists(rollback_info["backup_path"]):
                shutil.copytree(rollback_info["backup_path"], "YimiaoJue")
            
            # 3. 记录回滚操作
            self.rollback_log.append({
                "timestamp": datetime.now().isoformat(),
                "rollback_point": rollback_info["timestamp"],
                "success": True
            })
            
            return True
            
        except Exception as e:
            self.rollback_log.append({
                "timestamp": datetime.now().isoformat(),
                "rollback_point": rollback_info["timestamp"],
                "success": False,
                "error": str(e)
            })
            return False

class DeploymentHandler:
    def __init__(self, config):
        self.config = config
        
    def detect_current_structure(self):
        """检测当前部署结构"""
        app_info = self.config.get('app_info', {})
        working_dir = app_info.get('working_directory')
        main_exe = app_info.get('main_executable')
        
        if working_dir and os.path.exists(working_dir):
            return 'directory'
        elif main_exe and os.path.exists(main_exe):
            return 'single_file'
        else:
            return 'unknown'
    
    def install_update_package(self, package_path):
        """根据配置安装更新包"""
        structure = self.config.get('deployment', {}).get('structure', {})
        
        # 根据配置的结构定义来安装文件
        for path, definition in structure.items():
            self.install_component(package_path, path, definition)
    
    def install_component(self, source_root, target_path, definition):
        """安装单个组件"""
        component_type = definition.get('type')
        is_required = definition.get('required', False)
        
        if component_type == 'directory':
            self.install_directory(source_root, target_path, definition)
        elif component_type == 'file':
            self.install_file(source_root, target_path, definition)
        elif component_type == 'pattern':
            self.install_pattern(source_root, target_path, definition)

class CompatibilityManager:
    def __init__(self, config):
        self.config = config
        self.migration_rules = config.get('compatibility', {}).get('migration_rules', {})
    
    def migrate_from_old_version(self, old_structure_type):
        """从旧版本迁移"""
        if old_structure_type == 'single_file':
            self.migrate_from_single_file()
        elif old_structure_type == 'old_directory':
            self.migrate_from_old_directory()
    
    def migrate_from_single_file(self):
        """从单文件版本迁移"""
        rules = self.migration_rules.get('from_single_file', {})
        
        # 备份旧文件
        backup_files = rules.get('backup_files', [])
        for file_pattern in backup_files:
            self.backup_matching_files(file_pattern)
        
        # 清理文件
        cleanup_files = rules.get('cleanup_files', [])
        for file_pattern in cleanup_files:
            self.cleanup_matching_files(file_pattern)
    
    def handle_new_components(self, update_package):
        """处理新增组件"""
        extensions = self.config.get('extensions', {})
        additional_modules = extensions.get('additional_modules', [])
        
        for module in additional_modules:
            self.install_additional_module(update_package, module)

class StartupManager:
    def __init__(self, config):
        self.config = config
    
    def launch_application(self):
        """根据配置启动应用程序"""
        app_info = self.config.get('app_info', {})
        
        main_exe = app_info.get('main_executable', 'YimiaoJue.exe')
        working_dir = app_info.get('working_directory', 'YimiaoJue')
        startup_args = app_info.get('startup_args', [])
        
        # 构建完整路径
        if working_dir:
            exe_path = os.path.join(working_dir, main_exe)
            cwd = working_dir
        else:
            exe_path = main_exe
            cwd = os.path.dirname(exe_path) or '.'
        
        # 验证文件存在
        if not os.path.exists(exe_path):
            raise FileNotFoundError(f"主程序文件不存在: {exe_path}")
        
        # 根据入口点类型启动程序
        entry_point = app_info.get('entry_point', 'executable')
        
        if entry_point == 'python':
            # Python脚本启动方式
            python_exe = app_info.get('python_executable', 'python.exe')
            main_script = app_info.get('main_executable', 'ocr查询.py')
            script_path = os.path.join(cwd, main_script) if working_dir else main_script
            
            subprocess.Popen([python_exe, script_path] + startup_args, cwd=cwd)
            logging.info(f"成功启动Python应用程序: {python_exe} {script_path}")
        else:
            # 可执行文件启动方式
            subprocess.Popen([exe_path] + startup_args, cwd=cwd)
            logging.info(f"成功启动应用程序: {exe_path}")

class ConfigUpdateHandler:
    """处理配置文件的版本更新"""
    
    def __init__(self):
        self.config_filename = "app_structure.json"
        
    def update_config_from_package(self, update_package_path):
        """从更新包中更新配置文件"""
        new_config_path = os.path.join(update_package_path, self.config_filename)
        
        if os.path.exists(new_config_path):
            # 备份当前配置
            if os.path.exists(self.config_filename):
                shutil.copy2(self.config_filename, f"{self.config_filename}.backup")
            
            # 复制新配置
            shutil.copy2(new_config_path, self.config_filename)
            logging.info("已更新应用程序结构配置")
            return True
        
        return False
    
    def validate_new_config(self, config_path):
        """验证新配置的有效性"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            validator = ConfigValidator()
            errors = validator.validate_config(config)
            
            if errors:
                logging.warning(f"新配置存在问题: {errors}")
                return False
            
            return True
        except Exception as e:
            logging.error(f"验证新配置失败: {e}")
            return False
```

#### 更新流程设计

```mermaid
sequenceDiagram
    participant U as updater.exe
    participant S as 版本服务器
    participant C as app_structure.json
    participant M as 主程序
    participant F as 文件系统
    
    U->>C: 加载结构配置
    U->>S: 检查版本信息
    S->>U: 返回版本数据和新配置
    U->>U: 比较本地版本
    
    alt 需要更新
        U->>S: 下载更新包
        U->>U: 校验文件完整性
        U->>C: 检查新结构配置
        U->>F: 根据配置备份当前版本
        U->>F: 根据配置解压新版本
        U->>U: 显示手动重启按钮
        
        Note over U: 用户点击重启
        U->>F: 根据配置清理旧版本
        U->>C: 根据配置启动新版本
        U->>M: 启动主程序
    else 无需更新
        U->>C: 根据配置启动主程序
        U->>M: 直接启动主程序
    end
```

#### 未来扩展示例

**场景1：主程序改名为TFTAssistant**

```json
{
  "version": "2.0",
  "app_info": {
    "name": "TFT助手",
    "main_executable": "TFTAssistant.exe",
    "working_directory": "TFTAssistant",
    "startup_args": ["--mode", "production"]
  },
  "deployment": {
    "type": "directory",
    "structure": {
      "TFTAssistant/": {
        "type": "directory",
        "required": true,
        "contents": {
          "TFTAssistant.exe": {"type": "file", "required": true, "executable": true},
          "core/": {"type": "directory", "required": true},
          "plugins/": {"type": "directory", "required": false},
          "data/": {"type": "directory", "required": true}
        }
      }
    }
  },
  "compatibility": {
    "migration_rules": {
      "from_yimiaojue": {
        "backup_directory": "YimiaoJue",
        "data_migration": {
          "tft_data.db": "data/tft_data.db",
          "config.json": "config/app_config.json"
        }
      }
    }
  }
}
```

**场景2：添加新的AI模块**

```json
{
  "version": "1.5",
  "app_info": {
    "name": "弈秒决",
    "main_executable": "YimiaoJue.exe",
    "working_directory": "YimiaoJue"
  },
  "extensions": {
    "additional_modules": [
      {
        "name": "ai_advisor",
        "path": "ai_modules/",
        "executable": "ai_advisor.exe",
        "required": false,
        "auto_start": true
      }
    ],
    "optional_components": [
      {
        "name": "voice_assistant",
        "path": "voice/",
        "required": false,
        "dependencies": ["ai_advisor"]
      }
    ]
  }
}
```

#### 配置验证和错误处理

```python
class ConfigValidator:
    def __init__(self):
        self.required_fields = ['app_info', 'deployment']
        
    def validate_config(self, config):
        """验证配置文件的完整性"""
        errors = []
        
        # 检查必需字段
        for field in self.required_fields:
            if field not in config:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证应用信息
        app_info = config.get('app_info', {})
        if 'main_executable' not in app_info:
            errors.append("缺少主程序可执行文件名")
        
        # 验证部署结构
        deployment = config.get('deployment', {})
        if 'type' not in deployment:
            errors.append("缺少部署类型")
        
        return errors
    
    def fix_config_errors(self, config, errors):
        """尝试修复配置错误"""
        fixed_config = config.copy()
        
        # 应用默认值
        if 'app_info' not in fixed_config:
            fixed_config['app_info'] = {
                'main_executable': 'YimiaoJue.exe',
                'working_directory': 'YimiaoJue'
            }
        
        return fixed_config
```

### 5. 增强的更新流程 (EnhancedUpdateProcess)

#### 手动重启机制

```python
class ManualRestartHandler:
    def __init__(self):
        self.restart_button = None
        self.cleanup_pending = False
        
    def show_restart_button(self):
        """显示手动重启按钮"""
        self.restart_button = tk.Button(
            text="重启应用程序",
            command=self.handle_manual_restart
        )
        
    def handle_manual_restart(self):
        """处理手动重启"""
        self.cleanup_old_version()
        self.start_new_version()
        
    def cleanup_old_version(self):
        """清理旧版本文件"""
        # 处理backup_old_version目录
        # 清理残留文件
        pass
```

#### 错误恢复机制

```python
class UpdateErrorRecovery:
    def __init__(self):
        self.backup_path = "backup_old_version"
        self.recovery_log = []
        
    def create_recovery_point(self):
        """创建恢复点"""
        pass
        
    def restore_from_backup(self):
        """从备份恢复"""
        pass
        
    def verify_update_integrity(self):
        """验证更新完整性"""
        pass
```

### 6. 版本回档系统 (VersionRollbackSystem)

#### 服务器端版本管理

```python
class VersionHistoryManager:
    def __init__(self):
        self.release_history = {}  # 正式版历史
        self.beta_history = {}     # Beta版历史
        self.max_versions = 2      # 保留版本数
        
    def add_new_version(self, version_info, channel='release'):
        """添加新版本并管理历史"""
        history = self.release_history if channel == 'release' else self.beta_history
        
        # 添加新版本
        history[version_info['version']] = version_info
        
        # 清理过期版本
        self.cleanup_old_versions(history)
        
    def cleanup_old_versions(self, history):
        """清理过期版本，保留最新的2个"""
        if len(history) > self.max_versions:
            sorted_versions = sorted(history.keys(), key=parse_version, reverse=True)
            for version in sorted_versions[self.max_versions:]:
                del history[version]
                # 删除服务器上的文件
                
    def get_available_versions(self, channel='release'):
        """获取可用的历史版本"""
        history = self.release_history if channel == 'release' else self.beta_history
        return sorted(history.keys(), key=parse_version, reverse=True)
```

#### 客户端回档功能

```python
class RollbackHandler:
    def __init__(self, updater_ref):
        self.updater = updater_ref
        
    def show_rollback_options(self):
        """显示可回档的版本选项"""
        available_versions = self.get_available_rollback_versions()
        # 创建版本选择UI
        
    def perform_rollback(self, target_version):
        """执行版本回档"""
        # 下载目标版本
        # 备份当前版本
        # 安装目标版本
        # 处理版本号降级
        pass
        
    def handle_version_downgrade(self, current_version, target_version):
        """处理版本降级的兼容性问题"""
        pass
```

## 数据模型

### 版本信息数据结构

```json
{
  "release": {
    "current": "1.2.0",
    "history": {
      "1.2.0": {
        "version": "1.2.0",
        "url": "https://example.com/releases/v1.2.0.zip",
        "checksum": "sha256_hash",
        "release_date": "2024-01-15",
        "changelog": "修复装备窗口问题"
      },
      "1.1.9": {
        "version": "1.1.9",
        "url": "https://example.com/releases/v1.1.9.zip",
        "checksum": "sha256_hash",
        "release_date": "2024-01-10",
        "changelog": "优化OCR性能"
      }
    }
  },
  "beta": {
    "current": "1.2.1-beta.2",
    "history": {
      "1.2.1-beta.2": {
        "version": "1.2.1-beta.2",
        "url": "https://example.com/beta/v1.2.1-beta.2.zip",
        "checksum": "sha256_hash",
        "release_date": "2024-01-16",
        "changelog": "测试新的日志系统"
      },
      "1.2.1-beta.1": {
        "version": "1.2.1-beta.1",
        "url": "https://example.com/beta/v1.2.1-beta.1.zip",
        "checksum": "sha256_hash",
        "release_date": "2024-01-14",
        "changelog": "测试动态OCR时序"
      }
    }
  }
}
```

### OCR时序状态数据

```python
@dataclass
class OCRTimerState:
    start_time: float
    last_result_time: Optional[float]
    current_timeout: int
    is_manual_trigger: bool
    extension_count: int
    should_continue: bool
```

### 窗口状态跟踪数据

```python
@dataclass
class WindowState:
    window_id: str
    is_visible: bool
    last_update_time: float
    content_hash: str
    cleanup_scheduled: bool
```

## 错误处理

### OCR时序错误处理

```python
class OCRTimerErrorHandler:
    def handle_timer_exception(self, exception):
        """处理计时器异常"""
        logging.error(f"OCR计时器异常: {exception}")
        # 强制停止OCR
        # 清理资源
        # 重置状态
        
    def handle_extension_failure(self):
        """处理延长失败"""
        # 记录失败原因
        # 使用默认超时
        # 继续运行
```

### 更新错误处理

```python
class UpdateErrorHandler:
    def handle_download_failure(self, error):
        """处理下载失败"""
        # 重试机制
        # 备用下载源
        # 用户通知
        
    def handle_installation_failure(self, error):
        """处理安装失败"""
        # 自动恢复备份
        # 错误报告
        # 回滚操作
        
    def handle_compatibility_issues(self, old_version, new_version):
        """处理兼容性问题"""
        # 版本迁移
        # 配置转换
        # 数据迁移
```

### 窗口管理错误处理

```python
class WindowErrorHandler:
    def handle_cleanup_failure(self, window_list):
        """处理窗口清理失败"""
        # 强制销毁
        # 内存清理
        # 状态重置
        
    def handle_creation_failure(self, window_config):
        """处理窗口创建失败"""
        # 重试创建
        # 降级显示
        # 错误记录
```

## 测试策略

### 单元测试

1. **OCR时序测试**
   - 测试5秒超时机制
   - 测试动态延长逻辑
   - 测试手动/自动触发差异

2. **窗口管理测试**
   - 测试窗口创建和销毁
   - 测试清理时机
   - 测试状态跟踪

3. **版本管理测试**
   - 测试版本比较逻辑
   - 测试历史版本管理
   - 测试回档功能

### 集成测试

1. **更新流程测试**
   - 测试完整更新流程
   - 测试错误恢复
   - 测试兼容性处理

2. **日志系统测试**
   - 测试日志分离
   - 测试日志轮转
   - 测试性能影响

### 性能测试

1. **资源使用测试**
   - 内存使用监控
   - CPU使用监控
   - 线程数量监控

2. **响应时间测试**
   - OCR启动时间
   - 窗口显示延迟
   - 更新下载速度

## 部署考虑

### 打包策略

1. **更新器独立打包**
   - updater.exe单独打包
   - 最小化依赖
   - 向后兼容保证

2. **主程序打包**
   - 目录结构打包
   - 依赖文件包含
   - 版本信息嵌入

### 发布流程

1. **版本发布**
   - 自动版本号管理
   - 历史版本维护
   - 渠道分离管理

2. **回档支持**
   - 历史版本保留
   - 快速回档机制
   - 兼容性验证