# -*- coding: utf-8 -*-
"""
单例应用检查器

使用网络端口绑定的方式来确保程序只有一个实例在运行。
这比文件锁更可靠，因为操作系统会在程序退出（无论是正常还是异常）时自动释放端口。
"""

import socket
import sys
import tkinter as tk
from tkinter import messagebox

# 选择一个不常用的端口号
SINGLE_INSTANCE_PORT = 58888 

def check_and_lock():
    """
    检查是否已有实例在运行，如果没有，则锁定端口。

    Returns:
        socket.socket or None: 如果成功锁定，返回socket对象；否则返回None。
    """
    try:
        # 创建一个TCP socket
        lock_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # 尝试绑定到指定端口
        lock_socket.bind(("127.0.0.1", SINGLE_INSTANCE_PORT))
        # 成功绑定，说明是第一个实例
        print(f"成功锁定端口 {SINGLE_INSTANCE_PORT}，程序启动。")
        return lock_socket
    except OSError:
        # 端口已被占用，说明已有实例在运行
        print(f"端口 {SINGLE_INSTANCE_PORT} 已被占用，程序已在运行中。")
        # 弹出一个简单的提示框
        # 我们需要创建一个临时的Tk根窗口来显示messagebox
        root = tk.Tk()
        root.withdraw() # 隐藏主窗口
        messagebox.showerror("启动错误", "弈秒决 已在运行中，请勿重复启动！")
        root.destroy()
        sys.exit(1) # 退出当前（第二个）程序 

def release_lock(lock_socket):
    """
    [新增] 主动释放端口锁。
    这对于确保在主程序关闭后能立即重启至关重要。

    Args:
        lock_socket (socket.socket): 由 check_and_lock() 返回的socket对象。
    """
    if lock_socket:
        try:
            # 检查socket是否仍然有效且未关闭
            if (hasattr(lock_socket, 'fileno') and 
                lock_socket.fileno() != -1 and 
                not getattr(lock_socket, '_closed', False)):
                
                # 设置socket选项以立即释放端口
                lock_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                lock_socket.close()
                print(f"[Instance] 成功释放端口 {SINGLE_INSTANCE_PORT}")
            else:
                print(f"[Instance] 端口 {SINGLE_INSTANCE_PORT} 已经被释放")
        except Exception as e:
            print(f"[Instance] 释放端口锁时出错: {e}")

def force_release_port():
    """
    强制释放端口（用于紧急情况）
    """
    try:
        # 尝试创建并立即关闭socket来强制释放端口
        temp_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        temp_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        temp_socket.bind(("127.0.0.1", SINGLE_INSTANCE_PORT))
        temp_socket.close()
        print(f"[Instance] 强制释放端口 {SINGLE_INSTANCE_PORT} 成功")
    except Exception as e:
        print(f"[Instance] 强制释放端口失败: {e}") 