<template>
  <div class="relative">
    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="h-4 w-4 text-muted-foreground"
      >
        <circle cx="11" cy="11" r="8" />
        <path d="m21 21-4.35-4.35" />
      </svg>
    </div>
    <Input
      :value="modelValue"
      @input="handleInput"
      :placeholder="placeholder"
      :class="cn(
        'pl-10 pr-10 bg-section-bg border-border-color focus:border-text-highlight',
        props.class
      )"
    />
    <div
      v-if="modelValue && showClear"
      class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
      @click="clearInput"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"
      >
        <path d="M18 6 6 18" />
        <path d="m6 6 12 12" />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { cn } from '@/lib/utils'
import { Input } from '@/components/ui'

interface Props {
  modelValue: string
  placeholder?: string
  debounceMs?: number
  showClear?: boolean
  class?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search-changed', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '搜索...',
  debounceMs: 300,
  showClear: true,
  class: ''
})

const emit = defineEmits<Emits>()

let debounceTimer: number | null = null

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  
  emit('update:modelValue', value)
  
  // 防抖处理
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
  
  debounceTimer = setTimeout(() => {
    emit('search-changed', value)
  }, props.debounceMs)
}

const clearInput = () => {
  emit('update:modelValue', '')
  emit('search-changed', '')
  
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
}

// 清理定时器
onUnmounted(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
})
</script>
