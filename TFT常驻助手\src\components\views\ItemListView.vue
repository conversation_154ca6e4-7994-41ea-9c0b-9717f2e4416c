<template>
  <!-- 装备页面内容 -->
  <div class="item-page">
    
    <!-- 搜索区域 -->
    <div class="search-section">
      <input 
        v-model="searchQuery"
        type="text" 
        placeholder="搜索装备名称..."
        class="search-input"
      />
    </div>

    <!-- 装备类型筛选区域 -->
    <div class="filter-section">
      <div class="category-filters">
        <button 
          v-for="category in categoryFilters" 
          :key="category.value"
          class="category-filter-button"
          :class="{ 'active': selectedCategories.includes(category.value) }"
          @click="toggleCategoryFilter(category.value)"
        >
          {{ category.label }}
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载装备数据...</p>
    </div>

    <!-- 暂无数据提示 -->
    <div v-else-if="allItems.length === 0" class="no-data-state">
      <div class="no-data-icon">🛡️</div>
      <h3>暂无装备数据</h3>
      <p>数据库连接正常，但暂未加载到装备数据</p>
      <button @click="loadItems" class="retry-button">重新加载</button>
    </div>

    <!-- 装备竖向列表 -->
    <div v-else class="item-list-container">
      <!-- 表头 -->
      <div class="item-list-header">
        <div class="header-item header-icon"></div>
        <div class="header-item header-name" @click="sortBy('cn_name')">
          装备
          <span v-if="sortColumn === 'cn_name'" class="sort-indicator">
            {{ sortOrder === 'asc' ? '▲' : '▼' }}
          </span>
        </div>
        <div class="header-item header-tier" @click="sortBy('tier')">
          评级
          <span v-if="sortColumn === 'tier'" class="sort-indicator">
            {{ sortOrder === 'asc' ? '▲' : '▼' }}
          </span>
        </div>
        <div class="header-item header-play-rate" @click="sortBy('play_rate')">
          出场率
          <span v-if="sortColumn === 'play_rate'" class="sort-indicator">
            {{ sortOrder === 'asc' ? '▲' : '▼' }}
          </span>
        </div>
        <div class="header-item header-avg-place" @click="sortBy('avg_place')">
          平均排名
          <span v-if="sortColumn === 'avg_place'" class="sort-indicator">
            {{ sortOrder === 'asc' ? '▲' : '▼' }}
          </span>
        </div>
        <div class="header-item header-top1-rate" @click="sortBy('top1_rate')">
          登顶率
          <span v-if="sortColumn === 'top1_rate'" class="sort-indicator">
            {{ sortOrder === 'asc' ? '▲' : '▼' }}
          </span>
        </div>
      </div>

      <!-- 装备列表 -->
      <div class="item-list-content">
        <div 
          v-for="item in sortedItems" 
          :key="item.cn_name"
          class="item-row"
          @click="handleItemClick(item)"
        >
          <!-- 装备图标 -->
          <div class="item-cell item-icon-cell">
            <ItemIcon
              :item-name="item.cn_name || '未知装备'"
              :icon-path="item.icon_path"
              :tier="item.tier"
              :size="36"
              :show-tier-badge="false"
              :clickable="false"
            />
          </div>
          
          <!-- 装备名称 -->
          <div
            class="item-cell item-name-cell"
            @mouseenter="handleItemNameMouseEnter(item.cn_name || '未知装备', $event)"
            @mouseleave="handleItemNameMouseLeave"
            @mousemove="handleItemNameMouseMove"
          >
            {{ item.cn_name || '未知装备' }}
          </div>
          
          <!-- 评级 -->
          <div class="item-cell item-tier-cell">
            <div 
              class="tier-badge"
              :class="getTierBadgeClass(item.tier)"
            >
              {{ item.tier || '-' }}
            </div>
          </div>
          
          <!-- 出场率 -->
          <div class="item-cell item-play-rate-cell">
            <span :style="{ color: getStatColor(item.play_rate, 'playRate') }">
              {{ formatStat(item.play_rate, 'playRate') }}%
            </span>
          </div>
          
          <!-- 平均排名 -->
          <div class="item-cell item-avg-place-cell">
            <span :style="{ color: getStatColor(item.avg_place, 'avgPlace') }">
              {{ formatStat(item.avg_place, 'avgPlace') }}
            </span>
          </div>
          
          <!-- 登顶率 -->
          <div class="item-cell item-top1-rate-cell">
            <span :style="{ color: getStatColor(item.top1_rate, 'top1Rate') }">
              {{ formatStat(item.top1_rate, 'top1Rate') }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 装备名称悬停提示框 -->
    <Teleport to="body">
      <div
        v-if="showItemNameTooltip && hoveredItemName"
        class="item-name-tooltip"
        :style="{
          left: itemNameTooltipPosition.x + 'px',
          top: itemNameTooltipPosition.y + 'px'
        }"
      >
        {{ hoveredItemName }}
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import ItemIcon from '@/components/common/ItemIcon.vue'
import { useData } from '@/composables/useData'
import { useDataStore } from '@/stores/data'

// === 数据组合式函数 ===
const { getItemList } = useData()

// === 装备数据接口 ===
interface ItemData {
  cn_name: string
  tier: string
  play_rate: number
  avg_place: number
  top1_rate: number
  icon_path?: string
  categories?: string[]
  [key: string]: any
}

interface CategoryData {
  category_name: string
}

// === 装备数据状态 ===
const allItems = ref<ItemData[]>([])
const allCategories = ref<CategoryData[]>([])
const isLoading = ref(true)
const searchQuery = ref('')
const selectedCategories = ref<string[]>(['all'])

// 排序状态
const sortColumn = ref<string>('play_rate')
const sortOrder = ref<'asc' | 'desc'>('desc')

// === 装备类型筛选配置 ===
const categoryFilters = computed(() => {
  // 固定顺序的类别配置
  const orderedCategories = [
    { label: '全部', value: 'all' },
    { label: '普通装备', value: '普通装备' },
    { label: '奥恩神器', value: '奥恩神器' },
    { label: '辅助装备', value: '辅助装备' },
    { label: '光明装备', value: '光明装备' },
    { label: '纹章转职', value: '纹章转职' },
    { label: '其他装备', value: '其他装备' }
  ]
  
  // 获取实际存在的类别
  const existingCategories = new Set()
  allItems.value.forEach(item => {
    if (item.categories && Array.isArray(item.categories)) {
      item.categories.forEach(cat => existingCategories.add(cat))
    }
  })
  
  console.log('现有类别:', Array.from(existingCategories))
  
  // 返回存在的类别，如果没有数据则显示所有类别
  if (existingCategories.size === 0) {
    return orderedCategories // 数据未加载时显示所有选项
  }
  
  return orderedCategories.filter(category => 
    category.value === 'all' || existingCategories.has(category.value)
  )
})

// === 计算属性 ===
const filteredItems = computed(() => {
  let items = allItems.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    items = items.filter(item => item.cn_name?.toLowerCase().includes(query))
  }

  // 类别过滤
  if (!selectedCategories.value.includes('all') && selectedCategories.value.length > 0) {
    items = items.filter(item => {
      if (!item.categories || item.categories.length === 0) return false
      return selectedCategories.value.some(selectedCategory => 
        item.categories!.includes(selectedCategory)
      )
    })
  }

  return items
})

// 排序后的装备列表
const sortedItems = computed(() => {
  const items = [...filteredItems.value]
  
  items.sort((a, b) => {
    let aValue = a[sortColumn.value]
    let bValue = b[sortColumn.value]
    
    // 特殊处理评级排序
    if (sortColumn.value === 'tier') {
      const tierOrder = { 'S': 1, 'A': 2, 'B': 3, 'C': 4, 'D': 5 }
      aValue = tierOrder[aValue as keyof typeof tierOrder] || 6
      bValue = tierOrder[bValue as keyof typeof tierOrder] || 6
    }
    
    // 处理字符串排序
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder.value === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }
    
    // 处理数字排序
    if (sortOrder.value === 'asc') {
      return (aValue || 0) - (bValue || 0)
    } else {
      return (bValue || 0) - (aValue || 0)
    }
  })
  
  return items
})

// === 类别筛选功能 ===
const toggleCategoryFilter = (category: string) => {
  if (category === 'all') {
    selectedCategories.value = ['all']
  } else {
    const index = selectedCategories.value.indexOf(category)
    if (index > -1) {
      selectedCategories.value.splice(index, 1)
      if (selectedCategories.value.length === 0) {
        selectedCategories.value = ['all']
      }
    } else {
      const allIndex = selectedCategories.value.indexOf('all')
      if (allIndex > -1) {
        selectedCategories.value.splice(allIndex, 1)
      }
      selectedCategories.value.push(category)
    }
  }
  
  console.log('当前选择的类别:', selectedCategories.value)
}

// === 排序功能 ===
const sortBy = (column: string) => {
  if (sortColumn.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = column
    // 设置默认排序方向
    if (column === 'avg_place') {
      sortOrder.value = 'asc' // 平均排名越小越好
    } else if (column === 'tier') {
      sortOrder.value = 'asc' // 评级从S到D（升序）
    } else {
      sortOrder.value = 'desc' // 其他指标越大越好
    }
  }
}

// === 样式辅助函数 ===
const getTierBadgeClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s-badge',
    'A': 'tier-a-badge',
    'B': 'tier-b-badge',
    'C': 'tier-c-badge',
    'D': 'tier-d-badge'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-default-badge'
}

// === 数据格式化函数 ===
const formatStat = (value: number, type: 'playRate' | 'avgPlace' | 'top1Rate'): string => {
  if (!value && value !== 0) return '--'
  
  if (type === 'avgPlace') {
    return value.toFixed(2)
  } else {
    return value.toFixed(1)
  }
}

const getStatColor = (value: number, type: 'playRate' | 'avgPlace' | 'top1Rate'): string => {
  // 取消颜色显示，统一返回默认文字颜色
  return 'rgba(255, 255, 255, 0.9)'
}

// === 装备点击处理 ===
const handleItemClick = (item: any) => {
  console.log('点击装备:', item.cn_name)
  emit('itemClick', item.cn_name)
}

// === 装备名称悬停提示 ===
const showItemNameTooltip = ref(false)
const hoveredItemName = ref('')
const itemNameTooltipPosition = ref({ x: 0, y: 0 })

const handleItemNameMouseEnter = (itemName: string, event: MouseEvent) => {
  hoveredItemName.value = itemName
  updateItemNameTooltipPosition(event)
  showItemNameTooltip.value = true
}

const handleItemNameMouseLeave = () => {
  showItemNameTooltip.value = false
  hoveredItemName.value = ''
}

const handleItemNameMouseMove = (event: MouseEvent) => {
  if (showItemNameTooltip.value) {
    updateItemNameTooltipPosition(event)
  }
}

const updateItemNameTooltipPosition = (event: MouseEvent) => {
  const windowWidth = window.innerWidth
  const tooltipWidth = 200 // 提示框的大概宽度

  // 如果鼠标在屏幕右半部分，则将提示框显示在左侧
  const showOnLeft = event.clientX > windowWidth / 2

  itemNameTooltipPosition.value = {
    x: showOnLeft ? event.clientX - tooltipWidth - 10 : event.clientX + 10,
    y: event.clientY - 35
  }
}

// Emits
const emit = defineEmits<{
  itemClick: [itemName: string]
}>()

// === 数据加载 ===
const loadItems = async () => {
  console.log('🔍 开始加载装备数据...')

  try {
    // 先尝试同步获取缓存数据，避免设置loading状态
    const dataStore = useDataStore()
    const cachedItems = dataStore.getCachedQuery('item_list')

    if (cachedItems && cachedItems.length > 0) {
      console.log('✅ 从缓存即时加载装备数据，零延迟')
      allItems.value = cachedItems
      // 为缓存数据加载类别信息
      await loadItemCategories()
      return
    }

    // 缓存未命中时才显示loading
    console.log('缓存未命中，显示loading并从数据库加载...')
    isLoading.value = true

    const itemResults = await invoke('get_item_list')
    
    console.log('🛡️ 装备数据加载结果:', itemResults)
    
    // 处理装备数据
    if (itemResults && typeof itemResults === 'object' && 'data' in itemResults) {
      if ('error' in itemResults && itemResults.error) {
        console.error('❌ 装备数据查询错误:', itemResults.error)
        allItems.value = []
      } else if (Array.isArray(itemResults.data)) {
        allItems.value = itemResults.data
        console.log(`✅ 成功加载 ${allItems.value.length} 个装备`)
      } else {
        console.warn('⚠️ 装备数据格式不正确:', itemResults.data)
        allItems.value = []
      }
    } else if (Array.isArray(itemResults)) {
      allItems.value = itemResults
      console.log(`✅ 成功加载 ${allItems.value.length} 个装备`)
    } else {
      console.warn('⚠️ 装备数据格式不正确:', itemResults)
      allItems.value = []
    }
    
    // 为每个装备加载其类别信息
    await loadItemCategories()
    
  } catch (error) {
    console.error('❌ 加载装备数据失败:', error)
    allItems.value = []
    allCategories.value = []
  } finally {
    isLoading.value = false
  }
}

// 加载装备的类别信息
const loadItemCategories = async () => {
  try {
    console.log('🔍 开始加载装备类别信息...')
    
    // 批量处理，每次处理10个装备，避免过多并发请求
    const batchSize = 10
    for (let i = 0; i < allItems.value.length; i += batchSize) {
      const batch = allItems.value.slice(i, i + batchSize)
      
      const categoryPromises = batch.map(async (item) => {
        try {
          // 使用装备详情API获取类别信息
          const itemDetail = await invoke('get_item_detail', { itemName: item.cn_name })
          
          if (itemDetail && typeof itemDetail === 'object' && 'data' in itemDetail) {
            const data = itemDetail.data
            if (Array.isArray(data) && data.length > 0) {
              const categories = data[0].categories
              if (categories && typeof categories === 'string') {
                // 类别以逗号分隔的字符串形式存储
                item.categories = categories.split(',').map(cat => cat.trim()).filter(cat => cat)
              } else {
                item.categories = ['普通装备'] // 默认类别
              }
            } else {
              item.categories = ['普通装备']
            }
          } else {
            item.categories = ['普通装备']
          }
        } catch (error) {
          console.warn(`获取装备 ${item.cn_name} 的类别信息失败:`, error)
          item.categories = ['普通装备']
        }
      })
      
      await Promise.all(categoryPromises)
      console.log(`✅ 已处理 ${Math.min(i + batchSize, allItems.value.length)}/${allItems.value.length} 个装备的类别信息`)
    }
    
    console.log('✅ 装备类别信息加载完成')
    
    // 统计类别信息
    const categoryStats = new Map()
    allItems.value.forEach(item => {
      if (item.categories && Array.isArray(item.categories)) {
        item.categories.forEach(cat => {
          categoryStats.set(cat, (categoryStats.get(cat) || 0) + 1)
        })
      }
    })
    console.log('📊 类别统计:', Object.fromEntries(categoryStats))
    
  } catch (error) {
    console.error('加载装备类别信息失败:', error)
  }
}

// === 组件挂载时加载数据 ===
onMounted(() => {
  loadItems()
})
</script>

<style scoped>
/* === 装备页面 === */
.item-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
}

/* === 搜索区域 === */
.search-section {
  flex-shrink: 0;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* === 筛选区域 === */
.filter-section {
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.category-filters {
  display: flex;
  gap: 0.375rem;
  flex-wrap: wrap;
  justify-content: center;
}

.category-filter-button {
  padding: 0.375rem 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  min-width: fit-content;
}

.category-filter-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.category-filter-button.active {
  background: linear-gradient(135deg, rgba(100, 200, 255, 0.8), rgba(50, 150, 255, 0.8));
  border-color: rgba(100, 200, 255, 0.8);
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(100, 200, 255, 0.4);
}

/* === 装备列表区域（移除外层滚动） === */

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 暂无数据状态 === */
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-data-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.no-data-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 装备列表容器 === */
.item-list-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* === 表头 === */
.item-list-header {
  display: grid;
  grid-template-columns: 50px 2.5fr 55px 75px 85px 75px;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  align-items: center;
}

.header-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  justify-content: center;
}

.header-name {
  justify-content: flex-start !important;
}

.header-item:hover {
  color: white;
}

.header-icon {
  cursor: default !important;
}

.sort-indicator {
  margin-left: 0.25rem;
  font-size: 10px;
  color: rgba(100, 200, 255, 0.8);
}

/* === 装备列表内容 === */
.item-list-content {
  flex: 1;
  overflow-y: auto;
}

.item-list-content::-webkit-scrollbar {
  width: 6px;
}

.item-list-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.item-list-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.item-list-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* === 装备行 === */
.item-row {
  display: grid;
  grid-template-columns: 50px 2.5fr 55px 75px 85px 75px;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
  min-height: 50px;
}

.item-row:hover {
  background: rgba(255, 255, 255, 0.08);
}

.item-row:last-child {
  border-bottom: none;
}

/* === 装备单元格 === */
.item-cell {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  justify-content: center;
}

.item-icon-cell {
  justify-content: center;
}

.item-name-cell {
  font-weight: 500;
  color: white;
  justify-content: flex-start;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-tier-cell {
  justify-content: center;
}

.item-play-rate-cell,
.item-avg-place-cell,
.item-top1-rate-cell {
  justify-content: center;
  font-weight: 500;
}

/* === 评级徽章 === */
.tier-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  min-width: 24px;
  text-align: center;
}

.tier-s-badge { 
  background: linear-gradient(135deg, #fc1236, #ff6b6b);
  box-shadow: 0 2px 8px rgba(252, 18, 54, 0.3);
}

.tier-a-badge { 
  background: linear-gradient(135deg, #ffa726, #ff8f00);
  box-shadow: 0 2px 8px rgba(255, 167, 38, 0.3);
}

.tier-b-badge { 
  background: linear-gradient(135deg, #ffeb3b, #ffc107);
  box-shadow: 0 2px 8px rgba(255, 235, 59, 0.3);
  color: #333;
}

.tier-c-badge { 
  background: linear-gradient(135deg, #cddc39, #8bc34a);
  box-shadow: 0 2px 8px rgba(205, 220, 57, 0.3);
  color: #333;
}

.tier-d-badge { 
  background: linear-gradient(135deg, #4caf50, #2e7d32);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.tier-default-badge {
  background: linear-gradient(135deg, #666666, #444444);
  box-shadow: 0 2px 8px rgba(102, 102, 102, 0.3);
}

/* === 装备名称悬停提示框 === */
.item-name-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  z-index: 9999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  animation: tooltipFadeIn 0.15s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-3px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>