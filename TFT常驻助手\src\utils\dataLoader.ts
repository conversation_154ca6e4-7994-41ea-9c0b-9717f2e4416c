import { useDataStore } from '@/stores/data';

/**
 * 数据加载工具类
 * 提供应用启动时的数据预加载功能
 */
export class DataLoader {
  private static instance: DataLoader;
  private dataStore = useDataStore();
  private initPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): DataLoader {
    if (!DataLoader.instance) {
      DataLoader.instance = new DataLoader();
    }
    return DataLoader.instance;
  }

  /**
   * 初始化数据加载
   * 确保只执行一次预加载
   */
  public async initialize(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.performInitialization();
    return this.initPromise;
  }

  /**
   * 执行实际的初始化操作
   */
  private async performInitialization(): Promise<void> {
    try {
      console.log('开始初始化数据加载器...');
      
      // 预加载全局数据
      await this.dataStore.preloadGlobalData();
      
      console.log('数据加载器初始化完成');
    } catch (error) {
      console.error('数据加载器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据是否已准备就绪
   */
  public isReady(): boolean {
    return this.dataStore.isDataReady;
  }

  /**
   * 获取加载状态
   */
  public getLoadingStatus() {
    return this.dataStore.getCacheStatus();
  }

  /**
   * 重新加载数据
   */
  public async reload(): Promise<void> {
    console.log('重新加载数据...');
    this.dataStore.resetCache();
    this.initPromise = null;
    await this.initialize();
  }
}

/**
 * 导出单例实例
 */
export const dataLoader = DataLoader.getInstance();