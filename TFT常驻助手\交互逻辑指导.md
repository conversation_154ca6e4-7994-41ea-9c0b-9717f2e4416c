# TFT常驻助手 交互逻辑指导

## 概述

本文档基于对现有代码的深入分析，详细描述TFT助手的交互逻辑、数据流、状态管理和用户操作流程，为重构实现提供准确的逻辑指导。

## 核心架构分析

### 1. 应用状态管理架构

#### 1.1 主应用状态 (useAppStore)
```typescript
// 核心状态
currentPage: number           // 当前页面索引 (0-6)
isMinimized: boolean         // 窗口是否收起
historyStack: NavigationHistoryItem[]  // 导航历史栈
viewsLoaded: Record<number, boolean>   // 视图加载状态

// 页面常量
PAGE_COMP_LIST: 0      // 阵容列表
PAGE_HERO_LIST: 1      // 英雄列表  
PAGE_ITEM_LIST: 2      // 装备列表
PAGE_HEX_LIST: 3       // 海克斯列表
PAGE_COMP_DETAIL: 4    // 阵容详情
PAGE_HERO_DETAIL: 5    // 英雄详情
PAGE_ITEM_DETAIL: 6    // 装备详情
```

#### 1.2 数据缓存状态 (useDataStore)
```typescript
// 全局数据缓存
globalHeroInfoMap: Record<string, HeroInfo>    // 英雄信息映射
globalTraitIconMap: Record<string, string>     // 羁绊图标映射
globalItemInfoMap: Record<string, ItemInfo>    // 装备信息映射

// 查询缓存
queryCache: Record<string, any>                // 查询结果缓存

// 加载状态
isGlobalDataLoaded: boolean                    // 全局数据是否已加载
isPreloading: boolean                          // 是否正在预加载
```

### 2. 窗口管理逻辑

#### 2.1 窗口状态切换
```rust
// Tauri后端窗口管理
toggle_window_size()          // 切换窗口大小（展开/收起）
start_drag()                  // 开始拖拽窗口
close_app()                   // 关闭应用
set_always_on_top()           // 设置置顶状态
```

#### 2.2 窗口配置
```json
// tauri.conf.json 窗口配置
width: 580, height: 700       // 默认尺寸
minWidth: 580, minHeight: 42  // 最小尺寸（收起时高度42px）
decorations: false            // 无边框窗口
alwaysOnTop: true            // 始终置顶
```

#### 2.3 窗口交互流程
```
用户点击展开/收起按钮
    ↓
MainWindow.handleToggleWindow()
    ↓
调用 invoke('toggle_window_size')
    ↓
Rust后端执行窗口尺寸切换
    ↓
前端更新 isMinimized 状态
    ↓
UI响应状态变化（显示/隐藏内容区域）
```

### 3. 导航系统逻辑

#### 3.1 页面切换机制
```typescript
// 主页面切换
switchMainPage(pageIndex: number) {
  if (pageIndex === currentPage.value) return;
  
  // 清空历史记录（主页面间切换）
  historyStack.value = [];
  currentPage.value = pageIndex;
  
  // 标记视图需要加载
  if (!viewsLoaded.value[pageIndex]) {
    markViewLoaded(pageIndex);
  }
}
```

#### 3.2 详情页导航
```typescript
// 导航到详情页
navigateToDetail(detailPageIndex: number, dataKey: string) {
  // 记录当前页面到历史栈
  recordNavigation(currentPage.value, dataKey);
  
  // 切换到详情页
  currentPage.value = detailPageIndex;
  
  // 触发数据加载
  loadDetailData(dataKey);
}
```

#### 3.3 返回逻辑
```typescript
// 返回上一页
goBack() {
  if (historyStack.value.length === 0) return;
  
  const lastItem = historyStack.value.pop();
  currentPage.value = lastItem.pageIndex;
  
  // 如果返回到详情页，需要恢复数据
  if (lastItem.dataKey) {
    loadDetailData(lastItem.dataKey);
  }
}
```

### 4. 数据库操作逻辑

#### 4.1 查询缓存机制
```rust
// Rust后端缓存逻辑
pub async fn execute_query(
    sql: &str,
    params: Vec<String>,
    cache_key: Option<String>,
) -> QueryResult {
    // 1. 检查缓存
    if let Some(cached) = get_cached_result(cache_key).await {
        return cached;
    }
    
    // 2. 执行查询
    let result = perform_database_query(sql, params).await;
    
    // 3. 缓存结果
    if result.error.is_none() {
        cache_result(cache_key, result.clone()).await;
    }
    
    result
}
```

#### 4.2 异步查询流程
```
前端发起查询请求
    ↓
invoke('get_comp_list') 等Tauri命令
    ↓
Rust后端检查缓存
    ↓
缓存命中 → 直接返回 | 缓存未命中 → 执行SQL查询
    ↓
查询结果缓存到内存
    ↓
返回结果给前端
    ↓
前端更新UI状态
```

#### 4.3 数据预加载策略
```typescript
// 应用启动时预加载关键数据
async preloadGlobalData() {
  isPreloading.value = true;
  
  try {
    // 并行加载全局数据
    const [heroData, itemData, traitData] = await Promise.all([
      invoke('get_hero_list'),
      invoke('get_item_list'), 
      invoke('get_trait_list')
    ]);
    
    // 构建映射表
    buildGlobalMaps(heroData, itemData, traitData);
    isGlobalDataLoaded.value = true;
  } finally {
    isPreloading.value = false;
  }
}
```

## 具体页面交互逻辑

### 5. 阵容列表页面

#### 5.1 数据加载流程
```python
# 原始Python逻辑
def load_data(self):
    # 1. 加载阵容基础数据
    sql_base = "SELECT name, tier, frequency, comp_id FROM comps_base ORDER BY tier, CAST(comp_id AS INTEGER) ASC"
    execute_query_async(sql_base, on_success=self.on_base_data_loaded)
    
    # 2. 加载阵容英雄关联
    sql_heroes = "SELECT comp_name, hero_cn_name FROM comp_heroes ORDER BY comp_name, hero_order ASC"
    execute_query_async(sql_heroes, on_success=self.on_hero_map_loaded)
```

#### 5.2 搜索过滤逻辑
```python
def filter_comps(self, search_term):
    search_term = search_term.lower().strip()
    
    if not search_term:
        # 显示所有阵容
        self.filtered_comp_data = copy.deepcopy(self.comp_data)
    else:
        # 多维度搜索：阵容名称、英雄名称、羁绊名称
        for tier, comp_list in self.comp_data.items():
            filtered_list = []
            for comp in comp_list:
                # 阵容名称匹配
                if search_term in comp['name'].lower():
                    filtered_list.append(comp)
                    continue
                    
                # 英雄名称匹配
                heroes = self.comp_hero_map.get(comp['name'], [])
                if any(search_term in hero.lower() for hero in heroes):
                    filtered_list.append(comp)
                    continue
                    
                # 羁绊名称匹配
                traits = self.comp_trait_map.get(comp['name'], [])
                if any(search_term in trait.lower() for trait in traits):
                    filtered_list.append(comp)
```

#### 5.3 阵容行点击交互
```python
def on_comp_clicked(self, comp_name):
    # 发射信号给主窗口
    self.comp_selected.emit(comp_name)
    
# 主窗口处理
def on_comp_selected_from_list(self, comp_name):
    # 导航到阵容详情页
    self.navigate_to_detail(self.PAGE_COMP_DETAIL, comp_name)
```

### 6. 英雄列表页面

#### 6.1 费用筛选逻辑
```python
def init_cost_filter_ui(self):
    # 创建费用筛选按钮组
    cost_buttons = ["全部", "1费", "2费", "3费", "4费", "5费"]
    
    for i, text in enumerate(cost_buttons):
        button = QPushButton(text)
        button.clicked.connect(lambda checked, cost=i-1: self.filter_by_cost(cost))
        # cost=-1表示显示全部
```

#### 6.2 英雄卡片交互
```python
def create_hero_card(self, hero_data):
    card = QWidget()
    
    # 英雄头像点击
    hero_icon = IconLabel(icon_size=ICON_SIZE_LARGE)
    hero_icon.clicked.connect(lambda: self.hero_selected.emit(hero_data['cn_name']))
    
    # 显示统计信息
    play_rate_label = QLabel(f"出场率: {hero_data['play_rate']:.2f}")
    avg_place_label = QLabel(f"均名: {hero_data['avg_place']:.2f}")
```

#### 6.3 费用分组显示
```python
def update_hero_display(self):
    # 按费用分组显示英雄
    for cost in [1, 2, 3, 4, 5]:
        if cost in self.filtered_hero_data:
            # 创建费用分组
            group_widget = self.create_cost_group(cost, self.filtered_hero_data[cost])
            self.content_layout.addWidget(group_widget)
```

### 7. 详情页面交互

#### 7.1 阵容详情页加载
```python
def load_comp_data(self, comp_name):
    # 重置加载状态
    self.loading_tasks = {
        "base_data": False,
        "heroes": False,
        "level_recs": False,
        "unit_stats": False,
        "item_stats": False,
        "item_unit_stats": False
    }

    # 并行加载多个数据源
    self.load_base_data(comp_name)
    self.load_hero_data(comp_name)
    self.load_level_recommendations(comp_name)
    self.load_unit_stats(comp_name)
    self.load_item_stats(comp_name)
```

#### 7.2 数据加载完成检查
```python
def check_loading_complete(self):
    # 检查所有数据是否加载完成
    if all(self.loading_tasks.values()):
        self.loading_finished = True
        self.show_loading_mask(False)
        self.update_all_ui()
```

#### 7.3 详情页内跳转
```python
# 英雄图标点击 → 跳转英雄详情
def on_hero_icon_clicked(self, hero_name):
    self.hero_selected_from_detail.emit(hero_name)

# 装备图标点击 → 跳转装备详情
def on_item_icon_clicked(self, item_name):
    self.item_selected_from_detail.emit(item_name)
```

## 性能优化策略

### 8. 内存管理

#### 8.1 视图卸载机制
```python
# 常量与配置.py
ENABLE_VIEW_UNLOADING = True
VIEW_UNLOAD_DELAY_MS = 15000  # 15秒后卸载
MEMORY_INTENSIVE_VIEWS = [4, 5, 6]  # 详情页面

# 主窗口内存管理
def on_page_changed(self, new_page):
    # 延时卸载不可见的详情页
    if ENABLE_VIEW_UNLOADING:
        QTimer.singleShot(VIEW_UNLOAD_DELAY_MS, self.cleanup_invisible_views)
```

#### 8.2 图标缓存策略
```python
# LRU缓存机制
MAX_ICON_CACHE_SIZE = 300
ICON_CACHE_CLEAN_THRESHOLD = 250
ICON_CACHE_KEEP_COUNT = 150

def manage_icon_cache():
    if len(ICON_CACHE) > ICON_CACHE_CLEAN_THRESHOLD:
        # 保留最近使用的图标
        keep_recent_icons(ICON_CACHE_KEEP_COUNT)
```

#### 8.3 查询缓存管理
```python
# 查询结果缓存
CACHE_EXPIRY_SECONDS = 300  # 5分钟过期
MAX_QUERY_CACHE_SIZE = 100

def check_and_clean_query_cache():
    if len(QUERY_CACHE) > MAX_QUERY_CACHE_SIZE:
        # LRU清理策略
        cleanup_old_cache_entries()
```

### 9. 异步操作优化

#### 9.1 线程池管理
```python
MAX_THREADS = 3  # 限制并发查询数量

def execute_query_async(sql, params, on_success, on_error, query_key):
    # 检查线程池负载
    if thread_pool.activeThreadCount() > MAX_THREADS * 0.8:
        print(f"警告: 线程池负载较高")

    worker = DatabaseWorker(sql, params, query_key)
    thread_pool.start(worker)
```

#### 9.2 数据预加载时机
```typescript
// 应用启动时预加载
onMounted(async () => {
  await dataLoader.initialize();
});

// 页面切换时预加载下一页数据
const preloadNextPageData = (currentPage: number) => {
  const nextPage = getNextLikelyPage(currentPage);
  if (nextPage && !isViewLoaded(nextPage)) {
    preloadPageData(nextPage);
  }
};
```

## 错误处理机制

### 10. 数据库错误处理

#### 10.1 连接失败处理
```rust
pub async fn execute_query() -> QueryResult {
    match self.get_connection() {
        Ok(conn) => {
            // 执行查询
        },
        Err(e) => QueryResult {
            data: vec![],
            error: Some(format!("数据库连接失败: {}", e)),
        }
    }
}
```

#### 10.2 前端错误处理
```typescript
const handleQueryError = (error: string) => {
  console.error('查询失败:', error);

  // 显示用户友好的错误信息
  showErrorMessage('数据加载失败，请稍后重试');

  // 回退到缓存数据或默认状态
  fallbackToCache();
};
```

### 11. UI状态同步

#### 11.1 加载状态管理
```vue
<template>
  <div v-if="isLoading" class="loading-mask">
    <div class="loading-spinner">正在加载...</div>
  </div>

  <div v-else-if="hasError" class="error-state">
    <div class="error-message">{{ errorMessage }}</div>
    <button @click="retry">重试</button>
  </div>

  <div v-else class="content">
    <!-- 正常内容 -->
  </div>
</template>
```

#### 11.2 状态响应式更新
```typescript
// 监听状态变化
watch(currentPage, (newPage, oldPage) => {
  // 更新导航栏选中状态
  updateNavigationState(newPage);

  // 更新返回按钮显示
  updateBackButtonVisibility();

  // 预加载页面数据
  preloadPageData(newPage);
});
```

## 关键交互模式

### 12. 搜索交互模式

#### 12.1 实时搜索实现
```typescript
// 防抖搜索
const debouncedSearch = debounce((searchTerm: string) => {
  performSearch(searchTerm);
}, 300);

// 搜索输入处理
const handleSearchInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  debouncedSearch(target.value);
};
```

#### 12.2 多维度搜索逻辑
```typescript
const performSearch = (term: string) => {
  const normalizedTerm = term.toLowerCase().trim();

  if (!normalizedTerm) {
    // 显示所有结果
    filteredData.value = originalData.value;
    return;
  }

  filteredData.value = originalData.value.filter(item => {
    // 名称匹配
    if (item.name.toLowerCase().includes(normalizedTerm)) return true;

    // 关联数据匹配（英雄、羁绊等）
    if (item.relatedData?.some(data =>
      data.toLowerCase().includes(normalizedTerm)
    )) return true;

    return false;
  });
};
```

### 13. 分页和虚拟滚动

#### 13.1 虚拟滚动实现
```vue
<template>
  <div class="virtual-scroll-container" @scroll="handleScroll">
    <div class="virtual-scroll-spacer" :style="{ height: totalHeight + 'px' }">
      <div
        class="virtual-scroll-content"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <div
          v-for="item in visibleItems"
          :key="item.id"
          class="virtual-scroll-item"
        >
          <!-- 项目内容 -->
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 13.2 滚动性能优化
```typescript
const handleScroll = throttle((event: Event) => {
  const container = event.target as HTMLElement;
  const scrollTop = container.scrollTop;

  // 计算可见区域
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + visibleCount + bufferSize,
    totalItems.value.length
  );

  // 更新可见项目
  visibleItems.value = totalItems.value.slice(startIndex, endIndex);
  offsetY.value = startIndex * itemHeight;
}, 16); // 60fps
```

### 14. 数据同步策略

#### 14.1 乐观更新
```typescript
const updateItemOptimistically = async (itemId: string, newData: any) => {
  // 立即更新UI
  const oldData = updateLocalData(itemId, newData);

  try {
    // 发送到后端
    await invoke('update_item', { itemId, data: newData });
  } catch (error) {
    // 回滚更改
    updateLocalData(itemId, oldData);
    showErrorMessage('更新失败，已恢复原始数据');
  }
};
```

#### 14.2 数据一致性检查
```typescript
const validateDataConsistency = async () => {
  const localVersion = getLocalDataVersion();
  const serverVersion = await invoke('get_data_version');

  if (localVersion !== serverVersion) {
    // 数据不一致，需要重新同步
    await syncDataFromServer();
  }
};
```

## 实施建议

### 15. 开发阶段规划

#### 15.1 第一阶段：基础组件
- SearchInput（搜索功能）
- IconLabel（图标显示）
- TierLabel（评级标签）
- LoadingSpinner（加载状态）

#### 15.2 第二阶段：列表组件
- CompRow（阵容行）
- HeroCard（英雄卡片）
- CostFilter（费用筛选）
- VirtualScroll（虚拟滚动）

#### 15.3 第三阶段：详情组件
- CompDetail（阵容详情）
- HeroDetail（英雄详情）
- ItemDetail（装备详情）
- StatisticsChart（统计图表）

### 16. 测试策略

#### 16.1 单元测试重点
```typescript
// 状态管理测试
describe('AppStore', () => {
  it('should handle page navigation correctly', () => {
    const store = useAppStore();
    store.switchMainPage(1);
    expect(store.currentPage).toBe(1);
    expect(store.historyStack).toEqual([]);
  });
});

// 搜索功能测试
describe('Search', () => {
  it('should filter results correctly', () => {
    const results = performSearch('测试', mockData);
    expect(results).toHaveLength(2);
  });
});
```

#### 16.2 集成测试重点
- 页面导航流程
- 数据加载和缓存
- 错误处理和恢复
- 性能基准测试

这个交互逻辑指导文档详细描述了应用的核心交互机制，为重构实现提供了完整的技术蓝图。
