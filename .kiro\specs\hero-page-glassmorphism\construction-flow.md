# 英雄页面由外向内构建流程

## 构建层级图

```
第1层：基础容器 (需求1-3)
├── 渐变背景
├── 主窗口容器
└── 控制栏框架

第2层：窗口控制 (需求4-6)
├── 展开收起功能
├── 关闭按钮
└── 拖拽功能

第3层：导航结构 (需求7-8)
├── 导航栏容器
└── "英雄"标识

第4层：内容框架 (需求9-12)
├── 内容区域容器
├── 搜索框区域
├── 费用筛选区域
└── 英雄列表容器

第5层：分组结构 (需求13-16)
├── 费用分组容器
├── 费用标签组件
└── 英雄网格布局

第6层：卡片结构 (需求17-22)
├── 英雄卡片基础
├── 头像区域
├── 名称显示
├── 统计信息
├── 费用徽章
└── 交互效果

第7层：数据填充 (需求23-24)
├── 真实数据加载
└── 搜索筛选功能
```

## 详细构建步骤

### 阶段1：建立基础框架 (需求1-8)

#### 步骤1.1：清空现有UI
```vue
<template>
  <!-- 完全空白的页面，只有渐变背景 -->
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400">
  </div>
</template>
```

#### 步骤1.2：添加主窗口容器
```vue
<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400 flex items-center justify-center">
    <!-- Glassmorphism主窗口 -->
    <div class="w-[900px] h-[600px] bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl">
    </div>
  </div>
</template>
```

#### 步骤1.3：添加控制栏
```vue
<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400 flex items-center justify-center">
    <div class="w-[900px] h-[600px] bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl">
      <!-- 控制栏 -->
      <div class="h-12 flex items-center justify-between px-4 border-b border-white/10">
        <div class="w-8"></div> <!-- 返回按钮占位 -->
        <div class="flex-1 text-center">
          <button class="text-white/80 hover:text-white">⌄</button>
        </div>
        <button class="w-8 h-8 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded">×</button>
      </div>
    </div>
  </div>
</template>
```

#### 步骤1.4：实现展开收起功能
```vue
<script setup>
const isMinimized = ref(false)

const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400 flex items-center justify-center">
    <div 
      class="w-[900px] bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 shadow-2xl transition-all duration-300"
      :class="isMinimized ? 'h-12' : 'h-[600px]'"
    >
      <!-- 控制栏 -->
      <div class="h-12 flex items-center justify-between px-4 border-b border-white/10">
        <div class="w-8"></div>
        <div class="flex-1 text-center">
          <button @click="toggleMinimize" class="text-white/80 hover:text-white">
            {{ isMinimized ? '⌃' : '⌄' }}
          </button>
        </div>
        <button class="w-8 h-8 text-red-400 hover:text-red-300 hover:bg-red-500/20 rounded">×</button>
      </div>
      
      <!-- 内容区域（收起时隐藏） -->
      <div v-show="!isMinimized" class="flex-1">
        <!-- 后续内容将在这里添加 -->
      </div>
    </div>
  </div>
</template>
```

#### 步骤1.5：添加导航栏
```vue
<!-- 在内容区域内添加导航栏 -->
<div v-show="!isMinimized" class="flex-1">
  <!-- 导航栏 -->
  <div class="h-12 flex border-b border-white/10">
    <button 
      v-for="tab in ['阵容', '英雄', '装备', '海克斯']" 
      :key="tab"
      class="flex-1 text-white/80 hover:text-white hover:bg-white/10 transition-colors"
      :class="{ 'bg-white/20 text-white': tab === '英雄' }"
    >
      {{ tab }}
    </button>
  </div>
</div>
```

### 阶段2：构建内容框架 (需求9-12)

#### 步骤2.1：添加内容区域容器
```vue
<!-- 在导航栏下方添加内容区域 -->
<div class="flex-1 p-6">
  <!-- 内容将在这里添加 -->
</div>
```

#### 步骤2.2：添加搜索框
```vue
<div class="flex-1 p-6 space-y-4">
  <!-- 搜索框 -->
  <div class="relative">
    <input 
      type="text" 
      placeholder="搜索英雄名称..."
      class="w-full px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400"
    />
  </div>
</div>
```

#### 步骤2.3：添加费用筛选
```vue
<div class="flex-1 p-6 space-y-4">
  <!-- 搜索框 -->
  <div class="relative">...</div>
  
  <!-- 费用筛选 -->
  <div class="flex gap-2">
    <button 
      v-for="cost in ['全部', '1费', '2费', '3费', '4费', '5费']"
      :key="cost"
      class="px-3 py-1 text-sm bg-white/10 backdrop-blur-sm border border-white/20 rounded text-white/80 hover:text-white hover:bg-white/20 transition-colors"
      :class="{ 'bg-white/30 text-white': cost === '全部' }"
    >
      {{ cost }}
    </button>
  </div>
</div>
```

#### 步骤2.4：添加英雄列表容器
```vue
<div class="flex-1 p-6 space-y-4">
  <!-- 搜索框和筛选 -->
  ...
  
  <!-- 英雄列表容器 -->
  <div class="flex-1 overflow-y-auto">
    <!-- 英雄分组将在这里添加 -->
  </div>
</div>
```

### 阶段3：构建分组结构 (需求13-16)

#### 步骤3.1：添加费用分组容器
```vue
<!-- 在英雄列表容器内 -->
<div class="flex-1 overflow-y-auto space-y-4">
  <!-- 1费分组 -->
  <div class="flex bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
    <!-- 分组内容将在这里添加 -->
  </div>
  
  <!-- 2费分组 -->
  <div class="flex bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
    <!-- 分组内容将在这里添加 -->
  </div>
  
  <!-- 其他费用分组... -->
</div>
```

#### 步骤3.2：添加费用标签
```vue
<!-- 在每个分组内 -->
<div class="flex bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
  <!-- 费用标签 -->
  <div class="w-12 flex items-center justify-center bg-gray-400 text-white font-bold text-lg rounded-l-lg">
    1
  </div>
  
  <!-- 英雄网格区域 -->
  <div class="flex-1 p-4">
    <!-- 网格将在这里添加 -->
  </div>
</div>
```

#### 步骤3.3：添加英雄网格布局
```vue
<!-- 在英雄网格区域内 -->
<div class="flex-1 p-4">
  <div class="grid grid-cols-5 gap-4">
    <!-- 英雄卡片占位符 -->
    <div v-for="i in 5" :key="i" class="aspect-square bg-white/10 rounded-lg">
      <!-- 卡片内容将在这里添加 -->
    </div>
  </div>
</div>
```

### 阶段4：构建卡片结构 (需求17-22)

#### 步骤4.1：创建英雄卡片基础
```vue
<!-- 替换占位符 -->
<div class="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-3 hover:bg-white/20 transition-all cursor-pointer">
  <!-- 卡片内容将在这里添加 -->
</div>
```

#### 步骤4.2：添加头像区域
```vue
<div class="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-3 hover:bg-white/20 transition-all cursor-pointer">
  <!-- 头像区域 -->
  <div class="relative mb-2">
    <div class="w-16 h-16 bg-white/20 rounded-lg mx-auto flex items-center justify-center">
      <span class="text-white/60">?</span>
    </div>
  </div>
</div>
```

#### 步骤4.3：添加费用徽章
```vue
<div class="relative mb-2">
  <div class="w-16 h-16 bg-white/20 rounded-lg mx-auto flex items-center justify-center">
    <span class="text-white/60">?</span>
  </div>
  <!-- 费用徽章 -->
  <div class="absolute -top-1 -right-1 w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center text-xs text-white font-bold">
    1
  </div>
</div>
```

#### 步骤4.4：添加名称和统计信息
```vue
<div class="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-3 hover:bg-white/20 transition-all cursor-pointer">
  <!-- 头像区域 -->
  <div class="relative mb-2">...</div>
  
  <!-- 英雄名称 -->
  <div class="text-center mb-1">
    <p class="text-white text-sm font-medium">英雄名称</p>
  </div>
  
  <!-- 统计信息 -->
  <div class="text-center space-y-1">
    <p class="text-white/60 text-xs">出场率: 0.67</p>
    <p class="text-white/60 text-xs">均名: 4.38</p>
  </div>
</div>
```

### 阶段5：数据填充和功能实现 (需求23-24)

#### 步骤5.1：集成真实数据
```vue
<script setup>
import { useHeroesStore } from '@/stores/heroes'

const heroesStore = useHeroesStore()

onMounted(() => {
  heroesStore.loadHeroes()
})
</script>

<template>
  <!-- 使用真实数据渲染 -->
  <div v-for="cost in [1,2,3,4,5]" :key="cost" class="flex bg-white/5 backdrop-blur-sm rounded-lg border border-white/10">
    <div class="w-12 flex items-center justify-center text-white font-bold text-lg rounded-l-lg"
         :class="getCostColor(cost)">
      {{ cost }}
    </div>
    
    <div class="flex-1 p-4">
      <div class="grid grid-cols-5 gap-4">
        <div 
          v-for="hero in getHeroesByCost(cost)" 
          :key="hero.id"
          class="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-3 hover:bg-white/20 transition-all cursor-pointer"
        >
          <!-- 真实英雄数据 -->
          <div class="relative mb-2">
            <img :src="hero.iconPath" :alt="hero.name" class="w-16 h-16 rounded-lg mx-auto" />
            <div class="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs text-white font-bold"
                 :class="getCostColor(hero.cost)">
              {{ hero.cost }}
            </div>
          </div>
          
          <div class="text-center mb-1">
            <p class="text-white text-sm font-medium">{{ hero.name }}</p>
          </div>
          
          <div class="text-center space-y-1">
            <p class="text-white/60 text-xs">出场率: {{ hero.playRate.toFixed(2) }}</p>
            <p class="text-white/60 text-xs">均名: {{ hero.avgPlace.toFixed(2) }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 步骤5.2：实现搜索和筛选
```vue
<script setup>
const searchQuery = ref('')
const selectedCost = ref('全部')

const filteredHeroes = computed(() => {
  return heroesStore.heroes.filter(hero => {
    const matchesSearch = hero.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesCost = selectedCost.value === '全部' || hero.cost.toString() === selectedCost.value.replace('费', '')
    return matchesSearch && matchesCost
  })
})
</script>
```

## 构建验证检查点

每完成一个阶段，都应该验证：

1. **视觉效果**：Glassmorphism效果是否正确
2. **响应性**：布局是否适配不同屏幕尺寸
3. **交互性**：按钮和卡片的交互是否流畅
4. **功能性**：每个功能是否按预期工作
5. **性能**：页面渲染和交互是否流畅

这种渐进式构建方式确保每一步都有可见的进展，便于调试和修改。