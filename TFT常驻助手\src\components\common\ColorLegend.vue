<template>
  <div class="color-legend">
    <div class="legend-title">数据等级说明</div>
    <div class="legend-grid">
      <div 
        v-for="item in legendItems" 
        :key="item.level"
        class="legend-item"
      >
        <div 
          class="legend-color"
          :style="{ backgroundColor: item.color }"
        ></div>
        <span class="legend-label">{{ item.label }}</span>
      </div>
    </div>
    <div class="legend-note">
      <strong>颜色基于同费用英雄出场率相对比较</strong>
    </div>
  </div>
</template>

<script setup lang="ts">
// 根据图二的颜色方案，田字布局从优秀到较差
const legendItems = [
  { level: 'excellent', color: '#fc1236', label: '优秀 (前25%)' },    // 左上 - 红色
  { level: 'good', color: '#ff781b', label: '良好 (25%-50%)' },        // 右上 - 橙色  
  { level: 'average', color: '#ffd607', label: '一般 (50%-75%)' },     // 左下 - 黄色
  { level: 'poor', color: '#78f91a', label: '较差 (后25%)' }           // 右下 - 绿色
]
</script>

<style scoped>
.color-legend {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  margin-bottom: 0rem;
  height: auto;
}

.legend-title {
  color: white;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 0.3rem;
  text-align: center;
}

.legend-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.4rem 0.8rem;
  margin-bottom: 0.3rem;
  padding: 0 0.6rem;
  min-width: 0;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  justify-content: flex-start;
  min-width: 0;
  white-space: nowrap;
  overflow: hidden;
}

.legend-item:nth-child(odd) {
  padding-left: 0.4rem;
}

.legend-item:nth-child(even) {
  padding-right: 0.4rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
}

.legend-note {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  text-align: center;
  font-weight: 500;
  margin-top: 0.15rem;
}

/* 响应式设计 - 确保在任何宽度下都保持田字排布 */
@media (max-width: 900px) {
  .legend-grid {
    grid-template-columns: 1fr 1fr !important;
    gap: 0.3rem 0.6rem;
    padding: 0 0.4rem;
  }

  .legend-item {
    gap: 0.25rem;
  }

  .legend-item:nth-child(odd) {
    padding-left: 0.3rem;
  }

  .legend-item:nth-child(even) {
    padding-right: 0.3rem;
  }

  .legend-color {
    width: 10px;
    height: 10px;
  }

  .legend-label {
    font-size: 13px;
  }
}

@media (max-width: 600px) {
  .legend-grid {
    grid-template-columns: 1fr 1fr !important;
    gap: 0.2rem 0.4rem;
    padding: 0 0.3rem;
  }

  .legend-item {
    gap: 0.2rem;
  }

  .legend-item:nth-child(odd) {
    padding-left: 0.2rem;
  }

  .legend-item:nth-child(even) {
    padding-right: 0.2rem;
  }

  .legend-color {
    width: 8px;
    height: 8px;
  }

  .legend-label {
    font-size: 13px;
  }

  .legend-note {
    font-size: 13px;
  }
}
</style>