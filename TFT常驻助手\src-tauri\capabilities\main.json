{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "main", "description": "Main window capabilities", "windows": ["main"], "permissions": ["core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-set-always-on-top", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-unminimize", "core:window:allow-close", "core:window:allow-start-dragging", "core:window:allow-center", "core:window:allow-request-user-attention", "core:window:allow-set-resizable", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-decorations", "core:window:allow-get-all-windows", "core:window:allow-scale-factor", "core:window:allow-inner-position", "core:window:allow-outer-position", "core:window:allow-inner-size", "core:window:allow-outer-size", "core:window:allow-is-fullscreen", "core:window:allow-is-minimized", "core:window:allow-is-maximized", "core:window:allow-is-focused", "core:window:allow-is-decorated", "core:window:allow-is-resizable", "core:window:allow-is-maximizable", "core:window:allow-is-minimizable", "core:window:allow-is-closable", "core:window:allow-is-visible", "core:window:allow-is-enabled", "core:window:allow-title", "core:window:allow-current-monitor", "core:window:allow-primary-monitor", "core:window:allow-monitor-from-point", "core:window:allow-available-monitors", "core:window:allow-cursor-position", "core:window:allow-theme", "core:window:allow-internal-toggle-maximize"]}