<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'

// 响应式数据
const pythonStatus = ref<{
  success: boolean
  data?: string
  error?: string
}>({ success: false })

const yimiaoJueStatus = ref<{
  running: boolean
  message: string
}>({ running: false, message: '检查中...' })

const testResult = ref<{
  success: boolean
  data?: string
  error?: string
}>({ success: false })

const isLoading = ref(false)
const statusMessage = ref('正在初始化...')

// 检查Python环境
const checkPythonEnvironment = async () => {
  try {
    statusMessage.value = '检查Python环境...'
    const result = await invoke('check_python_environment')
    pythonStatus.value = result as any
    
    if (pythonStatus.value.success) {
      statusMessage.value = 'Python环境正常'
    } else {
      statusMessage.value = 'Python环境异常'
    }
  } catch (error) {
    console.error('检查Python环境失败:', error)
    pythonStatus.value = {
      success: false,
      error: `检查失败: ${error}`
    }
    statusMessage.value = 'Python环境检查失败'
  }
}

// 检查YimiaoJue状态
const checkYimiaoJueStatus = async () => {
  try {
    statusMessage.value = '检查弈秒决状态...'
    const result = await invoke('check_yimiaojue_status')
    yimiaoJueStatus.value = result as any
    
    if (yimiaoJueStatus.value.running) {
      statusMessage.value = '弈秒决就绪'
    } else {
      statusMessage.value = '弈秒决未就绪'
    }
  } catch (error) {
    console.error('检查弈秒决状态失败:', error)
    yimiaoJueStatus.value = {
      running: false,
      message: `检查失败: ${error}`
    }
    statusMessage.value = '弈秒决状态检查失败'
  }
}

// 测试Python脚本
const testPythonScript = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '测试Python脚本...'
    
    const result = await invoke('test_python_script')
    testResult.value = result as any
    
    if (testResult.value.success) {
      statusMessage.value = 'Python脚本测试成功'
    } else {
      statusMessage.value = 'Python脚本测试失败'
    }
  } catch (error) {
    console.error('测试Python脚本失败:', error)
    testResult.value = {
      success: false,
      error: `测试失败: ${error}`
    }
    statusMessage.value = 'Python脚本测试失败'
  } finally {
    isLoading.value = false
  }
}

// 启动弈秒决
const startYimiaoJue = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '启动弈秒决...'

    const result = await invoke('start_yimiaojue')
    const startResult = result as any

    if (startResult.success) {
      statusMessage.value = '弈秒决启动成功'
      pythonRunning.value = true  // 标记Python程序已启动

      // 等待3秒让Python程序完全启动，然后获取状态
      setTimeout(async () => {
        await getOCRStatus()
      }, 3000)

      // 重新检查状态
      await checkYimiaoJueStatus()
    } else {
      statusMessage.value = '弈秒决启动失败'
      pythonRunning.value = false
      console.error('启动失败:', startResult.error)
    }
  } catch (error) {
    console.error('启动弈秒决失败:', error)
    statusMessage.value = '弈秒决启动失败'
    pythonRunning.value = false
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await checkPythonEnvironment()
  await checkYimiaoJueStatus()
})

// 获取状态图标
const getStatusIcon = (success: boolean) => {
  return success ? '✅' : '❌'
}

// 获取状态颜色类
const getStatusClass = (success: boolean) => {
  return success ? 'text-green-400' : 'text-red-400'
}

// 获取Python详细信息
const getPythonDetails = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '获取Python详细信息...'

    const result = await invoke('get_python_details')
    testResult.value = result as any

    if (testResult.value.success) {
      statusMessage.value = 'Python详细信息获取成功'
    } else {
      statusMessage.value = 'Python详细信息获取失败'
    }
  } catch (error) {
    console.error('获取Python详细信息失败:', error)
    testResult.value = {
      success: false,
      error: `获取失败: ${error}`
    }
    statusMessage.value = 'Python详细信息获取失败'
  } finally {
    isLoading.value = false
  }
}

// 直接运行Python脚本
const runPythonScriptDirect = async () => {
  try {
    isLoading.value = true
    statusMessage.value = '直接运行Python脚本...'

    const result = await invoke('run_python_script_direct')
    testResult.value = result as any

    if (testResult.value.success) {
      statusMessage.value = 'Python脚本运行完成'
    } else {
      statusMessage.value = 'Python脚本运行失败'
    }
  } catch (error) {
    console.error('直接运行Python脚本失败:', error)
    testResult.value = {
      success: false,
      error: `运行失败: ${error}`
    }
    statusMessage.value = 'Python脚本运行失败'
  } finally {
    isLoading.value = false
  }
}

// OCR相关状态
const ocrStatus = ref<any>({})
const autoMode = ref(false)
const pythonRunning = ref(false)  // 跟踪Python程序是否运行

// 获取OCR状态
const getOCRStatus = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '获取OCR状态...'

    const result = await invoke('get_ocr_status')
    ocrStatus.value = result as any

    if (ocrStatus.value.success && ocrStatus.value.data) {
      const statusData = JSON.parse(ocrStatus.value.data)
      autoMode.value = statusData.auto_mode
      statusMessage.value = 'OCR状态获取成功'
    } else {
      statusMessage.value = 'OCR状态获取失败'
    }
  } catch (error) {
    console.error('获取OCR状态失败:', error)
    ocrStatus.value = {
      success: false,
      error: `获取失败: ${error}`
    }
    statusMessage.value = 'OCR状态获取失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描海克斯
const manualScanHex = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '手动扫描海克斯...'

    const result = await invoke('manual_scan_hex')
    testResult.value = result as any

    if (testResult.value.success) {
      statusMessage.value = '海克斯扫描完成'
    } else {
      statusMessage.value = '海克斯扫描失败'
    }
  } catch (error) {
    console.error('手动扫描海克斯失败:', error)
    testResult.value = {
      success: false,
      error: `扫描失败: ${error}`
    }
    statusMessage.value = '海克斯扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 手动扫描装备
const manualScanEquipment = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '手动扫描装备...'

    const result = await invoke('manual_scan_equipment')
    testResult.value = result as any

    if (testResult.value.success) {
      statusMessage.value = '装备扫描完成'
    } else {
      statusMessage.value = '装备扫描失败'
    }
  } catch (error) {
    console.error('手动扫描装备失败:', error)
    testResult.value = {
      success: false,
      error: `扫描失败: ${error}`
    }
    statusMessage.value = '装备扫描失败'
  } finally {
    isLoading.value = false
  }
}

// 切换自动模式
const toggleAutoMode = async () => {
  if (!pythonRunning.value) {
    statusMessage.value = '请先启动弈秒决程序'
    return
  }

  try {
    isLoading.value = true
    statusMessage.value = '切换自动模式...'

    const result = await invoke('toggle_auto_mode')
    testResult.value = result as any

    if (testResult.value.success && testResult.value.data) {
      const responseData = JSON.parse(testResult.value.data)
      autoMode.value = responseData.auto_mode
      statusMessage.value = `自动模式已${responseData.action === 'started' ? '启动' : '停止'}`
    } else {
      statusMessage.value = '切换自动模式失败'
    }
  } catch (error) {
    console.error('切换自动模式失败:', error)
    testResult.value = {
      success: false,
      error: `切换失败: ${error}`
    }
    statusMessage.value = '切换自动模式失败'
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <div class="yimiaojue-container">
    <!-- 标题区域 -->
    <div class="header-section">
      <h2 class="title">弈秒决 - 云顶之弈智能助手</h2>
      <p class="subtitle">基于RapidOCR的高精度游戏信息识别工具</p>
    </div>

    <!-- 状态显示区域 -->
    <div class="status-section">
      <div class="status-card">
        <h3 class="status-title">系统状态</h3>
        <div class="status-item">
          <span class="status-label">当前状态:</span>
          <span class="status-value">{{ statusMessage }}</span>
        </div>
        
        <!-- Python环境状态 -->
        <div class="status-item">
          <span class="status-label">Python环境:</span>
          <span :class="getStatusClass(pythonStatus.success)">
            {{ getStatusIcon(pythonStatus.success) }}
            {{ pythonStatus.success ? pythonStatus.data : pythonStatus.error }}
          </span>
        </div>
        
        <!-- 弈秒决状态 -->
        <div class="status-item">
          <span class="status-label">弈秒决状态:</span>
          <span :class="getStatusClass(yimiaoJueStatus.running)">
            {{ getStatusIcon(yimiaoJueStatus.running) }}
            {{ yimiaoJueStatus.message }}
          </span>
        </div>
      </div>
    </div>

    <!-- 控制按钮区域 -->
    <div class="control-section">
      <div class="button-grid">
        <button
          @click="checkPythonEnvironment"
          class="control-button"
          :disabled="isLoading"
        >
          🔍 检查Python环境
        </button>

        <button
          @click="getPythonDetails"
          class="control-button"
          :disabled="isLoading"
        >
          📋 Python详细信息
        </button>

        <button
          @click="testPythonScript"
          class="control-button"
          :disabled="isLoading"
        >
          🧪 测试Python脚本
        </button>

        <button
          @click="runPythonScriptDirect"
          class="control-button"
          :disabled="isLoading"
        >
          🔧 直接运行脚本
        </button>

        <button
          @click="startYimiaoJue"
          class="control-button primary"
          :disabled="isLoading || !pythonStatus.success"
        >
          🚀 启动弈秒决
        </button>
        
        <button
          @click="checkYimiaoJueStatus"
          class="control-button"
          :disabled="isLoading"
        >
          📊 检查状态
        </button>
      </div>
    </div>

    <!-- OCR控制面板 -->
    <div class="glass-card">
      <h3 class="section-title">🎯 OCR控制面板</h3>
      <p class="section-description">
        控制弈秒决的OCR识别功能，包括自动模式和手动扫描
      </p>

      <div class="control-grid">
        <button
          @click="getOCRStatus"
          class="control-button"
          :disabled="isLoading || !pythonRunning"
        >
          📊 获取OCR状态
        </button>

        <button
          @click="toggleAutoMode"
          class="control-button"
          :class="{ 'active': autoMode }"
          :disabled="isLoading || !pythonRunning"
        >
          {{ autoMode ? '🛑 停止自动模式' : '▶️ 启动自动模式' }}
        </button>

        <button
          @click="manualScanHex"
          class="control-button"
          :disabled="isLoading || !pythonRunning"
        >
          🔮 扫描海克斯
        </button>

        <button
          @click="manualScanEquipment"
          class="control-button"
          :disabled="isLoading || !pythonRunning"
        >
          ⚔️ 扫描装备
        </button>
      </div>

      <!-- Python运行状态提示 -->
      <div v-if="!pythonRunning" class="status-warning">
        ⚠️ 请先启动弈秒决程序才能使用OCR控制功能
      </div>

      <!-- OCR状态显示 -->
      <div v-if="ocrStatus.success && ocrStatus.data" class="status-display">
        <h4>📈 OCR状态信息</h4>
        <pre class="status-content">{{ JSON.stringify(JSON.parse(ocrStatus.data), null, 2) }}</pre>
      </div>
    </div>

    <!-- 测试结果区域 -->
    <div v-if="testResult.data || testResult.error" class="result-section">
      <div class="result-card">
        <h3 class="result-title">测试结果</h3>
        <div v-if="testResult.success && testResult.data" class="result-content success">
          <pre>{{ testResult.data }}</pre>
        </div>
        <div v-if="testResult.error" class="result-content error">
          <pre>{{ testResult.error }}</pre>
        </div>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ statusMessage }}</p>
    </div>
  </div>
</template>

<style scoped>
.yimiaojue-container {
  height: 100%;
  padding: 1.5rem;
  overflow-y: auto;
  position: relative;
}

/* 标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 状态区域 */
.status-section {
  margin-bottom: 2rem;
}

.status-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.status-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
}

.status-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.status-value {
  color: rgba(255, 255, 255, 0.9);
}

/* 控制按钮区域 */
.control-section {
  margin-bottom: 2rem;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.control-button {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.control-button.primary {
  background: linear-gradient(45deg, rgb(88, 86, 134), rgb(65, 95, 160));
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.control-button.primary:hover:not(:disabled) {
  background: linear-gradient(45deg, rgb(98, 96, 144), rgb(75, 105, 170));
}

.control-button.active {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  font-weight: 600;
  box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.status-display {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-display h4 {
  margin: 0 0 0.5rem 0;
  color: #e2e8f0;
  font-size: 0.9rem;
}

.status-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #a0aec0;
  margin: 0;
  overflow-x: auto;
}

.status-warning {
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  color: #ffc107;
  font-size: 0.9rem;
  text-align: center;
}

/* 结果区域 */
.result-section {
  margin-bottom: 2rem;
}

.result-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.result-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.result-content {
  border-radius: 8px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.result-content.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  color: rgb(134, 239, 172);
}

.result-content.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: rgb(252, 165, 165);
}

/* 加载指示器 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-text {
  color: white;
  font-size: 0.9rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
