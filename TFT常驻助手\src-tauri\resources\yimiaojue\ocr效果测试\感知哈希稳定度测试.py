# -*- coding: utf-8 -*-
"""
感知哈希（Perceptual Hash）稳定性测试工具 (v6.1 - 最终版)

功能：
1.  [实时监控模式]
    -   每个监控项支持设置两个基准图（初态/稳态），任一匹配即可触发。
    -   触发逻辑升级，可选择满足1,2,3或全部4个哈希算法的组合。
    -   每个监控项可独立设置预处理选项（灰度、模糊、裁剪）。
    -   引入“边框哈希”模式，免疫动态内容干扰。
    -   [新] 为刷新按钮实现“三取二”稳定触发规则。
"""

import tkinter as tk
from tkinter import filedialog, ttk, messagebox
from PIL import Image, ImageFilter, ImageGrab
import imagehash
import os
import threading
import time
import win32api
import json

# --- 全局定义监控区域 ---
MONITOR_REGIONS = {
    "blue_button": {"coords": (1163/2560, 1301/1440, 1398/2560, 1347/1440), "label": "蓝色按钮"},
    "refresh_button": {
        "coords": [
            (669/2560, 1118/1440, 805/2560, 1185/1440), 
            (1212/2560, 1118/1440, 1348/2560, 1185/1440), 
            (1754/2560, 1118/1440, 1890/2560, 1185/1440)
        ], 
        "label": "刷新按钮",
        "trigger_rule": "2_of_3" # 新增特殊触发规则
    },
    "fruit": {"coords": (900/2560, 1110/1440, 1540/2560, 1190/1440), "label": "果实位置"},
    "equipment_frame": {
        "label": "装备(仅边框)",
        "coords_outer": (349/2560, 1160/1440, 1987/2560, 1439/1440),
        "coords_inner": (355/2560, 1166/1440, 1980/2560, 1430/1440),
        "mode": "frame_hash"
    }
}

class AutoHidePopup(tk.Toplevel):
    def __init__(self, master, text, duration=2000):
        super().__init__(master)
        self.overrideredirect(True); self.attributes('-topmost', True); self.attributes('-alpha', 0.8)
        ttk.Label(self, text=text, padding=10, font=("Microsoft YaHei", 12, "bold"), background="green", foreground="white").pack()
        master.update_idletasks()
        x = master.winfo_x() + (master.winfo_width() // 2) - (self.winfo_width() // 2)
        y = master.winfo_y() + (master.winfo_height() // 2) - (self.winfo_height() // 2)
        self.geometry(f"+{x}+{y}"); self.after(duration, self.destroy)

class PerceptualHashStabilityTester(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("感知哈希稳定性测试工具 v6.1 (最终版)")
        self.geometry("1200x800")
        self.monitoring_bases = {}
        self.monitor_configs = {}
        self.is_monitoring = False
        self.monitor_thread = None
        self._setup_ui()
        self.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _setup_ui(self):
        tab = ttk.Frame(self)
        tab.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        global_control_frame = ttk.LabelFrame(tab, text="全局控制", padding="10")
        global_control_frame.pack(fill=tk.X, pady=5)
        
        self.btn_monitor_start = ttk.Button(global_control_frame, text="▶ 开始监控", command=self.start_monitoring)
        self.btn_monitor_start.pack(side=tk.LEFT, padx=5)
        self.btn_monitor_stop = ttk.Button(global_control_frame, text="■ 停止监控", command=self.stop_monitoring, state=tk.DISABLED)
        self.btn_monitor_stop.pack(side=tk.LEFT, padx=5)
        
        self.btn_save_hashes = ttk.Button(global_control_frame, text="💾 保存哈希", command=self.save_hashes_to_json)
        self.btn_save_hashes.pack(side=tk.RIGHT, padx=5)
        
        self.monitor_status_label = ttk.Label(global_control_frame, text="状态: 已停止", foreground="red")
        self.monitor_status_label.pack(side=tk.LEFT, padx=10)
        
        ttk.Label(global_control_frame, text="全局阈值:").pack(side=tk.LEFT, padx=(20, 0))
        self.threshold_var = tk.DoubleVar(value=4.0)
        ttk.Spinbox(global_control_frame, from_=0, to=64, increment=1, textvariable=self.threshold_var, width=5).pack(side=tk.LEFT, padx=5)

        regions_canvas = tk.Canvas(tab)
        scrollbar = ttk.Scrollbar(tab, orient="vertical", command=regions_canvas.yview)
        scrollable_frame = ttk.Frame(regions_canvas)
        scrollable_frame.bind("<Configure>", lambda e: regions_canvas.configure(scrollregion=regions_canvas.bbox("all")))
        regions_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        regions_canvas.configure(yscrollcommand=scrollbar.set)
        
        regions_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        for key, info in MONITOR_REGIONS.items():
            self._create_region_control(scrollable_frame, key, info)

    def _create_region_control(self, parent, key, info):
        frame = ttk.LabelFrame(parent, text=info['label'], padding=10)
        frame.pack(fill=tk.X, expand=True, padx=5, pady=5)
        
        left_frame = ttk.Frame(frame)
        left_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        row1 = ttk.Frame(left_frame)
        row1.pack(fill=tk.X)
        active_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row1, text="启用", variable=active_var).pack(side=tk.LEFT)
        ttk.Button(row1, text="设置基准1(初态)", command=lambda k=key: self._set_monitoring_base(k, 0)).pack(side=tk.LEFT, padx=(10, 2))
        ttk.Button(row1, text="设置基准2(稳态)", command=lambda k=key: self._set_monitoring_base(k, 1)).pack(side=tk.LEFT, padx=(2, 5))
        
        status_label = ttk.Label(left_frame, text="[基准1: 未设置]\n[基准2: 未设置]", foreground="grey", wraplength=600, justify=tk.LEFT)
        status_label.pack(side=tk.LEFT, padx=5, pady=(5,0))
        
        right_frame = ttk.Frame(frame)
        right_frame.pack(side=tk.LEFT, padx=20)
        
        grayscale_var = tk.BooleanVar(value=False)
        blur_var = tk.BooleanVar(value=False)
        crop_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(right_frame, text="灰度", variable=grayscale_var).pack(anchor=tk.W)
        ttk.Checkbutton(right_frame, text="模糊", variable=blur_var).pack(anchor=tk.W)
        ttk.Checkbutton(right_frame, text="裁剪", variable=crop_var).pack(anchor=tk.W)
        
        trigger_logic_var = tk.StringVar(value="任意 (>=2)")
        combo = ttk.Combobox(right_frame, textvariable=trigger_logic_var, values=["任意 (>=1)", "任意 (>=2)", "任意 (>=3)", "全部 (4)"], width=12, state="readonly")
        combo.pack(pady=5)
        
        self.monitor_configs[key] = {
            "active": active_var, "status_label": status_label,
            "grayscale": grayscale_var, "blur": blur_var, "crop": crop_var,
            "trigger_logic": trigger_logic_var
        }

    def _preprocess_image(self, img, key):
        config = self.monitor_configs[key]
        if config["blur"].get(): img = img.filter(ImageFilter.GaussianBlur(radius=1))
        if config["crop"].get(): w, h = img.size; img = img.crop((w*0.05, h*0.05, w*0.95, h*0.95))
        if config["grayscale"].get(): img = img.convert("L")
        return img

    def _calculate_hashes(self, img):
        return {"p": imagehash.phash(img), "d": imagehash.dhash(img), "a": imagehash.average_hash(img), "w": imagehash.whash(img)}

    def _get_image_for_hash(self, key, base_img):
        info = MONITOR_REGIONS[key]
        image_to_process = base_img
        if info.get("mode") == "frame_hash":
            outer_w, outer_h = base_img.size
            outer_coords = info["coords_outer"]
            inner_coords = info["coords_inner"]
            inner_rel_x1 = (inner_coords[0] - outer_coords[0]) / (outer_coords[2] - outer_coords[0])
            inner_rel_y1 = (inner_coords[1] - outer_coords[1]) / (outer_coords[3] - outer_coords[1])
            inner_rel_x2 = (inner_coords[2] - outer_coords[0]) / (outer_coords[2] - outer_coords[0])
            inner_rel_y2 = (inner_coords[3] - outer_coords[1]) / (outer_coords[3] - outer_coords[1])
            inner_abs_x1 = int(outer_w * inner_rel_x1)
            inner_abs_y1 = int(outer_h * inner_rel_y1)
            inner_abs_x2 = int(outer_w * inner_rel_x2)
            inner_abs_y2 = int(outer_h * inner_rel_y2)
            mask = Image.new('L', (outer_w, outer_h), 0)
            mask.paste(255, (0, 0, outer_w, outer_h))
            mask.paste(0, (inner_abs_x1, inner_abs_y1, inner_abs_x2, inner_abs_y2))
            final_img = Image.new(base_img.mode, (outer_w, outer_h), 'black')
            final_img.paste(base_img, (0,0), mask)
            image_to_process = final_img
        return self._preprocess_image(image_to_process, key)

    def _set_monitoring_base(self, key, index):
        path = filedialog.askopenfilename(title=f"为“{MONITOR_REGIONS[key]['label']}”设置基准 {index+1}")
        if not path: return
        try:
            if key not in self.monitoring_bases:
                self.monitoring_bases[key] = [None, None]
            with Image.open(path) as img:
                hash_img = self._get_image_for_hash(key, img)
                self.monitoring_bases[key][index] = self._calculate_hashes(hash_img)
            self._update_status_label(key)
        except Exception as e: messagebox.showerror("错误", f"处理基准图失败: {e}")

    def _update_status_label(self, key):
        status_label = self.monitor_configs[key]["status_label"]
        base_list = self.monitoring_bases.get(key, [None, None])
        text1 = "[基准1: 未设置]"; text2 = "[基准2: 未设置]"
        if base_list and len(base_list) > 0 and base_list[0]:
            h = base_list[0]; text1 = f"[基准1: p:{h['p']} d:{h['d']} a:{h['a']} w:{h['w']}]"
        if base_list and len(base_list) > 1 and base_list[1]:
            h = base_list[1]; text2 = f"[基准2: p:{h['p']} d:{h['d']} a:{h['a']} w:{h['w']}]"
        status_label.config(text=f"{text1}\n{text2}", foreground="black")

    def save_hashes_to_json(self):
        """将当前设置的所有基准哈希保存到JSON文件。"""
        if not self.monitoring_bases:
            messagebox.showwarning("无数据", "尚未设置任何基准哈希，无法保存。")
            return

        path = filedialog.asksaveasfilename(
            title="保存哈希基准文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialfile="hash_bases.json"
        )
        if not path:
            return

        # 将 imagehash 对象转换为字符串
        output_data = {}
        for key, bases in self.monitoring_bases.items():
            output_data[key] = {}
            if bases[0]:
                # 为装备区特别处理第一个基准态的命名
                base_1_key = 'base_empty' if key == 'equipment_frame' else 'base_1'
                output_data[key][base_1_key] = {k: str(v) for k, v in bases[0].items()}
            if bases[1]:
                # 为装备区特别处理第二个基准态的命名
                base_2_key = 'base_stable' if key == 'equipment_frame' else 'base_2'
                output_data[key][base_2_key] = {k: str(v) for k, v in bases[1].items()}

        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("成功", f"哈希基准已成功保存到:\n{path}")
        except Exception as e:
            messagebox.showerror("保存失败", f"写入文件时发生错误: {e}")

    def start_monitoring(self):
        if not any(self.monitor_configs[k]["active"].get() and k in self.monitoring_bases for k in self.monitor_configs):
            messagebox.showwarning("无法开始", "请至少为一个已启用的监控区域设置基准图。"); return
        self.is_monitoring = True
        self.btn_monitor_start.config(state=tk.DISABLED); self.btn_monitor_stop.config(state=tk.NORMAL)
        self.monitor_status_label.config(text="状态: 正在运行...", foreground="green")
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True); self.monitor_thread.start()

    def stop_monitoring(self):
        self.is_monitoring = False
        self.btn_monitor_start.config(state=tk.NORMAL); self.btn_monitor_stop.config(state=tk.DISABLED)
        self.monitor_status_label.config(text="状态: 已停止", foreground="red")

    def _monitoring_loop(self):
        screen_w, screen_h = win32api.GetSystemMetrics(0), win32api.GetSystemMetrics(1)
        center_x = screen_w / 2
        v_h, v_w = screen_h, screen_h * (16 / 9)
        v_x, v_y = center_x - v_w / 2, 0
        last_triggered = {}

        while self.is_monitoring:
            try:
                active_keys = [k for k, v in self.monitor_configs.items() if v["active"].get() and k in self.monitoring_bases]
                for key in active_keys:
                    if time.time() - last_triggered.get(key, 0) < 2.5: continue
                    
                    info = MONITOR_REGIONS[key]
                    base_hash_list = [h for h in self.monitoring_bases.get(key, []) if h is not None]
                    if not base_hash_list: continue

                    # --- 特殊规则处理: 刷新按钮 "三取二" ---
                    if info.get("trigger_rule") == "2_of_3":
                        match_count = 0
                        for c in info["coords"]:
                            bbox = (int(v_x + v_w * c[0]), int(v_y + v_h * c[1]), int(v_x + v_w * c[2]), int(v_y + v_h * c[3]))
                            img = ImageGrab.grab(bbox=bbox)
                            hash_img = self._get_image_for_hash(key, img)
                            current_hashes = self._calculate_hashes(hash_img)
                            
                            if any(self._check_trigger(current_hashes, base_h, key) for base_h in base_hash_list):
                                match_count += 1
                        
                        if match_count >= 2:
                            self.after(0, self._show_trigger_popup, info['label'])
                            last_triggered[key] = time.time()
                        continue

                    # --- 常规触发逻辑 ---
                    triggered_this_frame = False
                    coords_list = []
                    if info.get("mode") == "frame_hash": coords_list.append(info["coords_outer"])
                    else: coords_list = info["coords"] if isinstance(info["coords"], list) else [info["coords"]]

                    for c in coords_list:
                        bbox = (int(v_x + v_w * c[0]), int(v_y + v_h * c[1]), int(v_x + v_w * c[2]), int(v_y + v_h * c[3]))
                        img = ImageGrab.grab(bbox=bbox)
                        hash_img = self._get_image_for_hash(key, img)
                        current_hashes = self._calculate_hashes(hash_img)

                        if any(self._check_trigger(current_hashes, base_h, key) for base_h in base_hash_list):
                            triggered_this_frame = True
                            break
                    
                    if triggered_this_frame:
                        self.after(0, self._show_trigger_popup, info['label'])
                        last_triggered[key] = time.time()
                
                time.sleep(0.04)
            except Exception as e:
                print(f"监控循环出错: {e}"); time.sleep(1)
        
        self.after(0, self.stop_monitoring)

    def _check_trigger(self, current_hashes, base_hashes, key):
        """根据独立的触发逻辑检查是否匹配"""
        diffs = {h_key: base_hashes[h_key] - current_hashes[h_key] for h_key in base_hashes}
        logic_str = self.monitor_configs[key]["trigger_logic"].get()
        threshold = self.threshold_var.get()
        count = sum(1 for d in diffs.values() if d <= threshold)
        
        required_count = 0
        if ">=1" in logic_str: required_count = 1
        elif ">=2" in logic_str: required_count = 2
        elif ">=3" in logic_str: required_count = 3
        elif "4" in logic_str: required_count = 4
        
        return required_count > 0 and count >= required_count

    def _show_trigger_popup(self, label):
        AutoHidePopup(self, f"触发成功: {label}")

    def _on_closing(self):
        self.is_monitoring = False
        if self.monitor_thread: self.monitor_thread.join(timeout=0.2)
        self.destroy()

if __name__ == "__main__":
    PerceptualHashStabilityTester().mainloop()