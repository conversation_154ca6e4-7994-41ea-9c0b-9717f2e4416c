# 制品库配置说明

## 当前配置
根据现有的 `爬取上传全流程.py`，当前制品库配置如下：

```python
REPO_CONFIG = {
    "package": "shujugengxin/yu",
    "repo": "g-iuob0664-generic",
    "version_url": "https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/version.json"
}
```

## 数据库配置

### 新的简化配置
由于项目现在使用统一的完整数据库，我们**只需要上传一个数据库文件**：

```python
DATABASE_CONFIG = {
    'complete_database': {
        'name': '完整数据库',
        'db_file': 'tft_data.db'
    }
}
```

### 与原有配置的对比
原有的 `爬取上传全流程.py` 分别处理装备和海克斯：
```python
# 原有配置（已不再使用）
MODULES = {
    'equipment': {
        'name': '装备',
        'db_file': 'equipment.db'
    },
    'hextech': {
        'name': '海克斯科技',
        'db_file': 'hextech.db'
    }
}
```

**新配置的优势：**
- ✅ 统一数据库管理
- ✅ 简化上传流程
- ✅ 避免数据分散
- ✅ 更易维护和使用

## 版本控制文件更新

版本控制文件 `version.json` 现在只包含一个数据库条目：

```json
{
  "complete_database": {
    "version": "20231201.1",
    "checksum": "ghi789...",
    "url": "https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/tft_data.db?version=latest"
  }
}
```

## 制品库端改动要求

### 无需特殊改动

制品库本身是通用的文件存储系统，**不需要任何特殊配置**。只需要：

1. **确保有足够的存储空间** - `tft_data.db` 包含所有模块数据
2. **权限配置** - 确保 CODING_TOKEN 对应的用户有上传权限
3. **文件类型支持** - 制品库已支持 `.db` 文件

### 兼容性说明

- 新的完整数据库可以与现有的分模块数据库共存
- 原有的 `equipment.db` 和 `hextech.db` 可以继续保留
- 版本控制文件会自动扩展以支持新的数据库类型

## 环境变量设置

使用前需要设置环境变量：

```bash
# Windows
set CODING_TOKEN=your_token_here

# Linux/Mac
export CODING_TOKEN=your_token_here
```

## 上传的文件

修改后的程序将上传以下文件：
- `tft_data.db` - **完整的云顶之奕数据库**（包含所有模块数据）
- `version.json` - 更新的版本控制文件

## 下载地址

完整数据库的下载地址格式：
```
https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/tft_data.db?version=latest
```

## 总结

**制品库端无需任何特殊改动**，新的统一数据库配置：

✅ **简化了上传流程** - 只需上传一个数据库文件  
✅ **统一了数据管理** - 所有数据集中在一个文件中  
✅ **保持了向后兼容** - 不影响现有制品库结构  
✅ **减少了维护成本** - 单一文件更易管理  

所有改动都在客户端代码中完成，制品库可以直接支持新的数据库文件。 