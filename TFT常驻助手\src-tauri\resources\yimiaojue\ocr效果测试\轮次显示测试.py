# -*- coding: utf-8 -*-
import tkinter as tk
from tkinter import filedialog, ttk
from PIL import Image, ImageTk, ImageEnhance
import numpy as np
from rapidocr_onnxruntime import RapidOCR
import os

# --- OCR 引擎初始化 ---
try:
    print("正在初始化RapidOCR引擎...")
    ocr_engine = RapidOCR(text_score=0.3)
    print("RapidOCR引擎初始化成功。")
    OCR_AVAILABLE = True
except Exception as e:
    print(f"无法初始化RapidOCR引擎: {e}")
    OCR_AVAILABLE = False

class OCRTestApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("轮次OCR效果测试工具 (v3 - 区域分割诊断)")
        self.geometry("800x600")

        self.image_path = None
        self.original_image = None
        self.processed_images = {}

        # --- UI 布局 ---
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # --- 控制区 ---
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X)

        self.btn_select = ttk.Button(control_frame, text="1. 选择图片", command=self.select_image)
        self.btn_select.pack(side=tk.LEFT, padx=5)

        self.btn_run = ttk.Button(control_frame, text="2. 全图识别", command=self.run_ocr_test, state=tk.DISABLED)
        self.btn_run.pack(side=tk.LEFT, padx=5)
        
        # [新增] 区域识别按钮
        self.btn_left = ttk.Button(control_frame, text="仅识别左半部分", command=lambda: self.run_partial_ocr('left'), state=tk.DISABLED)
        self.btn_left.pack(side=tk.LEFT, padx=5)
        
        self.btn_right = ttk.Button(control_frame, text="仅识别右半部分", command=lambda: self.run_partial_ocr('right'), state=tk.DISABLED)
        self.btn_right.pack(side=tk.LEFT, padx=5)


        # --- 显示区 ---
        display_frame = ttk.Frame(main_frame)
        display_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.image_label = ttk.Label(display_frame, text="请先选择一张图片", anchor="center", borderwidth=2, relief="sunken")
        self.image_label.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10)

        self.result_frame = ttk.LabelFrame(display_frame, text="识别结果 (点击条目可预览预处理效果)")
        self.result_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=10, ipadx=5)

    def select_image(self):
        path = filedialog.askopenfilename(title="选择轮次截图", filetypes=[("Image files", "*.png *.jpg *.bmp"), ("All files", "*.*")])
        if path:
            self.image_path = path
            self.original_image = Image.open(path).convert("RGB")
            self.show_image(self.original_image)
            self.btn_run.config(state=tk.NORMAL if OCR_AVAILABLE else tk.DISABLED)
            self.btn_left.config(state=tk.NORMAL if OCR_AVAILABLE else tk.DISABLED)
            self.btn_right.config(state=tk.NORMAL if OCR_AVAILABLE else tk.DISABLED)

    def show_image(self, img):
        w, h = img.size
        max_w, max_h = 400, 500
        ratio = min(max_w/w, max_h/h) if w > max_w or h > max_h else 1
        display_img = img.resize((int(w*ratio), int(h*ratio)), Image.Resampling.LANCZOS)
        self.tk_image = ImageTk.PhotoImage(display_img)
        self.image_label.config(image=self.tk_image, text="")

    def run_ocr_test(self):
        if not self.original_image or not OCR_AVAILABLE: return
        self.clear_results()
        strategies = {
            "原始图像": lambda img: img, "放大2倍": lambda img: img.resize((img.width * 2, img.height * 2), Image.Resampling.LANCZOS),
            "放大3倍": lambda img: img.resize((img.width * 3, img.height * 3), Image.Resampling.LANCZOS),
            "灰度图": lambda img: img.convert('L'), "增强对比度": lambda img: ImageEnhance.Contrast(img.convert('L')).enhance(2.0),
            "二值化(阈值150)": lambda img: img.convert('L').point(lambda x: 0 if x < 150 else 255),
            "旧版轮次预处理": self.old_round_preprocess, "放大2倍->二值化(140)": lambda img: self.upscale_then_binary(img, 2, 140),
            "放大3倍->二值化(140)": lambda img: self.upscale_then_binary(img, 3, 140),
        }
        for name, func in strategies.items():
            self.perform_and_display_ocr(name, func(self.original_image.copy()))

    def run_partial_ocr(self, part):
        """[新增] 对部分图像进行识别"""
        if not self.original_image or not OCR_AVAILABLE: return
        self.clear_results()
        
        w, h = self.original_image.size
        if part == 'left':
            # 截取左侧50%的区域
            crop_area = (0, 0, w // 2, h)
            title = "左半部分"
        else: # right
            # 截取右侧50%的区域
            crop_area = (w // 2, 0, w, h)
            title = "右半部分"
            
        cropped_img = self.original_image.crop(crop_area)
        self.perform_and_display_ocr(f"{title}-原始", cropped_img)
        self.perform_and_display_ocr(f"{title}-放大3倍", cropped_img.resize((cropped_img.width * 3, cropped_img.height * 3), Image.Resampling.LANCZOS))

    def perform_and_display_ocr(self, name, img_to_process):
        """[重构] 执行OCR并显示结果的通用函数"""
        try:
            self.processed_images[name] = img_to_process
            img_np = np.array(img_to_process)
            result, _ = ocr_engine(img_np, char_dict='0123456789-')
            raw_text = " ".join([item[1] for item in result]) if result else "空"
        except Exception as e:
            raw_text = f"错误: {e}"

        frame = ttk.Frame(self.result_frame)
        frame.pack(fill=tk.X, pady=3)
        btn = ttk.Button(frame, text=f"{name}:", width=20, command=lambda n=name: self.preview_image(n))
        btn.pack(side=tk.LEFT)
        ttk.Label(frame, text=raw_text, font=("Consolas", 12, "bold")).pack(side=tk.LEFT, padx=5)

    def preview_image(self, name):
        if name in self.processed_images: self.show_image(self.processed_images[name])

    def clear_results(self):
        for widget in self.result_frame.winfo_children(): widget.destroy()
        self.processed_images.clear()

    def old_round_preprocess(self, image):
        img_gray = image.convert('L'); img_contrast = ImageEnhance.Contrast(img_gray).enhance(2.0)
        img_binary = img_contrast.point(lambda x: 0 if x < 150 else 255)
        return img_binary.resize((img_binary.width * 2, img_binary.height * 2), Image.Resampling.LANCZOS)

    def upscale_then_binary(self, image, factor, threshold):
        upscaled = image.resize((image.width * factor, image.height * factor), Image.Resampling.LANCZOS)
        return upscaled.convert('L').point(lambda x: 0 if x < threshold else 255)

if __name__ == "__main__":
    if not OCR_AVAILABLE:
        root = tk.Tk(); root.withdraw()
        tk.messagebox.showerror("OCR引擎错误", "无法初始化RapidOCR引擎，请检查依赖或查看控制台输出。")
    else:
        app = OCRTestApp(); app.mainloop()