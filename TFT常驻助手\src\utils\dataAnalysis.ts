/**
 * 数据分析工具
 * 用于计算英雄数据的百分位数和颜色编码
 */

export interface HeroData {
  cn_name: string
  cost: number
  play_rate: number
  avg_place: number
  top4_rate: number
  top1_rate: number
  [key: string]: any
}

export interface PercentileData {
  value: number
  percentile: number
  color: string
  level: 'excellent' | 'good' | 'average' | 'poor'
}

export interface CostGroupStats {
  cost: number
  playRatePercentiles: number[]
  avgPlacePercentiles: number[]
  top4RatePercentiles: number[]
  top1RatePercentiles: number[]
}

/**
 * 计算数组的百分位数
 */
function calculatePercentile(values: number[], percentile: number): number {
  const sorted = [...values].sort((a, b) => a - b)
  const index = (percentile / 100) * (sorted.length - 1)
  const lower = Math.floor(index)
  const upper = Math.ceil(index)

  if (lower === upper) {
    return sorted[lower]
  }

  const weight = index - lower
  return sorted[lower] * (1 - weight) + sorted[upper] * weight
}

/**
 * 根据百分位数获取颜色和等级
 */
function getColorByPercentile(percentile: number, isReverse: boolean = false): { color: string; level: 'excellent' | 'good' | 'average' | 'poor' } {
  // 对于平均排名，数值越小越好，所以需要反转
  const adjustedPercentile = isReverse ? 100 - percentile : percentile

  if (adjustedPercentile >= 75) {
    return { color: '#fc1236', level: 'excellent' } // 红色 - 优秀 (前25%)
  } else if (adjustedPercentile >= 50) {
    return { color: '#ff781b', level: 'good' } // 橙色 - 良好 (25%-50%)
  } else if (adjustedPercentile >= 25) {
    return { color: '#ffd607', level: 'average' } // 黄色 - 一般 (50%-75%)
  } else {
    return { color: '#78f91a', level: 'poor' } // 绿色 - 较差 (后25%)
  }
}

/**
 * 计算每个费用组的统计数据
 */
export function calculateCostGroupStats(heroes: HeroData[]): Map<number, CostGroupStats> {
  const costGroups = new Map<number, HeroData[]>()

  // 按费用分组
  heroes.forEach(hero => {
    const cost = hero.cost || 0
    if (!costGroups.has(cost)) {
      costGroups.set(cost, [])
    }
    costGroups.get(cost)!.push(hero)
  })

  const statsMap = new Map<number, CostGroupStats>()

  // 计算每组的百分位数
  costGroups.forEach((groupHeroes, cost) => {
    const playRates = groupHeroes.map(h => h.play_rate || 0)
    const avgPlaces = groupHeroes.map(h => h.avg_place || 0)
    const top4Rates = groupHeroes.map(h => h.top4_rate || 0)
    const top1Rates = groupHeroes.map(h => h.top1_rate || 0)

    const playRatePercentiles = [25, 50, 75].map(p => calculatePercentile(playRates, p))
    const avgPlacePercentiles = [25, 50, 75].map(p => calculatePercentile(avgPlaces, p))
    const top4RatePercentiles = [25, 50, 75].map(p => calculatePercentile(top4Rates, p))
    const top1RatePercentiles = [25, 50, 75].map(p => calculatePercentile(top1Rates, p))

    statsMap.set(cost, {
      cost,
      playRatePercentiles,
      avgPlacePercentiles,
      top4RatePercentiles,
      top1RatePercentiles
    })
  })

  return statsMap
}

/**
 * 获取英雄数据的百分位数信息
 */
export function getHeroPercentileData(
  hero: HeroData,
  costStats: CostGroupStats
): {
  playRate: PercentileData
  avgPlace: PercentileData
  top4Rate: PercentileData
  top1Rate: PercentileData
} {
  const playRate = hero.play_rate || 0
  const avgPlace = hero.avg_place || 0
  const top4Rate = hero.top4_rate || 0
  const top1Rate = hero.top1_rate || 0

  // 计算出场率百分位数
  const playRatePercentile = calculateHeroPercentile(playRate, costStats.playRatePercentiles, false)
  const playRateColor = getColorByPercentile(playRatePercentile, false)

  // 计算平均排名百分位数（数值越小越优秀）
  const avgPlacePercentile = calculateHeroPercentile(avgPlace, costStats.avgPlacePercentiles, true)
  const avgPlaceColor = getColorByPercentile(avgPlacePercentile, false) // 注意这里不反转，因为已经在calculateHeroPercentile中处理了

  // 计算前四率百分位数（数值越大越优秀）
  const top4RatePercentile = calculateHeroPercentile(top4Rate, costStats.top4RatePercentiles, false)
  const top4RateColor = getColorByPercentile(top4RatePercentile, false)

  // 计算登顶率百分位数（数值越大越优秀）
  const top1RatePercentile = calculateHeroPercentile(top1Rate, costStats.top1RatePercentiles, false)
  const top1RateColor = getColorByPercentile(top1RatePercentile, false)

  return {
    playRate: {
      value: playRate,
      percentile: playRatePercentile,
      color: playRateColor.color,
      level: playRateColor.level
    },
    avgPlace: {
      value: avgPlace,
      percentile: avgPlacePercentile,
      color: avgPlaceColor.color,
      level: avgPlaceColor.level
    },
    top4Rate: {
      value: top4Rate,
      percentile: top4RatePercentile,
      color: top4RateColor.color,
      level: top4RateColor.level
    },
    top1Rate: {
      value: top1Rate,
      percentile: top1RatePercentile,
      color: top1RateColor.color,
      level: top1RateColor.level
    }
  }
}

/**
 * 计算单个英雄在其费用组中的百分位数
 */
function calculateHeroPercentile(value: number, percentiles: number[], isReverse: boolean): number {
  const [p25, p50, p75] = percentiles

  if (isReverse) {
    // 对于平均排名，数值越小百分位数越高（排名越好）
    if (value <= p25) return 75 + Math.min(25, (p25 - value) / (p25 * 0.5) * 25)
    if (value <= p50) return 50 + (p50 - value) / (p50 - p25) * 25
    if (value <= p75) return 25 + (p75 - value) / (p75 - p50) * 25
    return Math.max(0, 25 * (1 - (value - p75) / p75))
  } else {
    // 对于出场率，数值越大百分位数越高
    if (value >= p75) return 75 + Math.min(25, (value - p75) / (p75 * 0.5) * 25)
    if (value >= p50) return 50 + (value - p50) / (p75 - p50) * 25
    if (value >= p25) return 25 + (value - p25) / (p50 - p25) * 25
    return Math.max(0, value / p25 * 25)
  }
}

/**
 * 格式化显示数据
 */
export function formatDisplayData(value: number, type: 'playRate' | 'avgPlace' | 'top4Rate' | 'top1Rate'): string {
  if (type === 'playRate') {
    // 出场率格式：直接使用数据库原始数据，不做转换
    return value.toFixed(2)
  } else if (type === 'top4Rate' || type === 'top1Rate') {
    // 前四率和登顶率格式：数据本身就是百分号大小，只需要加上%符号
    return value.toFixed(1) + '%'
  } else {
    return value.toFixed(2)
  }
}

/**
 * 获取数据等级的中文描述
 */
export function getLevelDescription(level: 'excellent' | 'good' | 'average' | 'poor'): string {
  const descriptions = {
    excellent: '优秀',
    good: '良好',
    average: '一般',
    poor: '较差'
  }
  return descriptions[level]
}