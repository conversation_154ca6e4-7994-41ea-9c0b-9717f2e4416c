<template>
  <div :class="badgeVariants({ variant, class: props.class })">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
        outline: 'text-foreground',
        // TFT特定的评级变体
        'tier-s': 'border-transparent bg-tier-s text-text-dark hover:bg-tier-s/80',
        'tier-a': 'border-transparent bg-tier-a text-text-dark hover:bg-tier-a/80',
        'tier-b': 'border-transparent bg-tier-b text-text-dark hover:bg-tier-b/80',
        'tier-c': 'border-transparent bg-tier-c text-text-dark hover:bg-tier-c/80',
        'tier-d': 'border-transparent bg-tier-d text-text-dark hover:bg-tier-d/80',
        // 费用变体
        'cost-1': 'border-transparent bg-cost-1 text-text-dark hover:bg-cost-1/80',
        'cost-2': 'border-transparent bg-cost-2 text-text-dark hover:bg-cost-2/80',
        'cost-3': 'border-transparent bg-cost-3 text-text-dark hover:bg-cost-3/80',
        'cost-4': 'border-transparent bg-cost-4 text-text-light hover:bg-cost-4/80',
        'cost-5': 'border-transparent bg-cost-5 text-text-dark hover:bg-cost-5/80',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

interface Props {
  variant?: VariantProps<typeof badgeVariants>['variant']
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  class: ''
})
</script>
