<template>
  <div
    :class="cn(
      'hero-card bg-section-bg border border-border-color rounded-lg p-3 cursor-pointer transition-all duration-200',
      'hover:bg-item-row-hover hover:border-text-highlight',
      props.class
    )"
    @click="handleClick"
  >
    <!-- 头像区域 -->
    <div class="relative mb-2">
      <div class="hero-avatar w-20 h-20 rounded-md overflow-hidden bg-gray-600 border border-border-color">
        <img
          v-if="hero.icon_path"
          :src="hero.icon_path"
          :alt="hero.cn_name"
          class="w-full h-full object-cover"
          @error="handleImageError"
        />
        <div
          v-else
          class="w-full h-full flex items-center justify-center text-text-medium text-xs"
        >
          {{ hero.cn_name?.slice(0, 2) || '?' }}
        </div>
      </div>
      
      <!-- 费用标识 -->
      <Badge
        :variant="`cost-${hero.cost}` as any"
        class="absolute -top-1 -right-1 w-5 h-5 p-0 text-xs rounded-full flex items-center justify-center"
      >
        {{ hero.cost }}
      </Badge>
    </div>
    
    <!-- 英雄名称 -->
    <div class="text-center mb-2">
      <div class="text-text-light text-sm font-medium truncate">
        {{ hero.cn_name }}
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="space-y-1 text-xs text-text-medium">
      <div class="flex justify-between">
        <span>出场率:</span>
        <span>{{ formatRate(hero.play_rate) }}</span>
      </div>
      <div class="flex justify-between">
        <span>均名:</span>
        <span>{{ formatAvgPlace(hero.avg_place) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui'

interface Hero {
  cn_name: string
  cost: 1 | 2 | 3 | 4 | 5
  icon_path?: string
  play_rate?: number
  avg_place?: number
  traits?: string[]
}

interface Props {
  hero: Hero
  class?: string
}

interface Emits {
  (e: 'click', hero: Hero): void
}

const props = withDefaults(defineProps<Props>(), {
  class: ''
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click', props.hero)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

const formatRate = (rate?: number): string => {
  if (rate === undefined || rate === null) return '--'
  return (rate * 100).toFixed(1) + '%'
}

const formatAvgPlace = (place?: number): string => {
  if (place === undefined || place === null) return '--'
  return place.toFixed(2)
}
</script>

<style scoped>
.hero-card {
  /* 固定卡片尺寸 - 基于UI风格指导 */
  width: 110px;
  min-height: 140px;
  
  /* 防止文字选择 */
  user-select: none;
}

.hero-avatar {
  /* 确保头像区域尺寸 - 80px × 80px */
  width: 80px;
  height: 80px;
}

.hero-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.hero-card:active {
  transform: translateY(0);
}
</style>
