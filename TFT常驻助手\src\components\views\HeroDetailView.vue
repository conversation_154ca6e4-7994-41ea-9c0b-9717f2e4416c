<template>
  <div class="hero-detail-view">
    <!-- 英雄基础信息区域 -->
    <div class="hero-info-section">
      <div class="hero-icon-container">
        <HeroIcon
          :hero-name="heroData.cn_name || '未知英雄'"
          :icon-path="heroData.icon_path"
          :cost="heroData.cost"
          :size="80"
          :show-cost-badge="false"
        />
      </div>

      <div class="hero-basic-info">
        <div class="hero-name-section">
          <h1 class="hero-name">{{ heroData.cn_name || '英雄名称' }}</h1>
          <span class="hero-cost" v-if="heroData.cost">{{ heroData.cost }}费</span>
        </div>
        <div class="hero-stats-grid">
          <div class="stat-item">
            <span class="stat-label">出场率:</span>
            <span class="stat-value">{{ formatPlayRate(heroData.play_rate) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均排名:</span>
            <span class="stat-value">{{ formatAvgPlace(heroData.avg_place) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">前四率:</span>
            <span class="stat-value">{{ formatTop4Rate(heroData.top4_rate) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">登顶率:</span>
            <span class="stat-value">{{ formatTop1Rate(heroData.top1_rate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 装备推荐区域 -->
    <div class="equipment-section">
      <div class="equipment-header">
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="`搜索${getTabLabel(activeTab)}...`"
          class="equipment-search-input"
        />
        <div class="equipment-filters">
          <button
            v-for="tab in equipmentTabs"
            :key="tab.key"
            class="filter-tab"
            :class="{ active: activeTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            {{ tab.label }}
          </button>
        </div>
      </div>

      <div class="equipment-content">
        <EquipmentRecommendList
          :item-count="activeTab"
          :equipment-data="equipmentData[activeTab] || []"
          :loading="isLoadingEquipment"
          :search-query="searchQuery"
          @item-selected="handleItemSelected"
          @magnify-requested="handleMagnifyRequest"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import HeroIcon from '@/components/common/HeroIcon.vue'
import EquipmentRecommendList from './EquipmentRecommendList.vue'
import type { QueryResult } from '@/types'

// Props
interface Props {
  heroName: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  itemSelected: [itemName: string]
  backToList: []
}>()

// 响应式数据
const heroData = ref<any>({})
const equipmentData = ref<Record<number, any[]>>({
  1: [],
  2: [],
  3: []
})
const isLoadingHero = ref(true)
const isLoadingEquipment = ref(true)
const activeTab = ref<number>(1)

// 装备标签页配置
const equipmentTabs = [
  { key: 1, label: '单装备' },
  { key: 2, label: '双装备' },
  { key: 3, label: '三装备' }
]

// 搜索查询
const searchQuery = ref('')

// 计算属性
const formatPlayRate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return rate.toFixed(1)
}

const formatAvgPlace = (place: number | null | undefined) => {
  if (place == null) return '--'
  return place.toFixed(2)
}

const formatTop4Rate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return `${rate.toFixed(1)}%`
}

const formatTop1Rate = (rate: number | null | undefined) => {
  if (rate == null) return '--'
  return `${rate.toFixed(1)}%`
}

// 方法
const switchTab = (tabKey: number) => {
  activeTab.value = tabKey
}

// 获取标签页标签
const getTabLabel = (tabKey: number) => {
  const tab = equipmentTabs.find(t => t.key === tabKey)
  return tab ? tab.label : '装备'
}

const handleItemSelected = (itemName: string) => {
  emit('itemSelected', itemName)
}

const handleMagnifyRequest = (currentItems: string[]) => {
  const currentCount = currentItems.length
  const targetCount = currentCount + 1
  if (targetCount > 3) return

  // 切换到目标标签页
  activeTab.value = targetCount
  
  // 这里可以添加搜索逻辑，筛选包含当前装备的组合
  console.log(`放大镜请求: 从 ${currentCount}件套 (${currentItems}) 查找 ${targetCount}件套`)
}

const loadHeroData = async () => {
  if (!props.heroName) return
  
  console.log(`开始加载英雄详情: ${props.heroName}`)
  isLoadingHero.value = true
  
  try {
    // 加载英雄基础信息
    const heroResult = await invoke<QueryResult>('get_hero_detail', { heroName: props.heroName })
    console.log('英雄基础信息:', heroResult)
    
    // 处理标准的QueryResult格式
    if (heroResult && typeof heroResult === 'object' && 'data' in heroResult) {
      const queryResult = heroResult as QueryResult
      if (queryResult.data && Array.isArray(queryResult.data) && queryResult.data.length > 0) {
        heroData.value = queryResult.data[0]
      }
    }
    // 处理直接返回数组的情况（向后兼容）
    else if (Array.isArray(heroResult)) {
      const arrayResult = heroResult as any[]
      if (arrayResult.length > 0) {
        console.warn('收到非标准格式的英雄数据，使用兼容模式处理')
        heroData.value = arrayResult[0]
      }
    }
  } catch (error) {
    console.error('加载英雄基础信息失败:', error)
  } finally {
    isLoadingHero.value = false
  }
}

const loadEquipmentData = async () => {
  if (!props.heroName) return

  console.log(`开始加载英雄装备数据: ${props.heroName}`)
  isLoadingEquipment.value = true

  try {
    // 加载英雄装备统计信息
    const equipResult = await invoke<QueryResult>('get_hero_item_stats', { heroName: props.heroName })
    console.log('英雄装备统计:', equipResult)
    
    let equipData: any[] = []
    if (equipResult && typeof equipResult === 'object' && 'data' in equipResult) {
      equipData = (equipResult as { data: any[] }).data || []
    } else if (Array.isArray(equipResult)) {
      equipData = equipResult
    }
    
    // 为每个装备项获取图标路径
    for (const item of equipData) {
      if (item.item_names) {
        const itemNames = item.item_names.split('|').filter((name: string) => name.trim())
        const iconPaths: string[] = []
        
        for (const itemName of itemNames) {
          try {
            const iconResult = await invoke<QueryResult>('get_item_icon_path', { itemName: itemName.trim() })
            if (iconResult && !iconResult.error && iconResult.data && iconResult.data.length > 0) {
              iconPaths.push(iconResult.data[0].icon_path)
            } else {
              console.warn(`未找到装备 ${itemName} 的图标路径`, iconResult?.error || '无数据')
              iconPaths.push('')
            }
          } catch (error) {
            console.error(`获取装备 ${itemName} 图标路径失败:`, error)
            iconPaths.push('')
          }
        }
        
        item.icon_paths = iconPaths
      }
    }
    
    // 按装备数量分组
    const groupedData: Record<number, any[]> = { 1: [], 2: [], 3: [] }
    equipData.forEach(item => {
      const count = item.item_count
      if (count >= 1 && count <= 3) {
        groupedData[count].push(item)
      }
    })
    
    equipmentData.value = groupedData
    console.log('处理后的装备数据:', groupedData)
  } catch (error) {
    console.error('加载英雄装备数据失败:', error)
  } finally {
    isLoadingEquipment.value = false
  }
}

// 监听英雄名称变化
watch(() => props.heroName, (newName) => {
  if (newName) {
    loadHeroData()
    loadEquipmentData()
  }
}, { immediate: true })

// 组件挂载时加载数据
onMounted(() => {
  if (props.heroName) {
    loadHeroData()
    loadEquipmentData()
  }
})
</script>

<style scoped>
.hero-detail-view {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
  background: transparent;
}

/* 英雄基础信息区域 */
.hero-info-section {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem 1rem 1rem 2rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1.5rem;
  height: 120px;
  box-sizing: border-box;
}

.hero-icon-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
}

.hero-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.5rem;
  height: 100%;
  min-width: 0;
  max-width: 400px;
}

.hero-name-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.1rem;
}

.hero-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.hero-cost {
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.hero-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0.4rem 1.2rem;
  align-items: start;
  width: 100%;
}

.stat-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
}

.stat-value {
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  line-height: 1.2;
}

/* 装备推荐区域 */
.equipment-section {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.6rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow: hidden;
}

.equipment-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 1.5rem;
  flex-shrink: 0;
  padding: 0.4rem;
}

.equipment-search-input {
  width: 220px;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 13px;
  outline: none;
  transition: all 0.2s ease;
}

.equipment-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.equipment-search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.equipment-filters {
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
}

.filter-tab {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tab:hover {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.filter-tab.active {
  background: linear-gradient(135deg, rgba(88, 86, 134, 0.8), rgba(65, 95, 160, 0.8));
  color: white;
  font-weight: 600;
  border-color: rgba(88, 86, 134, 0.6);
  box-shadow: 0 0 10px rgba(88, 86, 134, 0.3);
}

.equipment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}
</style>
