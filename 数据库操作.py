# 数据库操作.py

import sqlite3
import time
import traceback
import gc
from PySide6.QtCore import QObject, Signal, QRunnable, Slot, QThreadPool
from 常量与配置 import (DB_FILE, MAX_THREADS, QUERY_CACHE, CACHE_EXPIRY_SECONDS,
                    MAX_QUERY_CACHE_SIZE, ENABLE_QUERY_CACHE_LIMIT)

# --- 查询缓存管理 ---
QUERY_CACHE_ACCESS_TIME = {}  # {query_key: last_access_time}
QUERY_CACHE_LRU = []  # 记录query_key的访问顺序，最近访问的放在最后

def check_and_clean_query_cache():
    """检查并清理查询缓存，如果超出阈值"""
    if not ENABLE_QUERY_CACHE_LIMIT or len(QUERY_CACHE) <= MAX_QUERY_CACHE_SIZE:
        return
        
    # 基于访问时间清理缓存
    print(f"查询缓存达到阈值 ({len(QUERY_CACHE)}个查询)，开始清理...")
    keys_with_time = [(k, QUERY_CACHE_ACCESS_TIME.get(k, 0)) for k in QUERY_CACHE.keys()]
    # 按访问时间排序，保留最近使用的
    sorted_keys = sorted(keys_with_time, key=lambda x: x[1])
    # 计算需要删除的条目数
    to_remove = max(0, len(QUERY_CACHE) - MAX_QUERY_CACHE_SIZE//2)  # 每次清理一半
    
    # 删除最旧的缓存条目
    for i in range(to_remove):
        if i < len(sorted_keys):
            key_to_remove = sorted_keys[i][0]
            if key_to_remove in QUERY_CACHE:
                del QUERY_CACHE[key_to_remove]
            if key_to_remove in QUERY_CACHE_ACCESS_TIME:
                del QUERY_CACHE_ACCESS_TIME[key_to_remove]
                
    # 触发垃圾回收
    gc.collect()
    print(f"查询缓存清理完成，现有缓存数量: {len(QUERY_CACHE)}个")
    
def clear_query_cache():
    """完全清空查询缓存"""
    QUERY_CACHE.clear()
    QUERY_CACHE_ACCESS_TIME.clear()
    QUERY_CACHE_LRU.clear()
    print("查询缓存已完全清空")
    gc.collect()

# --- 数据库连接管理 ---
_connection_pool = None # 简单的线程本地连接（或后续考虑连接池）

def get_db_connection():
    """获取数据库连接 (线程安全)"""
    # 这里使用简单的每次创建新连接的方式，适用于 SQLite 的多线程模式
    # 如果性能瓶颈在于连接创建，可以考虑更复杂的连接池或线程本地存储
    try:
        conn = sqlite3.connect(DB_FILE, check_same_thread=False) # 允许在不同线程使用
        conn.row_factory = sqlite3.Row # 允许按列名访问
        conn.execute("PRAGMA foreign_keys = ON;") # 确保启用外键
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}")
        return None

def close_db_connection(conn):
    """关闭数据库连接"""
    if conn:
        try:
            conn.close()
        except sqlite3.Error as e:
            print(f"关闭数据库连接时出错: {e}")

# --- 异步查询框架 ---

class WorkerSignals(QObject):
    """定义工作线程可以发出的信号"""
    result = Signal(object)  # 查询成功，发送结果 (通常是 list of dicts)
    error = Signal(str)      # 查询出错，发送错误信息
    finished = Signal()      # 任务完成信号

class DatabaseWorker(QRunnable):
    """执行数据库查询的工作线程"""
    def __init__(self, sql, params=None, query_key=None):
        super().__init__()
        self.sql = sql
        self.params = params if params is not None else []
        self.query_key = query_key # 用于缓存的键
        self.signals = WorkerSignals()

    @Slot()
    def run(self):
        """执行查询任务"""
        conn = None
        try:
            # 检查缓存
            if self.query_key and self.query_key in QUERY_CACHE:
                timestamp, cached_result = QUERY_CACHE[self.query_key]
                current_time = time.time()
                
                if current_time - timestamp < CACHE_EXPIRY_SECONDS:
                    # 更新访问时间，用于LRU策略
                    QUERY_CACHE_ACCESS_TIME[self.query_key] = current_time
                    
                    # 更新LRU列表
                    if self.query_key in QUERY_CACHE_LRU:
                        QUERY_CACHE_LRU.remove(self.query_key)
                    QUERY_CACHE_LRU.append(self.query_key)
                    
                    # 从缓存返回结果
                    self.signals.result.emit(cached_result)
                    self.signals.finished.emit()
                    return # 从缓存返回

            # print(f"执行查询: {self.sql[:100]}... Params: {self.params}") # Debug
            conn = get_db_connection()
            if not conn:
                raise sqlite3.Error("无法获取数据库连接")

            cursor = conn.cursor()
            start_time = time.time() # 记录查询时间
            cursor.execute(self.sql, self.params)
            # 对于 SELECT 语句，获取所有结果
            if self.sql.strip().upper().startswith("SELECT"):
                # 将 sqlite3.Row 转换为字典列表，方便使用
                results = [dict(row) for row in cursor.fetchall()]
            else:
                # 对于 INSERT/UPDATE/DELETE，可能不需要结果，或者返回 lastrowid/rowcount
                conn.commit() # 确保提交更改
                results = cursor.rowcount # 返回影响的行数作为结果示例

            end_time = time.time()
            # print(f"查询完成 ({end_time - start_time:.4f}s): {self.sql[:50]}...") # Debug

            # 更新缓存
            if self.query_key:
                # 检查缓存大小，必要时清理
                check_and_clean_query_cache()
                
                current_time = time.time()
                QUERY_CACHE[self.query_key] = (current_time, results)
                QUERY_CACHE_ACCESS_TIME[self.query_key] = current_time
                
                # 更新LRU列表
                if self.query_key in QUERY_CACHE_LRU:
                    QUERY_CACHE_LRU.remove(self.query_key)
                QUERY_CACHE_LRU.append(self.query_key)

            self.signals.result.emit(results)

        except sqlite3.Error as e:
            error_msg = f"数据库查询错误: {e}\nSQL: {self.sql}\nParams: {self.params}"
            print(error_msg)
            traceback.print_exc() # 打印详细堆栈到控制台
            self.signals.error.emit(f"数据库查询失败: {e}") # 发送简化错误信息给UI
        except Exception as e:
            error_msg = f"执行数据库任务时发生意外错误: {e}\nSQL: {self.sql}\nParams: {self.params}"
            print(error_msg)
            traceback.print_exc()
            self.signals.error.emit(f"查询时发生错误: {e}")
        finally:
            close_db_connection(conn)
            self.signals.finished.emit() # 确保 finished 信号总是发出

# --- 全局线程池 ---
# 限制最大线程数，避免资源耗尽
thread_pool = QThreadPool()
thread_pool.setMaxThreadCount(MAX_THREADS)
print(f"数据库操作线程池已初始化，最大线程数: {thread_pool.maxThreadCount()}")

def execute_query_async(sql, params=None, on_success=None, on_error=None, query_key=None):
    """
    异步执行数据库查询。

    Args:
        sql (str): 要执行的 SQL 语句。
        params (tuple, optional): SQL 语句的参数。Defaults to None.
        on_success (callable, optional): 查询成功时调用的回调函数，接收查询结果 (list of dicts)。
        on_error (callable, optional): 查询失败时调用的回调函数，接收错误信息 (str)。
        query_key (str, optional): 用于缓存结果的唯一键。如果提供，将尝试使用缓存。
    """
    # 检查线程池是否太满
    if thread_pool.activeThreadCount() > MAX_THREADS * 0.8:  # 超过80%容量
        print(f"警告: 线程池负载较高 ({thread_pool.activeThreadCount()}/{MAX_THREADS})")
        
    worker = DatabaseWorker(sql, params, query_key)

    # 连接信号到槽函数
    if on_success:
        worker.signals.result.connect(on_success)
    if on_error:
        worker.signals.error.connect(on_error)
        
    def _cleanup_resources():
        # 完成查询后进行清理工作
        pass
        
    worker.signals.finished.connect(_cleanup_resources)

    # 将任务提交到线程池执行
    thread_pool.start(worker)

# --- 同步查询函数 (可选，用于简单或必须同步的场景) ---
def execute_query_sync(sql, params=None):
    """同步执行数据库查询 (主要用于非 UI 线程或简单场景)"""
    conn = None
    results = None
    error = None
    try:
        conn = get_db_connection()
        if not conn:
            raise sqlite3.Error("无法获取数据库连接")
        cursor = conn.cursor()
        cursor.execute(sql, params if params is not None else [])
        if sql.strip().upper().startswith("SELECT"):
            results = [dict(row) for row in cursor.fetchall()]
        else:
            conn.commit()
            results = cursor.rowcount
    except sqlite3.Error as e:
        error = f"数据库查询错误: {e}\nSQL: {sql}\nParams: {params}"
        print(error)
    except Exception as e:
        error = f"执行数据库任务时发生意外错误: {e}\nSQL: {sql}\nParams: {params}"
        print(error)
    finally:
        close_db_connection(conn)
    return results, error

# --- 示例用法 (通常在视图模块中调用 execute_query_async) ---
# def handle_results(data):
#     print("查询成功:", data)
#
# def handle_error(error_message):
#     print("查询失败:", error_message)
#
# # 异步查询示例
# sql_example = "SELECT name, tier FROM comps_base WHERE tier = ? ORDER BY name"
# params_example = ('S',)
# query_key_example = f"comps_base_tier_{params_example[0]}" # 创建缓存键
# execute_query_async(sql_example, params_example, handle_results, handle_error, query_key_example)

# # 同步查询示例
# results, error = execute_query_sync("SELECT COUNT(*) as count FROM heroes")
# if error:
#     print("同步查询错误:", error)
# else:
#     print("同步查询结果:", results[0]['count'] if results else None)