#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import asyncio
import json
import random
import time
import traceback
import re
import shutil
from pathlib import Path
import aiohttp
from playwright.async_api import async_playwright, TimeoutError

class CompScraper:
    """云顶之弈阵容数据爬虫"""
    
    def __init__(self, base_dir=None, max_concurrent=3, min_delay=1.5, max_delay=3.0, clean_old_data=True):
        # 基础目录设置
        self.base_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        self.comp_data_dir = os.path.join(self.base_dir, "阵容数据")
        self.mapping_dir = os.path.join(self.base_dir, "映射文件")
        self.config_dir = os.path.join(self.base_dir, "配置文件")
        
        # 创建必要的目录
        os.makedirs(self.comp_data_dir, exist_ok=True)
        os.makedirs(self.mapping_dir, exist_ok=True)
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 爬虫设置
        self.max_concurrent = max_concurrent
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.comps_url = "https://www.metatft.com/comps"
        self.api_base_url = "https://api-hc.metatft.com/tft-comps-api/comp_details"
        
        # 动态获取cluster_id
        self.cluster_id = "340"  # 默认值，如果无法获取最新值则使用此值
        self._load_or_fetch_cluster_id()
        
        # 数据存储
        self.comp_data = []
        self.comp_mapping = {}
        
        # 清理旧数据
        if clean_old_data:
            self._clean_old_data()
        
        # 加载现有的映射数据
        self._load_existing_mappings()
        
    def _clean_old_data(self):
        """清理旧的阵容数据，保证数据最新"""
        try:
            print("正在清理旧的阵容数据...")
            # 清理阵容数据目录中的所有JSON文件
            if os.path.exists(self.comp_data_dir):
                for filename in os.listdir(self.comp_data_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(self.comp_data_dir, filename)
                        os.remove(file_path)
                        print(f"已删除旧文件: {file_path}")
            
            # 确保保留目录结构
            os.makedirs(self.comp_data_dir, exist_ok=True)
            
            # 清理阵容映射文件
            mapping_file = os.path.join(self.mapping_dir, "阵容数据映射.json")
            if os.path.exists(mapping_file):
                os.remove(mapping_file)
                print(f"已删除旧映射文件: {mapping_file}")
            
            print("旧数据清理完成")
        except Exception as e:
            print(f"清理旧数据时出错: {e}")
            print(traceback.format_exc())
        
    def _load_existing_mappings(self):
        """加载现有的阵容映射文件"""
        mapping_file = os.path.join(self.mapping_dir, "阵容数据映射.json")
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.comp_mapping = json.load(f)
                    print(f"成功加载现有阵容映射数据: {len(self.comp_mapping)} 条记录")
            except Exception as e:
                print(f"加载阵容映射文件失败: {e}")
                self.comp_mapping = {}
    
    async def add_random_delay(self):
        """添加随机延迟，避免请求过快"""
        delay = random.uniform(self.min_delay, self.max_delay)
        await asyncio.sleep(delay)
    
    async def fetch_comp_details(self, comp_id, comp_name):
        """获取阵容详细数据
        
        Args:
            comp_id: 阵容ID
            comp_name: 阵容名称
            
        Returns:
            bool: 是否成功获取并保存数据
        """
        # 构造API请求URL
        url = f"{self.api_base_url}?comp={comp_id}&cluster_id={self.cluster_id}"
        
        print(f"正在获取阵容 '{comp_name}' (ID: {comp_id}) 的详细数据...")
        
        # 设置最大重试次数
        max_retries = 3
        retry_delay = 2
        
        for retry in range(max_retries):
            try:
                # 发送API请求
                timeout = aiohttp.ClientTimeout(total=30)  # 设置30秒超时
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # 添加请求头，模拟浏览器
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                        'Accept': 'application/json',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Referer': 'https://www.metatft.com/',
                        'Origin': 'https://www.metatft.com'
                    }
                    
                    async with session.get(url, headers=headers) as response:
                        if response.status != 200:
                            # 尝试获取错误详情
                            try:
                                error_text = await response.text()
                                print(f"获取阵容详细数据失败，状态码: {response.status}")
                                print(f"错误详情: {error_text[:200]}")
                                
                                # 检查是否是cluster_id过期问题
                                if response.status == 500 and ('cluster' in error_text.lower() or 'version' in error_text.lower() or 'Internal Server Error' in error_text):
                                    print("检测到可能的cluster_id过期问题，尝试重新获取最新的cluster_id...")
                                    # 强制重新获取最新的cluster_id
                                    old_cluster_id = self.cluster_id
                                    await self._fetch_latest_cluster_id()
                                    
                                    if self.cluster_id != old_cluster_id:
                                        print(f"cluster_id已更新：{old_cluster_id} -> {self.cluster_id}")
                                        # 使用新的cluster_id重新构造URL
                                        url = f"{self.api_base_url}?comp={comp_id}&cluster_id={self.cluster_id}"
                                        print(f"使用新的cluster_id重试...")
                                        # 重试当前请求(不增加重试计数)
                                        continue
                                    else:
                                        print("无法获取新的cluster_id，可能是网络问题或API变更")
                            except Exception as text_err:
                                print(f"获取错误详情失败: {text_err}")
                            
                            if retry < max_retries - 1:
                                print(f"将在 {retry_delay} 秒后重试 ({retry+2}/{max_retries})...")
                                await asyncio.sleep(retry_delay)
                                continue
                            return False
                        
                        # 解析JSON数据
                        raw_data = await response.json()
                        
                        # 处理API返回的数据
                        processed_data = self._process_comp_details(raw_data)
                        
                        # 保存处理后的数据
                        self._save_comp_details(comp_name, processed_data)
                        
                        print(f"已成功获取并保存阵容 '{comp_name}' 的详细数据")
                        return True
            
            except asyncio.TimeoutError:
                print(f"获取阵容 '{comp_name}' 详细数据超时")
                if retry < max_retries - 1:
                    print(f"将在 {retry_delay} 秒后重试 ({retry+2}/{max_retries})...")
                    await asyncio.sleep(retry_delay)
                continue
            except Exception as e:
                print(f"获取阵容 '{comp_name}' 详细数据时出错: {e}")
                print(traceback.format_exc())
                if retry < max_retries - 1:
                    print(f"将在 {retry_delay} 秒后重试 ({retry+2}/{max_retries})...")
                    await asyncio.sleep(retry_delay)
                continue
        
        print(f"获取阵容 '{comp_name}' 详细数据失败，已达到最大重试次数")
        return False
    
    def _process_comp_details(self, raw_data):
        """处理API返回的阵容详细数据，按指定格式重组
        
        Args:
            raw_data: API返回的原始JSON数据
            
        Returns:
            dict: 处理后的数据
        """
        try:
            if not raw_data.get("results"):
                print("API返回的数据中没有'results'字段")
                return {}
            
            results = raw_data["results"]
            
            # 目标结构初始化
            processed_data = {
                "overall_comp_stats": {},
                "unit_overall_stats": [],
                "item_overall_stats": [],
                "trait_overall_stats": [],  # 新增羁绊统计字段
                "recommended_compositions_by_level": {}
            }
            
            # 1. 提取整体阵容统计
            # 尝试从 overall 字段获取，如果不存在则从 placements 列表的第一个元素获取
            overall_stats = results.get("overall")
            if not overall_stats and results.get("placements"):
                overall_stats = results["placements"][0]
            
            if overall_stats:
                processed_data["overall_comp_stats"] = {
                    "total_games": overall_stats.get("count"),
                    "avg_placement": overall_stats.get("avg")
                }
            else:
                print("警告: 未找到整体阵容统计数据。")
                processed_data["overall_comp_stats"] = {
                    "total_games": None,
                    "avg_placement": None
                }
            
            # 获取总场次用于计算出现率
            total_games = processed_data["overall_comp_stats"]["total_games"]
            
            # 2. 提取英雄整体统计
            unit_stats_list = results.get("unit_stats", [])
            for unit_stat in unit_stats_list:
                processed_data["unit_overall_stats"].append({
                    "unit_id": unit_stat.get("unit"),
                    "appearance_rate": unit_stat.get("pcnt"),
                    "avg_placement": unit_stat.get("avg"),
                    "total_count": unit_stat.get("count")
                })
            
            # 2.1 新增：提取羁绊整体统计
            trait_stats_list = results.get("traits", [])
            for trait_stat in trait_stats_list:
                trait_id = trait_stat.get("trait")
                trait_count = trait_stat.get("count")
                # 计算羁绊在该阵容总场次中的出现率
                appearance_rate = (trait_count / total_games) if total_games and total_games > 0 and trait_count is not None else 0
                
                processed_data["trait_overall_stats"].append({
                    "trait_id": trait_id,
                    "appearance_rate": appearance_rate,
                    "avg_placement": trait_stat.get("avg"),
                    "total_count": trait_count,
                    "tier": trait_stat.get("tier")  # 如果API返回羁绊等级
                })
            
            # 3. 提取装备整体统计
            item_names_list = results.get("itemNames", [])
            for item_stat in item_names_list:
                item_data = {
                    "item_id": item_stat.get("itemNames"),
                    "overall_appearance_rate": item_stat.get("pcnt"),
                    "overall_avg_placement": item_stat.get("avg"),
                    "overall_count": item_stat.get("count"),
                    "unit_specific_stats": [] # 初始化嵌套列表
                }
                
                # 提取装备在特定单位身上的统计
                unit_specific_list = item_stat.get("units", [])
                for unit_specific in unit_specific_list:
                    item_data["unit_specific_stats"].append({
                        "unit_id": unit_specific.get("units"),
                        "avg_placement_on_unit": unit_specific.get("avg"),
                        "count_on_unit": unit_specific.get("count"),
                        "unit_pick_rate": unit_specific.get("unit_pick"),
                        "item_pick_rate": unit_specific.get("item_pick")
                    })
                processed_data["item_overall_stats"].append(item_data)
            
            # 4. 提取按等级推荐的阵容构成 (从 results.options)
            recommended_options = results.get("options", {})
            for level_key, comp_list in recommended_options.items():
                # 确保等级键是字符串，并添加到目标结构中
                level_str = str(level_key)
                processed_data["recommended_compositions_by_level"][level_str] = []
                
                for comp_data in comp_list:
                    # 解析 units_list 
                    units = []
                    units_list_str = comp_data.get("units_list", "")
                    
                    if isinstance(units_list_str, str):
                        # 如果是以 '&' 分隔的字符串形式
                        if '&' in units_list_str:
                            units = units_list_str.split('&')
                            units = [unit for unit in units if unit]
                        # 如果是以 ', ' 分隔或 []括起的格式
                        elif '[' in units_list_str or ', ' in units_list_str:
                            units = units_list_str.strip("[]").replace("'", "").split(", ")
                            units = [unit for unit in units if unit]
                    elif isinstance(units_list_str, list):
                        # 如果已经是列表形式
                        units = units_list_str
                    
                    # 解析 traits_list
                    traits = []
                    traits_list_str = comp_data.get("traits_list", "")
                    
                    if isinstance(traits_list_str, str):
                        # 1. 尝试解析 '&' 分隔的形式，如 "TFT9_Redeemer_1&TFT9_Slayer_1"
                        if '&' in traits_list_str:
                            trait_strings = traits_list_str.split('&')
                            for trait_str in trait_strings:
                                match = re.match(r"(.+)_(\d+)$", trait_str)
                                if match:
                                    trait_id = match.group(1)
                                    level = int(match.group(2))
                                    traits.append({"trait_id": trait_id, "level": level})
                                else:
                                    traits.append({"trait_id": trait_str, "level": None})
                        # 2. 尝试解析 JSON 字符串形式，如 "{'Slayer': 3, 'Bruiser': 2}"
                        elif '{' in traits_list_str:
                            try:
                                # 正则表达式提取特质和等级
                                trait_matches = re.findall(r"'([^']+)':\s*(\d+)", traits_list_str)
                                for trait_id, level in trait_matches:
                                    traits.append({
                                        "trait_id": trait_id,
                                        "level": int(level)
                                    })
                            except Exception as e:
                                print(f"解析特质列表失败: {e}, 原始字符串: {traits_list_str}")
                    elif isinstance(traits_list_str, dict):
                        # 字典形式，如 {"Slayer": 3, "Bruiser": 2}
                        for trait_id, level in traits_list_str.items():
                            traits.append({
                                "trait_id": trait_id,
                                "level": int(level) if isinstance(level, (int, str)) else level
                            })
                    elif isinstance(traits_list_str, list):
                        # 列表形式，可能是字典的列表或已经是目标格式
                        for trait_item in traits_list_str:
                            if isinstance(trait_item, dict) and "trait_id" in trait_item and "level" in trait_item:
                                traits.append(trait_item)
                            elif isinstance(trait_item, dict):
                                # 只有一个键值对的字典列表，如 [{"Slayer": 3}, {"Bruiser": 2}]
                                for trait_id, level in trait_item.items():
                                    traits.append({
                                        "trait_id": trait_id,
                                        "level": int(level) if isinstance(level, (int, str)) else level
                                    })
                    
                    processed_data["recommended_compositions_by_level"][level_str].append({
                        "units": units,
                        "traits": traits,
                        "avg_placement": comp_data.get("avg"),
                        "count": comp_data.get("count")
                    })
            
            return processed_data
            
        except Exception as e:
            print(f"处理阵容详细数据时出错: {e}")
            print(traceback.format_exc())
            return {}
    
    def _save_comp_details(self, comp_name, data):
        """保存处理后的阵容详细数据
        
        Args:
            comp_name: 阵容名称
            data: 处理后的数据
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 安全处理文件名
            safe_name = self._sanitize_filename(comp_name)
            file_path = os.path.join(self.comp_data_dir, f"{safe_name}.json")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"已保存阵容 '{comp_name}' 的详细数据到: {file_path}")
            return True
        
        except Exception as e:
            print(f"保存阵容 '{comp_name}' 详细数据时出错: {e}")
            print(traceback.format_exc())
            return False
    
    def _sanitize_filename(self, filename):
        """安全处理文件名，移除不允许的字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 处理后的安全文件名
        """
        # 替换Windows文件名中不允许的字符
        for char in ['\\', '/', ':', '*', '?', '"', '<', '>', '|']:
            filename = filename.replace(char, '_')
        return filename
    
    async def scrape_comps(self):
        """爬取阵容数据主函数"""
        print("开始爬取阵容数据...")
        print(f"当前使用的cluster_id: {self.cluster_id}")
        async with async_playwright() as playwright:
            # 启动浏览器，配置优化选项
            print("正在配置浏览器...")
            
            # 配置浏览器选项
            browser_args = [
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-accelerated-2d-canvas",
                "--disable-infobars",
                "--disable-extensions",
                "--disable-notifications",
                "--disable-popup-blocking",
                # 禁用不必要的资源加载
                "--block-new-web-contents",
                # 禁用默认浏览器检查
                "--no-default-browser-check",
                # 设置较低的内存限制
                "--js-flags=--max-old-space-size=512",
                # 用户代理
                f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
            ]
            
            try:
                # 启动浏览器
                browser = await playwright.chromium.launch(
                    headless=True,
                    args=browser_args,
                    # 使用较低的内存限制
                    handle_sigint=True,
                    handle_sigterm=True,
                    handle_sighup=True,
                    slow_mo=50
                )
                print("浏览器启动成功")
                
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    locale='zh-CN',
                    timezone_id='Asia/Shanghai',
                    # 使用低内存配置
                    bypass_csp=True,
                    # 使用有效UA
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
                )
                
                # 添加随机模拟用户行为的函数
                async def simulate_human_behavior(page):
                    try:
                        # 随机滚动
                        await page.evaluate("""
                            () => {
                                window.scrollTo({
                                    top: Math.random() * document.body.scrollHeight,
                                    behavior: 'smooth'
                                });
                            }
                        """)
                        await asyncio.sleep(random.uniform(0.5, 1.5))
                    except Exception as e:
                        print(f"模拟人类行为时出错: {e}")
                
                page = await context.new_page()
                
                # 设置更合理的超时和加载策略
                print("设置页面加载策略...")
                page.set_default_navigation_timeout(120000)  # 设置更长的导航超时时间
                page.set_default_timeout(60000)  # 设置一般操作的超时时间
                
                try:
                    # 访问阵容页面
                    print(f"正在访问网页: {self.comps_url}")
                    
                    # 使用更稳健的方式加载页面，采用分阶段策略
                    try:
                        print("正在进行页面导航...")
                        # 先使用domcontentloaded策略，确保基本HTML加载完成
                        response = await page.goto(
                            self.comps_url, 
                            wait_until="domcontentloaded",  # 仅等待DOM加载，不等待所有资源
                            timeout=60000
                        )
                        
                        if not response:
                            print("警告: 页面导航未返回响应对象")
                        elif not response.ok:
                            print(f"警告: 页面响应状态码非成功: {response.status}")
                        else:
                            print(f"页面导航成功，状态码: {response.status}")
                            
                    except TimeoutError as e:
                        print(f"页面导航超时: {e}")
                        print(f"错误详情: {traceback.format_exc()}")
                        # 这里不立即退出，尝试继续处理可能已部分加载的页面
                        
                    except Exception as e:
                        print(f"页面导航发生异常: {e}")
                        print(f"错误详情: {traceback.format_exc()}")
                    
                    # 等待基本内容加载
                    print("等待页面基本内容加载...")
                    try:
                        await page.wait_for_load_state("domcontentloaded", timeout=30000)
                        print("DOM内容已加载")
                    except Exception as e:
                        print(f"等待DOM内容加载失败: {e}")
                    
                    # 增加额外等待时间让页面充分渲染
                    print("等待JavaScript渲染...")
                    await asyncio.sleep(5)
                    
                    # 设置中文语言
                    print("尝试将语言设置为中文...")
                    try:
                        # 使用JavaScript设置localStorage中的language值为zh_cn
                        await page.evaluate("localStorage.setItem('language', 'zh_cn');")
                        print("已设置localStorage中的language为zh_cn")
                        
                        # 刷新页面以应用新的语言设置
                        await page.reload(wait_until="domcontentloaded", timeout=30000)
                        print("已刷新页面以应用语言设置")
                        
                        # 验证语言是否已切换
                        current_language = await page.evaluate("localStorage.getItem('language');")
                        print(f"当前语言设置: {current_language}")
                        
                        # 再次等待以确保页面渲染完成
                        await asyncio.sleep(5)
                    except Exception as e:
                        print(f"设置中文语言时出错: {e}")
                        print(traceback.format_exc())
                    
                    # 模拟人类行为
                    await simulate_human_behavior(page)
                    
                    # 检查页面标题和内容，确认页面已正确加载
                    try:
                        title = await page.title()
                        print(f"页面标题: {title}")
                        
                        # 获取页面文本内容的一部分，判断是否有效加载
                        text_content = await page.evaluate("document.body.textContent.substring(0, 1000)")
                        print(f"页面内容片段: {text_content[:200]}...")
                    except Exception as e:
                        print(f"获取页面基本信息失败: {e}")
                    
                    # 尝试找到阵容列表容器
                    print("查找阵容容器...")
                    
                    # 添加调试功能：等待关键元素加载完成
                    print("等待阵容数据加载完成...")
                    try:
                        # 等待第一个阵容行出现，最多等待30秒
                        print("等待第一个阵容行 #row_1 出现...")
                        await page.wait_for_selector("#row_1", timeout=30000)
                        print("✅ 第一个阵容行已加载")
                        
                        # 等待阵容标题加载
                        print("等待阵容标题元素加载...")
                        await page.wait_for_selector("div.CompRowName div.Comp_Title", timeout=10000)
                        print("✅ 阵容标题元素已加载")
                        
                        # 额外等待，确保所有动态内容都加载完成
                        print("等待所有动态内容加载完成...")
                        await asyncio.sleep(3)
                        
                    except TimeoutError as e:
                        print(f"❌ 等待元素加载超时: {e}")
                        # 保存当前页面状态用于调试
                        # 继续尝试，可能部分内容已加载
                        print("尝试继续处理可能已加载的内容...")
                    
                    # 调试：检查当前页面状态
                    print("检查当前页面中的阵容元素...")
                    try:
                        # 检查是否存在预期的元素
                        row_1_exists = await page.query_selector("#row_1")
                        comp_rows_count = len(await page.query_selector_all("div.CompRow"))
                        comp_titles_count = len(await page.query_selector_all("div.Comp_Title"))
                        
                        print(f"调试信息:")
                        print(f"  - #row_1 是否存在: {'✅ 是' if row_1_exists else '❌ 否'}")
                        print(f"  - div.CompRow 元素数量: {comp_rows_count}")
                        print(f"  - div.Comp_Title 元素数量: {comp_titles_count}")
                        
                        if row_1_exists:
                            # 获取第一个阵容的详细信息用于调试
                            row_1_id = await row_1_exists.get_attribute("id")
                            row_1_class = await row_1_exists.get_attribute("class")
                            print(f"  - 第一个阵容行详情: id='{row_1_id}', class='{row_1_class}'")
                            
                            # 检查是否能找到阵容名称
                            title_elem = await row_1_exists.query_selector("div.CompRowName div.Comp_Title")
                            if title_elem:
                                title_text = await title_elem.inner_text()
                                print(f"  - 第一个阵容名称: '{title_text}'")
                            else:
                                print(f"  - ❌ 无法在第一个阵容行中找到标题元素")
                        
                    except Exception as debug_err:
                        pass  # 忽略调试检查错误
                    
                    try:
                        # 尝试多种选择器找到阵容列表容器
                        comp_container_selectors = [
                            "#CompListContainer > div",
                            "div[id='CompListContainer'] > div",
                            "div.CompListContainer > div",
                            "div[class*='CompRow']",
                            "div[id*='row_']"
                        ]
                        
                        comp_containers = None
                        used_selector = ""
                        
                        for selector in comp_container_selectors:
                            try:
                                print(f"尝试使用选择器: {selector}")
                                comp_containers = await page.query_selector_all(selector)
                                if comp_containers and len(comp_containers) > 0:
                                    used_selector = selector
                                    print(f"使用选择器 '{selector}' 找到 {len(comp_containers)} 个容器")
                                    break
                            except Exception as e:
                                print(f"选择器 '{selector}' 查找失败: {e}")
                                continue
                        
                        if not comp_containers or len(comp_containers) == 0:
                            # 如果所有选择器都失败
                            print("没有找到阵容容器")
                            
                            return 0
                        
                        print(f"找到 {len(comp_containers)} 个可能的阵容容器元素")
                        
                        # 遍历每个阵容容器
                        comp_count = 0
                        comp_ids_to_fetch = []  # 收集所有comp_id，后续单独请求API
                        
                        for idx, container in enumerate(comp_containers):
                            # 每10个容器等待一次，避免处理过快
                            if idx > 0 and idx % 10 == 0:
                                await self.add_random_delay()
                                # 模拟人类行为随机滚动
                                await simulate_human_behavior(page)
                            
                            try:
                                # 设置处理单个容器的超时
                                try:
                                    # 检查这个容器是否包含阵容行
                                    comp_row_selector = "div.CompRow"
                                    comp_row = await container.query_selector(comp_row_selector)
                                    
                                    if not comp_row:
                                        # 尝试其他可能的选择器
                                        alternative_selectors = [
                                            "div[class*='CompRow']", 
                                            "div[id*='row_']",
                                            "*[id*='row_']"
                                        ]
                                        
                                        for alt_selector in alternative_selectors:
                                            comp_row = await container.query_selector(alt_selector)
                                            if comp_row:
                                                print(f"使用备选选择器 '{alt_selector}' 找到阵容行")
                                                break
                                                
                                        if not comp_row:
                                            print(f"容器 {idx+1} 不是阵容行，跳过")
                                            continue
                                    
                                    # 获取阵容的ID - 添加详细调试
                                    print(f"正在处理容器 {idx+1}...")
                                    row_id = await comp_row.get_attribute("id")
                                    row_class = await comp_row.get_attribute("class")
                                    
                                    print(f"  容器 {idx+1} 详情:")
                                    print(f"    - ID: '{row_id}'")
                                    print(f"    - Class: '{row_class}'")
                                    
                                    if not row_id:
                                        print(f"  ❌ 容器 {idx+1} 没有ID属性，尝试备选标识方法")
                                        # 尝试使用其他属性作为唯一标识
                                        row_id = f"unnamed_row_{idx+1}"
                                        print(f"  使用备选ID: '{row_id}'")
                                    else:
                                        print(f"  ✅ 成功获取ID: '{row_id}'")
                                    
                                    # 从id中提取数字部分作为API请求的comp参数
                                    comp_id = None
                                    if row_id and row_id.startswith("row_"):
                                        comp_id = row_id.replace("row_", "")
                                        print(f"  ✅ 从ID '{row_id}' 提取comp_id: '{comp_id}'")
                                    else:
                                        print(f"  ❌ ID '{row_id}' 不是预期的 'row_X' 格式")
                                        # 尝试使用其他方法获取comp_id
                                        # 有时候网页上会有像data-comp-id这样的属性
                                        data_comp_id = await comp_row.get_attribute("data-comp-id")
                                        if data_comp_id:
                                            comp_id = data_comp_id
                                            print(f"  ✅ 从data-comp-id属性获取comp_id: '{comp_id}'")
                                        else:
                                            print(f"  ❌ 也无法从data-comp-id属性获取comp_id")
                                            
                                            # 尝试从其他可能的属性获取
                                            all_attributes = await comp_row.evaluate("el => Array.from(el.attributes).map(attr => attr.name + '=' + attr.value)")
                                            print(f"  所有属性: {all_attributes}")
                                            
                                            print(f"  跳过容器 {idx+1}，无法获取有效的comp_id")
                                            continue
                                    
                                    # 验证comp_id是否为数字
                                    if comp_id and not comp_id.isdigit():
                                        print(f"  ❌ comp_id '{comp_id}' 不是数字，跳过")
                                        continue
                                    
                                    print(f"  ✅ 最终确定comp_id: '{comp_id}'")
                                    
                                    # 获取阵容名称 - 添加详细调试
                                    comp_name = ""
                                    name_selectors = [
                                        "div.CompRowName > div.Comp_Title",
                                        "div[class*='CompRowName'] > div[class*='Comp_Title']",
                                        "div[class*='Comp_Title']",
                                        "div[class*='title']"
                                    ]
                                    
                                    print(f"  正在查找阵容名称...")
                                    for name_selector in name_selectors:
                                        print(f"    尝试选择器: '{name_selector}'")
                                        name_element = await comp_row.query_selector(name_selector)
                                        if name_element:
                                            comp_name = await name_element.inner_text()
                                            if comp_name:
                                                print(f"    ✅ 使用选择器 '{name_selector}' 找到阵容名称: '{comp_name}'")
                                                break
                                            else:
                                                print(f"    ❌ 元素存在但文本为空")
                                        else:
                                            print(f"    ❌ 选择器未找到元素")
                                    
                                    if not comp_name:
                                        print(f"  ❌ 无法找到阵容 {row_id} 的名称，使用默认值")
                                        comp_name = f"未命名阵容_{idx+1}"
                                        
                                        # 额外调试：查看这个阵容行的HTML结构
                                        try:
                                            row_html = await comp_row.inner_html()
                                            print(f"  阵容行HTML结构（前300字符）: {row_html[:300]}...")
                                        except Exception as html_err:
                                            print(f"  无法获取阵容行HTML: {html_err}")
                                    else:
                                        print(f"  ✅ 最终阵容名称: '{comp_name}'")
                                    
                                    # 获取阵容评分
                                    comp_tier = "未知"
                                    tier_selectors = [
                                        "div.CompRowTier > div",
                                        "div[class*='CompRowTier'] > div",
                                        "div[class*='tier']",
                                        "div[class*='Tier']"
                                    ]
                                    
                                    for tier_selector in tier_selectors:
                                        tier_element = await comp_row.query_selector(tier_selector)
                                        if tier_element:
                                            comp_tier = await tier_element.inner_text()
                                            if comp_tier:
                                                break
                                    
                                    # 获取阵容英雄
                                    heroes = []
                                    units_selectors = [
                                        "div.CompUnitStatsContainer > div.CompUnitTraitsContainer > div.UnitsContainer > div",
                                        "div[class*='CompUnitStats'] div[class*='Units'] > div",
                                        "div[class*='Unit'] div[class*='Name']"
                                    ]
                                    
                                    for units_selector in units_selectors:
                                        try:
                                            unit_elements = await comp_row.query_selector_all(units_selector)
                                            if unit_elements and len(unit_elements) > 0:
                                                for unit_elem in unit_elements:
                                                    unit_name_selectors = [
                                                        "div.UnitNames",
                                                        "div[class*='UnitName']",
                                                        "div[class*='Name']",
                                                        "span"
                                                    ]
                                                    
                                                    for unit_name_selector in unit_name_selectors:
                                                        unit_name_elem = await unit_elem.query_selector(unit_name_selector)
                                                        if unit_name_elem:
                                                            hero_name = await unit_name_elem.inner_text()
                                                            if hero_name and hero_name not in heroes:
                                                                heroes.append(hero_name)
                                                                break
                                                
                                                if heroes:
                                                    print(f"使用选择器 '{units_selector}' 找到 {len(heroes)} 个英雄")
                                                    break
                                        except Exception as e:
                                            print(f"使用选择器 '{units_selector}' 查找英雄失败: {e}")
                                    
                                    # 获取统计数据
                                    stats = {
                                        "avg_placement": "未知",
                                        "frequency": "未知",
                                        "win_rate": "未知",
                                        "top4_rate": "未知"
                                    }
                                    
                                    # 尝试获取平均排名
                                    try:
                                        avg_selectors = [
                                            f"#stat_1_{comp_id}",
                                            f"[id='stat_1_{comp_id}']",
                                            "div[class*='Comp_Stats'] div:nth-child(1) div span"
                                        ]
                                        
                                        for avg_selector in avg_selectors:
                                            avg_elem = await page.query_selector(avg_selector)
                                            if avg_elem:
                                                avg_placement = await avg_elem.inner_text()
                                                if avg_placement:
                                                    stats["avg_placement"] = avg_placement
                                                    break
                                    except Exception as e:
                                        print(f"获取平均排名失败: {e}")
                                    
                                    # 尝试获取出场率
                                    try:
                                        freq_selectors = [
                                            f"#stat_2_{comp_id}",
                                            f"[id='stat_2_{comp_id}']",
                                            "div[class*='Comp_Stats'] div:nth-child(2) div span"
                                        ]
                                        
                                        for freq_selector in freq_selectors:
                                            freq_elem = await page.query_selector(freq_selector)
                                            if freq_elem:
                                                frequency = await freq_elem.inner_text()
                                                if frequency:
                                                    stats["frequency"] = frequency
                                                    break
                                    except Exception as e:
                                        print(f"获取出场率失败: {e}")
                                    
                                    # 尝试获取登顶率
                                    try:
                                        win_selectors = [
                                            f"#stat_3_{comp_id}",
                                            f"[id='stat_3_{comp_id}']",
                                            "div[class*='Comp_Stats'] div[class*='optional1'] div span"
                                        ]
                                        
                                        for win_selector in win_selectors:
                                            win_elem = await page.query_selector(win_selector)
                                            if win_elem:
                                                win_rate = await win_elem.inner_text()
                                                if win_rate:
                                                    stats["win_rate"] = win_rate
                                                    break
                                    except Exception as e:
                                        print(f"获取登顶率失败: {e}")
                                    
                                    # 尝试获取前四率
                                    try:
                                        top4_selectors = [
                                            f"#stat_4_{comp_id}",
                                            f"[id='stat_4_{comp_id}']",
                                            "div[class*='Comp_Stats'] div[class*='optional2'] div span"
                                        ]
                                        
                                        for top4_selector in top4_selectors:
                                            top4_elem = await page.query_selector(top4_selector)
                                            if top4_elem:
                                                top4_rate = await top4_elem.inner_text()
                                                if top4_rate:
                                                    stats["top4_rate"] = top4_rate
                                                    break
                                    except Exception as e:
                                        print(f"获取前四率失败: {e}")
                                    
                                    # 创建阵容数据对象
                                    comp_data = {
                                        "id": row_id,
                                        "comp_id": comp_id,  # 添加API所需的comp_id
                                        "name": comp_name,
                                        "tier": comp_tier,
                                        "heroes": heroes,
                                        "stats": stats
                                    }
                                    
                                    # 添加到阵容数据列表
                                    self.comp_data.append(comp_data)
                                    
                                    # 同时更新映射
                                    self.comp_mapping[comp_name] = {
                                        "id": row_id,
                                        "comp_id": comp_id,  # 添加API所需的comp_id
                                        "name": comp_name,
                                        "tier": comp_tier
                                    }
                                    
                                    # 将comp_id和name添加到待获取详情的列表
                                    if comp_id and comp_id.isdigit():
                                        comp_ids_to_fetch.append((comp_id, comp_name))
                                    
                                    comp_count += 1
                                    print(f"已处理阵容 [{comp_count}]: {comp_name}")
                                
                                except Exception as e:
                                    print(f"处理阵容容器 {idx+1} 时出错: {e}")
                                    print(traceback.format_exc())
                            
                            except Exception as e:
                                print(f"处理阵容容器 {idx+1} 时发生未处理异常: {e}")
                                print(traceback.format_exc())
                                # 继续处理下一个容器，不中断整个过程
                        
                        print(f"共找到并处理了 {comp_count} 个阵容基本信息")
                        
                        # 移除调试信息保存
                        
                        # 保存基础阵容数据
                        if comp_count > 0:
                            await self.save_data()
                        else:
                            print("没有找到任何有效阵容数据，跳过保存")
                        
                        # 关闭浏览器，释放资源
                        await browser.close()
                        print("浏览器已关闭")
                        
                        # 分批获取阵容详细数据，避免同时发送太多请求
                        print(f"开始获取 {len(comp_ids_to_fetch)} 个阵容的详细数据...")
                        batch_size = 5
                        for i in range(0, len(comp_ids_to_fetch), batch_size):
                            batch = comp_ids_to_fetch[i:i+batch_size]
                            print(f"正在处理第 {i//batch_size + 1} 批，共 {len(batch)} 个阵容")
                            
                            # 并发获取详细数据，但限制并发数
                            tasks = []
                            for comp_id, comp_name in batch:
                                tasks.append(self.fetch_comp_details(comp_id, comp_name))
                            
                            # 等待当前批次完成
                            results = await asyncio.gather(*tasks)
                            
                            # 添加延迟，避免请求过快
                            await asyncio.sleep(2)
                    
                    except Exception as e:
                        print(f"查找阵容容器过程中出错: {e}")
                        print(traceback.format_exc())
                    
                except Exception as e:
                    print(f"爬取阵容数据时发生错误: {e}")
                    print(traceback.format_exc())
                    # 保存页面以便调试
                    try:
                        content = await page.content()
                        with open("error_page.html", "w", encoding="utf-8") as f:
                            f.write(content)
                        print("已保存错误页面到 error_page.html")
                        
                        # 尝试截图
                        await page.screenshot(path="error_screenshot.png", full_page=True)
                        print("已保存错误页面截图到 error_screenshot.png")
                    except Exception as screenshot_error:
                        print(f"保存调试信息失败: {screenshot_error}")
                
                finally:
                    # 确保浏览器关闭
                    try:
                        if browser and not browser.is_connected():
                            await browser.close()
                            print("浏览器已关闭")
                    except:
                        pass
            
            except Exception as e:
                print(f"启动浏览器过程中出错: {e}")
                print(traceback.format_exc())
                try:
                    if browser and browser.is_connected():
                        await browser.close()
                except:
                    pass
        
        return len(self.comp_data)
    
    async def save_data(self):
        """保存阵容数据和映射数据"""
        try:
            # 保存阵容基础数据
            data_file = os.path.join(self.comp_data_dir, "阵容基础数据.json")
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.comp_data, f, ensure_ascii=False, indent=2)
            print(f"已保存阵容基础数据到: {data_file}")
            
            # 保存阵容映射数据
            mapping_file = os.path.join(self.mapping_dir, "阵容数据映射.json")
            with open(mapping_file, 'w', encoding='utf-8') as f:
                json.dump(self.comp_mapping, f, ensure_ascii=False, indent=2)
            print(f"已保存阵容映射数据到: {mapping_file}")
            
            return True
        except Exception as e:
            print(f"保存数据时出错: {e}")
            print(traceback.format_exc())
            return False

    def _load_or_fetch_cluster_id(self):
        """加载或获取最新的cluster_id - 简化版本"""
        config_file = os.path.join(self.config_dir, "metatft_config.json")
        
        # 尝试加载本地配置文件
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    stored_id = config.get('cluster_id')
                    update_time = config.get('update_time', 0)
                    
                    # 如果配置文件存在且更新时间不超过7天，直接使用
                    if stored_id and (time.time() - update_time < 604800):  # 7天 = 604800秒
                        self.cluster_id = stored_id
                        print(f"使用缓存的cluster_id: {self.cluster_id}")
                        print(f"上次更新时间：{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(update_time))}")
                        return
                    else:
                        print(f"缓存的cluster_id已过期（超过7天），使用默认值")
        except Exception as e:
            print(f"读取配置文件失败: {e}")
        
        # 使用代码中的默认值
        print(f"使用默认cluster_id: {self.cluster_id}")
        
        # 保存当前使用的cluster_id到配置文件
        self._save_cluster_id(self.cluster_id)
        
        # 提示用户如何手动更新
        print("\n" + "="*60)
        print("📌 cluster_id 更新提示:")
        print("如果API请求失败，可能需要更新cluster_id。")
        print("获取最新cluster_id的方法：")
        print("1. 打开浏览器访问: https://www.metatft.com/comps")
        print("2. 按F12打开开发者工具，切换到Network标签")
        print("3. 刷新页面，查找包含'comp_details'的请求")
        print("4. 在请求URL中找到cluster_id参数的值")
        print("5. 修改代码中第40行的cluster_id值")
        print("="*60 + "\n")
    
    def test_cluster_id_validity(self):
        """测试当前cluster_id是否有效"""
        print(f"正在测试cluster_id={self.cluster_id}的有效性...")
        
        # 使用一个简单的测试请求
        test_url = f"{self.api_base_url}?comp=1&cluster_id={self.cluster_id}"
        
        try:
            import aiohttp
            import asyncio
            
            async def test_request():
                timeout = aiohttp.ClientTimeout(total=10)
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                        'Accept': 'application/json',
                        'Referer': 'https://www.metatft.com/'
                    }
                    
                    async with session.get(test_url, headers=headers) as response:
                        return response.status, await response.text()
            
            # 运行测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            status, response_text = loop.run_until_complete(test_request())
            loop.close()
            
            if status == 200:
                print(f"✅ cluster_id={self.cluster_id} 有效！")
                return True
            else:
                print(f"❌ cluster_id={self.cluster_id} 无效，状态码: {status}")
                if status == 500:
                    print("💡 建议：cluster_id可能已过期，需要手动更新")
                return False
                
        except Exception as e:
            print(f"❌ 测试cluster_id时出错: {e}")
            return False
    
    async def _fetch_latest_cluster_id(self):
        """移除复杂的自动获取逻辑，改为提示用户手动获取"""
        print("⚠️  自动获取cluster_id功能已禁用")
        print("💡 建议手动获取最新的cluster_id以确保稳定性")
        return

    def _save_cluster_id(self, cluster_id):
        """保存cluster_id到配置文件"""
        try:
            config_file = os.path.join(self.config_dir, "metatft_config.json")
            
            # 准备配置数据
            config = {
                'cluster_id': cluster_id,
                'update_time': time.time()
            }
            
            # 保存到文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"已保存cluster_id: {cluster_id}到配置文件")
        except Exception as e:
            print(f"保存cluster_id失败: {e}")
            print(traceback.format_exc())

async def run_comp_scraper(base_dir=None, max_concurrent=3, min_delay=1.5, max_delay=3.0, clean_old_data=True):
    """运行阵容爬虫的入口函数"""
    max_retries = 3
    
    for retry in range(max_retries):
        try:
            print(f"爬取尝试 {retry+1}/{max_retries}")
            scraper = CompScraper(
                base_dir=base_dir,
                max_concurrent=max_concurrent,
                min_delay=min_delay,
                max_delay=max_delay,
                clean_old_data=clean_old_data
            )
            
            count = await scraper.scrape_comps()
            print(f"爬取完成，共获取 {count} 个阵容数据")
            
            if count > 0:
                return True
            else:
                print(f"未获取到阵容数据，将在5秒后重试...")
                await asyncio.sleep(5)
        except Exception as e:
            print(f"运行阵容爬虫时出错: {e}")
            print(traceback.format_exc())
            if retry < max_retries - 1:
                print(f"将在5秒后重试...")
                await asyncio.sleep(5)
    
    print(f"已尝试 {max_retries} 次，爬取失败")
    return False

# 导出接口，方便作为子模块调用
async def fetch_comp_data(base_dir=None, clean_old_data=True, max_retries=3):
    """
    获取所有阵容数据的主函数
    
    Args:
        base_dir: 基础目录，默认为当前脚本所在目录
        clean_old_data: 是否清理旧数据，默认为True
        max_retries: 最大重试次数，默认为3
        
    Returns:
        bool: 是否成功获取数据
    """
    return await run_comp_scraper(
        base_dir=base_dir, 
        max_concurrent=3, 
        min_delay=1.5, 
        max_delay=3.0, 
        clean_old_data=clean_old_data
    )

def get_comp_data_mapping(base_dir=None):
    """
    获取已保存的阵容映射数据
    
    Args:
        base_dir: 基础目录，默认为当前脚本所在目录
        
    Returns:
        dict: 阵容映射数据
    """
    if base_dir is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
    
    mapping_file = os.path.join(base_dir, "映射文件", "阵容数据映射.json")
    
    try:
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"获取阵容映射数据失败: {e}")
    
    return {}

def get_comp_detail(comp_name, base_dir=None):
    """
    获取指定阵容的详细数据
    
    Args:
        comp_name: 阵容名称
        base_dir: 基础目录，默认为当前脚本所在目录
        
    Returns:
        dict: 阵容详细数据，如果不存在返回空字典
    """
    if base_dir is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 安全处理文件名
    for char in ['\\', '/', ':', '*', '?', '"', '<', '>', '|']:
        comp_name = comp_name.replace(char, '_')
    
    comp_file = os.path.join(base_dir, "阵容数据", f"{comp_name}.json")
    
    try:
        if os.path.exists(comp_file):
            with open(comp_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"获取阵容 '{comp_name}' 详细数据失败: {e}")
    
    return {}

async def test_comp_api(comp_id=2, cluster_id=328):
    """测试API请求函数，用于调试
    
    Args:
        comp_id: 阵容ID
        cluster_id: 集群ID
        
    Returns:
        dict: API返回的原始数据
    """
    url = f"https://api-hc.metatft.com/tft-comps-api/comp_details?comp={comp_id}&cluster_id={cluster_id}"
    print(f"测试API请求: {url}")
    
    try:
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://www.metatft.com/',
                'Origin': 'https://www.metatft.com'
            }
            
            async with session.get(url, headers=headers) as response:
                print(f"API响应状态码: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print("API请求成功")
                    return data
                else:
                    print(f"API请求失败，状态码: {response.status}")
                    response_text = await response.text()
                    print(f"响应内容: {response_text[:200]}")
                    return None
    except Exception as e:
        print(f"API请求异常: {e}")
        print(traceback.format_exc())
        return None

# 添加同步版本的入口函数，方便外部导入使用
def get_comp_data(base_dir=None, clean_old_data=False):
    """获取阵容数据的同步函数，返回阵容数据
    
    Args:
        base_dir: 基础目录
        clean_old_data: 是否清理旧数据
        
    Returns:
        tuple: (是否成功, 阵容基础数据, 阵容映射数据)
    """
    try:
        # 确定基础目录
        current_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        data_file = os.path.join(current_dir, "阵容数据", "阵容基础数据.json")
        mapping_file = os.path.join(current_dir, "映射文件", "阵容数据映射.json")
        
        # 如果文件已经存在，且不需要清理旧数据，直接加载
        if os.path.exists(data_file) and os.path.exists(mapping_file) and not clean_old_data:
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    comp_data = json.load(f)
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    comp_mapping = json.load(f)
                print(f"从本地文件加载了 {len(comp_data)} 条阵容数据")
                return True, comp_data, comp_mapping
            except Exception as e:
                print(f"读取本地阵容数据失败，将重新获取: {e}")
        
        # 使用asyncio运行异步函数
        success = asyncio.run(fetch_comp_data(
            base_dir=base_dir,
            clean_old_data=clean_old_data
        ))
        
        # 如果成功获取，读取新生成的文件
        if success and os.path.exists(data_file) and os.path.exists(mapping_file):
            with open(data_file, 'r', encoding='utf-8') as f:
                comp_data = json.load(f)
            with open(mapping_file, 'r', encoding='utf-8') as f:
                comp_mapping = json.load(f)
            return True, comp_data, comp_mapping
        else:
            return False, [], {}
    
    except Exception as e:
        print(f"获取阵容数据时出错: {e}")
        return False, [], {}

# 如果直接运行此脚本
if __name__ == "__main__":
    import sys
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            # 测试cluster_id有效性
            print("🔧 测试cluster_id有效性...")
            scraper = CompScraper(clean_old_data=False)
            is_valid = scraper.test_cluster_id_validity()
            
            if not is_valid:
                print("\n💡 如果cluster_id无效，请按照以下步骤手动更新：")
                print("1. 访问 https://www.metatft.com/comps")
                print("2. 打开浏览器开发者工具 (F12)")
                print("3. 切换到 Network 标签")
                print("4. 刷新页面")
                print("5. 查找包含 'comp_details' 的请求")
                print("6. 在URL中找到 cluster_id 参数")
                print("7. 修改代码第40行的cluster_id值")
            
            sys.exit(0)
        
        elif sys.argv[1] == "update":
            # 手动更新cluster_id
            if len(sys.argv) > 2:
                new_cluster_id = sys.argv[2]
                print(f"🔄 更新cluster_id为: {new_cluster_id}")
                
                scraper = CompScraper(clean_old_data=False)
                scraper.cluster_id = new_cluster_id
                scraper._save_cluster_id(new_cluster_id)
                
                # 测试新的cluster_id
                print("🔧 测试新的cluster_id...")
                is_valid = scraper.test_cluster_id_validity()
                
                if is_valid:
                    print("✅ 新的cluster_id设置成功！")
                else:
                    print("❌ 新的cluster_id似乎无效，请检查")
                
                sys.exit(0)
            else:
                print("❌ 使用方法: python 阵容.py update <新的cluster_id>")
                print("例如: python 阵容.py update 341")
                sys.exit(1)
        
        elif sys.argv[1] == "help":
            print("🔧 阵容爬虫工具使用说明：")
            print("")
            print("基本用法:")
            print("  python 阵容.py              # 正常运行爬虫")
            print("")
            print("测试功能:")
            print("  python 阵容.py test         # 测试当前cluster_id是否有效")
            print("  python 阵容.py update <ID>  # 更新cluster_id并测试")
            print("  python 阵容.py help         # 显示此帮助信息")
            print("")
            print("示例:")
            print("  python 阵容.py test")
            print("  python 阵容.py update 341")
            sys.exit(0)
    
    # 正常运行爬虫
    asyncio.run(run_comp_scraper())