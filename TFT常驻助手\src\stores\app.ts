import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { PAGE_CONSTANTS, type NavigationHistoryItem, type PageType } from '@/types';

/**
 * 主应用状态管理
 * 复刻主窗口.py中的MainWindow类的状态管理功能
 */
export const useAppStore = defineStore('app', () => {
  // 状态定义
  const currentPage = ref<number>(PAGE_CONSTANTS.PAGE_COMP_LIST);
  const isMinimized = ref<boolean>(false);
  const historyStack = ref<NavigationHistoryItem[]>([]);
  const viewsLoaded = ref<Record<number, boolean>>({});

  // 计算属性
  const hasHistory = computed(() => historyStack.value.length > 0);
  const isDetailPage = computed(() => {
    return currentPage.value >= PAGE_CONSTANTS.PAGE_COMP_DETAIL;
  });
  const isMainPage = computed(() => {
    return currentPage.value < PAGE_CONSTANTS.PAGE_COMP_DETAIL;
  });

  /**
   * 切换主页面
   * 复刻MainWindow.switch_main_page方法
   * @param pageIndex 页面索引
   */
  const switchMainPage = (pageIndex: number) => {
    console.log(`切换到主页面: ${pageIndex}`);
    
    // 清空历史记录栈
    historyStack.value = [];
    
    // 设置当前页面
    currentPage.value = pageIndex;
    
    // 标记视图已加载
    if (!viewsLoaded.value[pageIndex]) {
      viewsLoaded.value[pageIndex] = true;
    }
  };

  /**
   * 导航到详情页面
   * 复刻MainWindow.navigate_to_detail方法
   * @param detailPageIndex 详情页面索引
   * @param dataKey 数据键值
   */
  const navigateToDetail = (detailPageIndex: number, dataKey: string) => {
    console.log(`导航到详情页面: ${detailPageIndex}, 数据键: ${dataKey}`);
    
    // 记录当前页面到历史栈
    recordNavigation(currentPage.value, '');
    
    // 切换到详情页面
    currentPage.value = detailPageIndex;
    
    // 标记视图已加载
    if (!viewsLoaded.value[detailPageIndex]) {
      viewsLoaded.value[detailPageIndex] = true;
    }
  };

  /**
   * 记录导航历史
   * 复刻MainWindow.record_navigation方法
   * @param pageIndex 页面索引
   * @param dataKey 数据键值
   */
  const recordNavigation = (pageIndex: number, dataKey: string) => {
    const historyItem: NavigationHistoryItem = {
      pageIndex,
      dataKey
    };
    
    historyStack.value.push(historyItem);
    console.log(`记录导航历史: 页面${pageIndex}, 历史栈长度: ${historyStack.value.length}`);
  };

  /**
   * 返回上一页
   * 复刻MainWindow.go_back方法
   */
  const goBack = () => {
    if (historyStack.value.length === 0) {
      console.warn('历史栈为空，无法返回');
      return;
    }
    
    const lastHistory = historyStack.value.pop();
    if (lastHistory) {
      console.log(`返回到页面: ${lastHistory.pageIndex}`);
      currentPage.value = lastHistory.pageIndex;
    }
  };

  /**
   * 切换窗口最小化状态
   * 复刻MainWindow.toggle_window方法
   */
  const toggleWindow = () => {
    isMinimized.value = !isMinimized.value;
    console.log(`窗口状态切换: ${isMinimized.value ? '最小化' : '展开'}`);
  };

  /**
   * 设置窗口最小化状态
   * @param minimized 是否最小化
   */
  const setMinimized = (minimized: boolean) => {
    isMinimized.value = minimized;
    console.log(`设置窗口状态: ${minimized ? '最小化' : '展开'}`);
  };

  /**
   * 检查视图是否已加载
   * @param pageIndex 页面索引
   * @returns 是否已加载
   */
  const isViewLoaded = (pageIndex: number): boolean => {
    return viewsLoaded.value[pageIndex] || false;
  };

  /**
   * 标记视图已加载
   * @param pageIndex 页面索引
   */
  const markViewLoaded = (pageIndex: number) => {
    viewsLoaded.value[pageIndex] = true;
  };

  /**
   * 清空历史记录
   */
  const clearHistory = () => {
    historyStack.value = [];
    console.log('清空历史记录');
  };

  /**
   * 重置应用状态
   */
  const resetState = () => {
    currentPage.value = PAGE_CONSTANTS.PAGE_COMP_LIST;
    isMinimized.value = false;
    historyStack.value = [];
    viewsLoaded.value = {};
    console.log('重置应用状态');
  };

  return {
    // 状态
    currentPage,
    isMinimized,
    historyStack,
    viewsLoaded,
    
    // 计算属性
    hasHistory,
    isDetailPage,
    isMainPage,
    
    // 方法
    switchMainPage,
    navigateToDetail,
    recordNavigation,
    goBack,
    toggleWindow,
    setMinimized,
    isViewLoaded,
    markViewLoaded,
    clearHistory,
    resetState
  };
});