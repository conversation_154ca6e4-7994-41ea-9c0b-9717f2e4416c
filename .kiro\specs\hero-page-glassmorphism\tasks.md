# TFT助手完整应用Glassmorphism重构实施计划

## 任务概述

将TFT常驻助手从传统桌面UI**完全重构**为现代化Glassmorphism风格应用。这是一个**完全重写**项目，删除所有现有UI组件，在App.vue中构建完整的单文件应用架构。采用由外向内、逐层构建的渐进式开发方式，确保每一步都有可见的进展且功能正常。

## 实施任务

- [x] 1. 完全重写应用架构并建立基础容器（架构安全重构）
  - **架构保护措施**：
    - ✅ 备份现有组件代码到.backup文件
    - ✅ 保留所有Tauri API导入（`@tauri-apps/api`相关导入）
    - ✅ 保留所有数据库命令调用接口（`invoke('get_hero_list')`等）
    - ✅ 保留窗口管理相关的API调用（`invoke('toggle_window_size')`等）
    - ✅ 保留数据获取和状态管理逻辑
  - **完全重写步骤**：
    - ✅ 删除所有现有UI组件（MainWindow.vue、DemoContent.vue、ControlBar.vue等）
    - ✅ 完全重写App.vue，包含完整的应用架构
    - ✅ 创建云顶之弈风格紫蓝色渐变背景
    - ✅ 建立完整应用框架：控制栏 + 导航栏 + 内容区域
    - ✅ 应用完全填充窗口（870x1050），无边距无圆角
  - **验证检查**：
    - ✅ 应用完全填充窗口，云顶之弈风格渐变背景正常显示
    - ✅ Tauri命令调用测试正常
    - ✅ 数据库连接正常，英雄数据加载成功
  - _需求: 1_

- [x] 2. 构建完整应用窗口框架
  - ✅ 应用完全填充窗口（870x1050）
  - ✅ 实现Glassmorphism效果（半透明、模糊背景）
  - ✅ 建立完整的层级结构：背景 → 主窗口 → 控制栏 → 导航栏 → 内容区域
  - ✅ 验证窗口框架的视觉效果符合设计要求
  - _需求: 2_

- [x] 3. 实现顶部控制栏结构
  - ✅ 在主窗口顶部添加控制栏区域（48px高度）
  - ✅ 实现汉堡包收放按钮（左侧）
  - ✅ 实现拖拽区域（中央，显示应用标题）
  - ✅ 实现关闭按钮（右侧）
  - ✅ 应用Glassmorphism样式，与主窗口风格保持一致
  - _需求: 3_

- [x] 4. 实现展开收起功能
  - ✅ 实现汉堡包图标的点击收放逻辑
  - ✅ 添加窗口状态管理（展开/收起）
  - ✅ 实现窗口尺寸的平滑过渡效果
  - ✅ 收起状态：400x104（控制栏+导航栏）
  - ✅ 展开状态：870x1050（保持初始尺寸）
  - ✅ 汉堡包图标动画效果（三横线 ↔ X形状）
  - _需求: 4_

- [x] 5. 添加关闭按钮功能
  - ✅ 实现关闭按钮的点击事件处理
  - ✅ 添加悬停效果（红色高亮）
  - ✅ 集成Tauri的窗口关闭API
  - ✅ 验证关闭按钮功能正常且有适当的视觉反馈
  - _需求: 5_

- [x] 6. 实现窗口拖拽移动功能
  - ✅ 在控制栏中央区域添加拖拽功能
  - ✅ 实现窗口位置的实时跟随鼠标移动
  - ✅ 分离拖拽和收放功能，避免冲突
  - ✅ 验证拖拽功能流畅且窗口能正确跟随鼠标
  - _需求: 6_

- [x] 7. 构建导航栏区域框架
  - ✅ 在控制栏下方添加导航栏容器（56px高度）
  - ✅ 创建四个导航按钮：阵容、英雄、装备、海克斯
  - ✅ 应用Glassmorphism样式，与整体风格一致
  - ✅ 收起状态下保持导航栏可见，只隐藏文字
  - _需求: 7_

- [x] 8. 添加完整导航标识
  - ✅ 在导航栏中添加四个标签：阵容🏆、英雄⚔️、装备🛡️、海克斯🔮
  - ✅ 实现激活状态的视觉效果（高亮显示+底部渐变线）
  - ✅ 应用Glassmorphism按钮样式
  - ✅ 验证导航标识显示正确且激活状态明显
  - _需求: 8_

- [x] 9. 创建内容区域容器
  - ✅ 在导航栏下方添加内容区域容器
  - ✅ 设置适当的内边距和滚动能力
  - ✅ 应用Glassmorphism卡片样式
  - ✅ 验证内容区域占据剩余空间且样式正确
  - _需求: 9_

- [x] 10. 实现搜索框区域
  - ✅ 在英雄页面顶部添加搜索框
  - ✅ 应用Glassmorphism输入框样式
  - ✅ 添加"搜索英雄名称..."占位符文本
  - ✅ 实现实时搜索功能
  - _需求: 10_

- [x] 11. 构建费用筛选区域
  - ✅ 在搜索框下方添加费用筛选按钮组
  - ✅ 包含"全部"、"1费"到"5费"按钮
  - ✅ 应用Glassmorphism按钮样式和选中状态效果
  - ✅ 实现多选筛选逻辑
  - _需求: 11_

- [x] 12. 建立英雄列表容器框架
  - ✅ 在筛选区域下方添加英雄列表容器
  - ✅ 设置滚动能力和适当的内边距
  - ✅ 添加加载状态指示
  - ✅ 验证列表容器能够正确显示且支持滚动
  - _需求: 12_

- [x] 13. 实现费用分组容器结构
  - ✅ 在英雄列表容器中添加费用分组容器
  - ✅ 采用水平布局（左侧标签+右侧网格）
  - ✅ 为1-5费各创建一个分组容器
  - ✅ 验证分组容器布局正确且样式统一
  - _需求: 13, 14_

- [x] 14. 创建费用标签组件
  - ✅ 实现垂直费用标签，显示费用数字
  - ✅ 应用对应费用的颜色（1费灰色、2费绿色、3费蓝色、4费紫色、5费金色）
  - ✅ 设置固定宽度（48px）和自适应高度
  - ✅ 验证费用标签显示正确且颜色符合设计
  - _需求: 15_

- [x] 15. 构建英雄网格布局
  - ✅ 在费用分组右侧实现网格布局
  - ✅ 设置自适应网格结构（最小100px宽度）
  - ✅ 添加适当的网格间距（0.75rem）
  - ✅ 验证网格布局整齐且间距合适
  - _需求: 16_

- [x] 16. 创建英雄卡片基础结构
  - ✅ 实现英雄卡片的基础容器
  - ✅ 应用Glassmorphism卡片样式
  - ✅ 设置固定内边距和自适应尺寸
  - ✅ 验证卡片容器样式和尺寸正确
  - _需求: 17_

- [x] 17. 实现英雄头像区域
  - ✅ 在英雄卡片顶部添加头像区域
  - ✅ 设置48x48像素的圆角占位符容器
  - ✅ 添加首字母占位符处理
  - ✅ 验证头像区域显示正确且居中对齐
  - _需求: 18_

- [x] 18. 添加英雄名称显示
  - ✅ 在头像下方添加英雄名称文本
  - ✅ 使用白色文字且居中对齐
  - ✅ 支持长名称的自动换行
  - ✅ 验证名称显示正确且样式符合设计
  - _需求: 19_

- [x] 19. 实现统计信息显示
  - ✅ 在名称下方添加"出场率: X.XX%"和"均名: X.XX"
  - ✅ 使用较小的灰色文字
  - ✅ 设置适当的行间距
  - ✅ 验证统计信息格式和样式正确
  - _需求: 20_

- [x] 20. 添加费用标识徽章
  - ✅ 在英雄头像右上角添加费用徽章
  - ✅ 使用对应费用的背景颜色
  - ✅ 设置圆形样式和适当的尺寸（20px）
  - ✅ 验证徽章位置和颜色正确
  - _需求: 21_

- [x] 21. 实现卡片交互效果
  - ✅ 添加鼠标悬停效果（轻微放大和阴影变化）
  - ✅ 实现点击反馈效果
  - ✅ 设置鼠标指针为手型
  - ✅ 验证交互效果流畅且反馈明显
  - _需求: 22_

- [x] 22. 集成真实英雄数据加载
  - ✅ 在App.vue中集成数据获取逻辑
  - ✅ 实现Tauri命令调用以加载数据库中的英雄数据
  - ✅ 添加加载状态指示和错误处理
  - ✅ 验证真实数据能够正确加载和显示
  - _需求: 23_

- [x] 23. 实现搜索功能
  - ✅ 在搜索框中添加输入事件处理
  - ✅ 实现实时搜索筛选逻辑
  - ✅ 支持中文名称搜索
  - ✅ 验证搜索功能能够正确筛选英雄
  - _需求: 24_

- [x] 24. 实现费用筛选功能
  - ✅ 在费用筛选按钮中添加点击事件处理
  - ✅ 实现费用筛选逻辑和状态管理
  - ✅ 支持多选和全选功能
  - ✅ 验证费用筛选能够正确过滤英雄列表
  - _需求: 24_

- [x] 25. 提取英雄页面到独立组件



  - 将App.vue中的英雄页面逻辑提取到HeroListView.vue组件
  - 保持App.vue只负责应用框架（控制栏、导航栏、容器）
  - 通过组件引用在App.vue中显示英雄页面
  - 为其他页面（阵容、装备、海克斯）创建占位符组件
  - 验证组件化架构正常工作且性能良好
  - _需求: 架构优化_

- [ ] 26. 创建其他页面组件
  - 创建CompListView.vue（阵容页面）
  - 创建ItemListView.vue（装备页面）  
  - 创建HexListView.vue（海克斯页面）
  - 实现基础的页面结构和占位符内容
  - 验证页面切换功能正常
  - _需求: 完整应用功能_

## 验收标准

每个任务完成后需要验证：

1. **功能正确性**：实现的功能按预期工作
2. **视觉效果**：Glassmorphism效果符合设计要求
3. **交互流畅性**：用户交互响应及时且流畅
4. **代码质量**：代码结构清晰，遵循最佳实践
5. **性能表现**：页面渲染和交互性能良好

## 技术要求

- 使用Vue 3 Composition API
- 基于Shadcn/Vue组件库
- 应用Tailwind CSS样式系统
- 集成Tauri桌面应用API
- 遵循TypeScript类型安全
- 实现响应式设计原则