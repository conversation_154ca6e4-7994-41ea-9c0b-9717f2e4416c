# 数据库路径和图标显示修复需求文档

## 介绍

修复TFT常驻助手中的数据库路径问题和英雄图标显示问题，确保应用能正确读取根目录的数据库文件并显示英雄图标。

## 需求

### 需求 1: 数据库路径修复

**用户故事:** 作为开发者，我希望应用能自动找到正确的数据库文件路径，这样用户就不需要手动复制数据库文件。

#### 验收标准

1. WHEN 应用启动时 THEN 系统应该按以下优先级查找数据库文件：
   - 根目录下的 `tft_data.db`
   - 当前工作目录下的 `tft_data.db`
   - 可执行文件目录下的 `tft_data.db`

2. WHEN 找到数据库文件时 THEN 系统应该在控制台输出找到的数据库路径

3. WHEN 数据库文件不存在时 THEN 系统应该显示清晰的错误信息，指导用户运行数据库建立脚本

### 需求 2: 英雄图标显示修复

**用户故事:** 作为用户，我希望看到英雄的真实图标而不是占位符，这样我能更直观地识别英雄。

#### 验收标准

1. WHEN 英雄列表加载时 THEN 每个英雄应该显示其对应的图标

2. WHEN 英雄图标文件存在时 THEN 应用应该正确加载并显示图标

3. WHEN 英雄图标文件不存在时 THEN 应用应该显示英雄名称的首字符作为占位符

4. WHEN 图标加载失败时 THEN 应用应该优雅降级到文字占位符

### 需求 3: 图标路径解析

**用户故事:** 作为开发者，我希望图标路径能正确解析，参考Python版本的实现逻辑。

#### 验收标准

1. WHEN 从数据库读取图标路径时 THEN 系统应该将相对路径转换为绝对路径

2. WHEN 图标路径为相对路径时 THEN 系统应该基于项目根目录解析完整路径

3. WHEN 图标文件格式不支持时 THEN 系统应该尝试常见的图片格式（png, jpg, webp等）

### 需求 4: 错误处理和调试

**用户故事:** 作为开发者，我希望有详细的调试信息来诊断数据库和图标问题。

#### 验收标准

1. WHEN 数据库连接失败时 THEN 系统应该输出详细的错误信息和建议解决方案

2. WHEN 图标加载失败时 THEN 系统应该在控制台输出具体的错误原因

3. WHEN 数据加载成功时 THEN 系统应该输出加载的数据统计信息

4. WHEN 用户点击"重新加载"时 THEN 系统应该重新尝试数据库连接和数据加载