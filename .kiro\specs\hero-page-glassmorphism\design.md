# TFT助手完整应用Glassmorphism重构设计文档

## 概述

本文档详细描述了TFT常驻助手**完整应用**的Glassmorphism风格重构设计。这是一个**完全重写**项目，采用单文件架构（App.vue包含所有UI逻辑），结合Vue 3 + Tauri技术栈，实现现代化的玻璃拟态桌面应用界面。

## 架构

### 技术栈架构

```
前端层
├── Vue 3 (Composition API)
├── TypeScript
├── Tailwind CSS
├── Shadcn/Vue (组件库)
└── Lucide Vue Next (图标)

桌面应用层
├── Tauri 2.0
├── Rust (后端逻辑)
└── SQLite (数据存储)

状态管理
├── Pinia (全局状态)
├── Composables (逻辑复用)
└── Reactive API (响应式数据)
```

### 分层组件架构

```
App.vue (应用框架)
├── 渐变背景层 (glassmorphism-background)
├── 主应用窗口 (main-app-window)
│   ├── 控制栏 (control-bar)
│   │   ├── 收放按钮 (toggle-button) - 汉堡包图标
│   │   ├── 拖拽区域 (drag-area) - 应用标题
│   │   └── 关闭按钮 (close-button)
│   ├── 导航栏 (navigation-bar)
│   │   └── 导航按钮 (nav-button) - 阵容/英雄/装备/海克斯
│   └── 内容区域 (content-area)
│       ├── <CompListView /> - 阵容页面组件
│       ├── <HeroListView /> - 英雄页面组件
│       ├── <ItemListView /> - 装备页面组件
│       └── <HexListView /> - 海克斯页面组件

页面组件 (独立的.vue文件)
├── CompListView.vue - 阵容列表页面
├── HeroListView.vue - 英雄列表页面
│   ├── 搜索区域 (search-section)
│   ├── 筛选区域 (filter-section)
│   └── 英雄列表 (hero-list-area)
│       └── 费用分组 → 英雄网格 → 英雄卡片
├── ItemListView.vue - 装备列表页面
└── HexListView.vue - 海克斯列表页面

详情组件 (点击后显示)
├── CompDetailView.vue - 阵容详情页面
├── HeroDetailView.vue - 英雄详情页面
├── ItemDetailView.vue - 装备详情页面
└── HexDetailView.vue - 海克斯详情页面
```

**关键设计决策：**
- **框架与内容分离**：App.vue只负责应用框架，具体页面内容由独立组件负责
- **组件化架构**：每个页面都是独立的Vue组件，便于维护和扩展
- **完全填充**：应用完全填充窗口，无边距无圆角
- **分离交互**：收放、拖拽、关闭功能完全分离，避免冲突

## 组件和接口

### 1. 核心组件设计

#### 1.1 WindowFrame 组件

**职责**：提供主窗口框架和Glassmorphism基础样式

```vue
<template>
  <div class="hero-page-container">
    <div class="main-window" :class="{ 'minimized': isMinimized }">
      <ControlBar @toggle="toggleMinimize" @close="closeWindow" />
      <NavigationBar v-show="!isMinimized" />
      <ContentArea v-show="!isMinimized" />
    </div>
  </div>
</template>

<style scoped>
.hero-page-container {
  @apply min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400;
  @apply flex items-center justify-center p-8;
}

.main-window {
  @apply w-[900px] h-[600px] bg-white/10 backdrop-blur-md;
  @apply rounded-2xl border border-white/20 shadow-2xl;
  @apply transition-all duration-300 ease-in-out;
}

.main-window.minimized {
  @apply h-12;
}
</style>
```

#### 1.2 ControlBar 组件

**职责**：提供窗口控制功能（拖拽、展开收起、关闭）

```vue
<template>
  <div 
    class="control-bar"
    @mousedown="startDrag"
    @mousemove="handleDrag"
    @mouseup="endDrag"
  >
    <Button 
      v-show="showBackButton" 
      variant="ghost" 
      size="sm"
      class="back-button"
    >
      ←
    </Button>
    
    <div class="flex-1 flex justify-center">
      <Button 
        variant="ghost" 
        size="sm"
        @click="$emit('toggle')"
        class="toggle-button"
      >
        {{ isMinimized ? '⌃' : '⌄' }}
      </Button>
    </div>
    
    <Button 
      variant="ghost" 
      size="sm"
      @click="$emit('close')"
      class="close-button"
    >
      ×
    </Button>
  </div>
</template>

<style scoped>
.control-bar {
  @apply h-12 flex items-center justify-between px-4;
  @apply border-b border-white/10 cursor-move;
}

.back-button {
  @apply w-8 h-8 text-white/80 hover:text-white hover:bg-white/10;
}

.toggle-button {
  @apply text-white/80 hover:text-white hover:bg-white/10;
}

.close-button {
  @apply w-8 h-8 text-red-400 hover:text-red-300 hover:bg-red-500/20;
}
</style>
```

#### 1.3 HeroCard 组件

**职责**：显示单个英雄的信息卡片

```vue
<template>
  <Card 
    class="hero-card"
    @click="$emit('heroClick', hero.name)"
  >
    <CardContent class="hero-card-content">
      <div class="hero-avatar-container">
        <img 
          :src="hero.iconPath" 
          :alt="hero.name"
          class="hero-avatar"
          @error="handleImageError"
        />
        <Badge 
          :class="getCostBadgeClass(hero.cost)"
          class="cost-badge"
        >
          {{ hero.cost }}
        </Badge>
      </div>
      
      <div class="hero-info">
        <p class="hero-name">{{ hero.name }}</p>
        <p class="hero-stat">出场率: {{ formatRate(hero.playRate) }}</p>
        <p class="hero-stat">均名: {{ formatPlace(hero.avgPlace) }}</p>
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
.hero-card {
  @apply bg-white/10 backdrop-blur-sm border border-white/20;
  @apply hover:bg-white/20 hover:scale-105 hover:shadow-xl;
  @apply transition-all duration-200 cursor-pointer;
  @apply rounded-lg overflow-hidden;
}

.hero-card-content {
  @apply p-3 text-center;
}

.hero-avatar-container {
  @apply relative mb-2;
}

.hero-avatar {
  @apply w-16 h-16 rounded-lg mx-auto object-cover;
}

.cost-badge {
  @apply absolute -top-1 -right-1 w-6 h-6 rounded-full;
  @apply flex items-center justify-center text-xs font-bold;
}

.hero-info {
  @apply space-y-1;
}

.hero-name {
  @apply text-white text-sm font-medium mb-1;
}

.hero-stat {
  @apply text-white/60 text-xs;
}
</style>
```

### 2. 数据接口设计

#### 2.1 Hero 数据模型

```typescript
interface Hero {
  id: string
  name: string           // 中文名称
  enName: string        // 英文名称
  cost: number          // 费用 (1-5)
  iconPath: string      // 头像路径
  playRate: number      // 出场率
  avgPlace: number      // 平均排名
  traits: string[]      // 羁绊列表
}
```

#### 2.2 Store 接口设计

```typescript
// stores/heroes.ts
export const useHeroesStore = defineStore('heroes', () => {
  // 状态
  const heroes = ref<Hero[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 筛选状态
  const searchQuery = ref('')
  const selectedCosts = ref<number[]>([])
  
  // 计算属性
  const filteredHeroes = computed(() => {
    return heroes.value.filter(hero => {
      const matchesSearch = hero.name.toLowerCase().includes(searchQuery.value.toLowerCase())
      const matchesCost = selectedCosts.value.length === 0 || selectedCosts.value.includes(hero.cost)
      return matchesSearch && matchesCost
    })
  })
  
  const heroesByCost = computed(() => {
    const grouped: Record<number, Hero[]> = {}
    filteredHeroes.value.forEach(hero => {
      if (!grouped[hero.cost]) {
        grouped[hero.cost] = []
      }
      grouped[hero.cost].push(hero)
    })
    return grouped
  })
  
  // 方法
  const loadHeroes = async () => {
    loading.value = true
    error.value = null
    try {
      const result = await invoke<Hero[]>('get_heroes')
      heroes.value = result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载英雄数据失败'
    } finally {
      loading.value = false
    }
  }
  
  const searchHeroes = (query: string) => {
    searchQuery.value = query
  }
  
  const filterByCost = (costs: number[]) => {
    selectedCosts.value = costs
  }
  
  return {
    heroes,
    loading,
    error,
    searchQuery,
    selectedCosts,
    filteredHeroes,
    heroesByCost,
    loadHeroes,
    searchHeroes,
    filterByCost
  }
})
```

#### 2.3 Tauri 命令接口

```rust
// src-tauri/src/database.rs
#[tauri::command]
pub async fn get_heroes() -> Result<Vec<Hero>, String> {
    let conn = get_database_connection()?;
    
    let mut stmt = conn.prepare(
        "SELECT cn_name, cost, icon_path, avg_place, play_rate 
         FROM heroes 
         ORDER BY cost, cn_name"
    ).map_err(|e| e.to_string())?;
    
    let hero_iter = stmt.query_map([], |row| {
        Ok(Hero {
            id: row.get::<_, String>(0)?.to_lowercase(),
            name: row.get(0)?,
            en_name: row.get::<_, Option<String>>(1)?.unwrap_or_default(),
            cost: row.get(1)?,
            icon_path: row.get(2)?,
            play_rate: row.get(3)?,
            avg_place: row.get(4)?,
            traits: vec![], // 需要额外查询
        })
    }).map_err(|e| e.to_string())?;
    
    let mut heroes = Vec::new();
    for hero in hero_iter {
        heroes.push(hero.map_err(|e| e.to_string())?);
    }
    
    Ok(heroes)
}
```

## 数据模型

### 1. 前端数据流

```
用户交互 → Store Actions → Tauri Commands → SQLite → 数据返回 → Store State → UI 更新
```

### 2. 状态管理结构

```typescript
// 全局状态结构
interface AppState {
  // 窗口状态
  window: {
    isMinimized: boolean
    isDragging: boolean
    position: { x: number, y: number }
  }
  
  // 导航状态
  navigation: {
    currentPage: string
    history: string[]
  }
  
  // 英雄页面状态
  heroes: {
    data: Hero[]
    loading: boolean
    error: string | null
    filters: {
      search: string
      costs: number[]
    }
  }
}
```

### 3. 组件通信模式

```typescript
// 父子组件通信
interface ComponentEvents {
  // HeroCard 事件
  heroClick: (heroName: string) => void
  
  // ControlBar 事件
  toggle: () => void
  close: () => void
  
  // SearchInput 事件
  search: (query: string) => void
  
  // CostFilter 事件
  filterChange: (costs: number[]) => void
}
```

## 错误处理

### 1. 数据加载错误

```typescript
// 错误处理策略
const handleError = (error: Error) => {
  console.error('英雄数据加载失败:', error)
  
  // 显示用户友好的错误信息
  const errorMessage = error.message.includes('database') 
    ? '数据库连接失败，请检查数据文件'
    : '加载英雄数据失败，请稍后重试'
  
  // 更新错误状态
  heroesStore.error = errorMessage
  
  // 可选：显示重试按钮
  showRetryOption.value = true
}
```

### 2. 图片加载错误

```typescript
// 图片加载失败处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/placeholder-hero.png' // 使用占位符图片
}
```

### 3. 网络错误处理

```typescript
// 网络请求错误处理
const retryWithBackoff = async (fn: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      if (i === maxRetries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, i)))
    }
  }
}
```

## 测试策略

### 1. 单元测试

```typescript
// HeroCard.spec.ts
describe('HeroCard', () => {
  it('应该正确显示英雄信息', () => {
    const hero = {
      id: 'test-hero',
      name: '测试英雄',
      cost: 3,
      iconPath: '/test-icon.png',
      playRate: 0.67,
      avgPlace: 4.38
    }
    
    const wrapper = mount(HeroCard, { props: { hero } })
    
    expect(wrapper.find('.hero-name').text()).toBe('测试英雄')
    expect(wrapper.find('.cost-badge').text()).toBe('3')
    expect(wrapper.find('.hero-stat').at(0).text()).toContain('0.67')
  })
  
  it('应该在点击时发出事件', async () => {
    const wrapper = mount(HeroCard, { props: { hero: mockHero } })
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('heroClick')).toBeTruthy()
    expect(wrapper.emitted('heroClick')[0]).toEqual(['测试英雄'])
  })
})
```

### 2. 集成测试

```typescript
// HeroPage.spec.ts
describe('HeroPage Integration', () => {
  it('应该正确加载和显示英雄列表', async () => {
    const mockHeroes = [
      { id: '1', name: '英雄1', cost: 1, /* ... */ },
      { id: '2', name: '英雄2', cost: 2, /* ... */ }
    ]
    
    // Mock Tauri 命令
    vi.mocked(invoke).mockResolvedValue(mockHeroes)
    
    const wrapper = mount(HeroPage)
    
    // 等待数据加载
    await flushPromises()
    
    expect(wrapper.findAll('.hero-card')).toHaveLength(2)
    expect(wrapper.find('.cost-group[data-cost="1"]')).toBeTruthy()
  })
})
```

### 3. E2E 测试

```typescript
// hero-page.e2e.ts
describe('英雄页面 E2E', () => {
  it('应该支持搜索功能', async () => {
    await page.goto('/heroes')
    
    // 输入搜索关键词
    await page.fill('[data-testid="search-input"]', '劫')
    
    // 验证筛选结果
    const heroCards = await page.locator('.hero-card').count()
    expect(heroCards).toBeGreaterThan(0)
    
    // 验证搜索结果包含关键词
    const heroNames = await page.locator('.hero-name').allTextContents()
    expect(heroNames.some(name => name.includes('劫'))).toBeTruthy()
  })
})
```

## 性能优化

### 1. 虚拟滚动

```vue
<!-- 对于大量英雄数据使用虚拟滚动 -->
<template>
  <RecycleScroller
    class="hero-scroller"
    :items="filteredHeroes"
    :item-size="180"
    key-field="id"
    v-slot="{ item }"
  >
    <HeroCard :hero="item" @hero-click="handleHeroClick" />
  </RecycleScroller>
</template>
```

### 2. 图片懒加载

```vue
<template>
  <img 
    v-lazy="hero.iconPath"
    :alt="hero.name"
    class="hero-avatar"
    loading="lazy"
  />
</template>
```

### 3. 防抖搜索

```typescript
// 搜索防抖
const debouncedSearch = debounce((query: string) => {
  heroesStore.searchHeroes(query)
}, 300)

const handleSearchInput = (event: Event) => {
  const query = (event.target as HTMLInputElement).value
  debouncedSearch(query)
}
```

### 4. 内存优化

```typescript
// 组件卸载时清理资源
onUnmounted(() => {
  // 清理定时器
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
  
  // 清理事件监听器
  window.removeEventListener('resize', handleResize)
  
  // 清理大型数据结构
  heroesStore.$reset()
})
```

## 可访问性设计

### 1. 键盘导航

```vue
<template>
  <div 
    class="hero-card"
    tabindex="0"
    @keydown.enter="handleHeroClick"
    @keydown.space.prevent="handleHeroClick"
    role="button"
    :aria-label="`英雄 ${hero.name}，${hero.cost}费，出场率${hero.playRate}`"
  >
    <!-- 卡片内容 -->
  </div>
</template>
```

### 2. 屏幕阅读器支持

```vue
<template>
  <div role="main" aria-label="英雄列表页面">
    <div role="search" aria-label="搜索英雄">
      <input 
        type="text"
        aria-label="输入英雄名称进行搜索"
        aria-describedby="search-help"
      />
      <div id="search-help" class="sr-only">
        输入英雄名称可以快速筛选英雄列表
      </div>
    </div>
    
    <div role="group" aria-label="费用筛选">
      <!-- 费用筛选按钮 -->
    </div>
    
    <div role="grid" aria-label="英雄列表">
      <!-- 英雄卡片 -->
    </div>
  </div>
</template>
```

### 3. 高对比度支持

```css
@media (prefers-contrast: high) {
  .hero-card {
    @apply border-2 border-white;
  }
  
  .hero-name {
    @apply text-white font-bold;
  }
  
  .cost-badge {
    @apply border-2 border-white;
  }
}
```

## 国际化支持

```typescript
// i18n 配置
const messages = {
  'zh-CN': {
    hero: {
      search: '搜索英雄名称...',
      playRate: '出场率',
      avgPlace: '均名',
      cost: '费用',
      all: '全部'
    }
  },
  'en-US': {
    hero: {
      search: 'Search hero name...',
      playRate: 'Play Rate',
      avgPlace: 'Avg Place',
      cost: 'Cost',
      all: 'All'
    }
  }
}
```

这个设计文档提供了完整的技术架构、组件设计、数据模型、错误处理、测试策略和性能优化方案，为实现英雄页面的Glassmorphism重构提供了详细的技术指导。