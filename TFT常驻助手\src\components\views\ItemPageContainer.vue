<template>
  <div class="item-page-container">
    <!-- 装备列表视图 -->
    <ItemListView 
      v-if="currentView === 'list'"
      @item-click="showItemDetail"
    />
    
    <!-- 装备详情视图 -->
    <ItemDetailView 
      v-else-if="currentView === 'detail' && selectedItemName"
      :item-name="selectedItemName"
      @back="showItemList"
      @hero-click="handleHeroClick"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ItemListView from './ItemListView.vue'
import ItemDetailView from './ItemDetailView.vue'

// 视图状态
const currentView = ref<'list' | 'detail'>('list')
const selectedItemName = ref<string>('')

// 显示装备详情
const showItemDetail = (itemName: string) => {
  selectedItemName.value = itemName
  currentView.value = 'detail'
}

// 返回装备列表
const showItemList = () => {
  currentView.value = 'list'
  selectedItemName.value = ''
}

// 处理英雄点击（可以扩展为跳转到英雄详情）
const handleHeroClick = (heroName: string) => {
  console.log('点击英雄:', heroName)
  // TODO: 可以添加跳转到英雄详情的逻辑
}
</script>

<style scoped>
.item-page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>