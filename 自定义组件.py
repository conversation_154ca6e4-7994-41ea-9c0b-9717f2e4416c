# 自定义组件.py

from PySide6.QtWidgets import Q<PERSON>abel, QScrollArea, QWidget, QVBoxLayout, QHBoxLayout, QFrame, QSizePolicy, QSpacerItem, QPushButton, QLineEdit, QStyle
from PySide6.QtGui import QPixmap, QCursor, QWheelEvent, QPainter, QColor, QPen, QFontMetrics, QIcon
from PySide6.QtCore import Qt, Signal, QSize, QPoint, Property, QPropertyAnimation, QEasingCurve, QTimer
from collections import OrderedDict
import time

from 常量与配置 import (ICON_PLACEHOLDER_BG, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM,
                       HERO_ICON_BASE_DIR, ITEM_ICON_BASE_DIR, TRAIT_ICON_BASE_DIR, HEX_ICON_BASE_DIR,
                       ICON_CACHE, get_icon_path, SCROLL_AREA_STYLE_SIMPLE, BUTTON_STYLE_BASE,
                       LINE_EDIT_STYLE, ICON_SIZE_LARGE, ICON_SIZE_MEDIUM, ICON_SIZE_SMALL, BORDER_COLOR,
                       FONT_SIZE_SMALL, FONT_WEIGHT_BOLD, ENABLE_ICON_CACHE_LIMIT, MAX_ICON_CACHE_SIZE, 
                       ICON_CACHE_CLEAN_THRESHOLD, ICON_CACHE_KEEP_COUNT)

# --- 使用有序字典来实现LRU缓存替换策略 ---
ICON_CACHE_LRU = OrderedDict()  # 跟踪图标最近使用情况
ICON_CACHE_ACCESS_TIME = {}  # 跟踪每个图标的最后访问时间

# --- 图标缓存辅助函数 ---
def get_cached_pixmap(icon_relative_path, base_path_type='item'):
    """
    从缓存获取图标，如果不存在则加载并缓存。
    Args:
        icon_relative_path (str): 数据库中存储的相对路径。
        base_path_type (str): 图标类型 ('hero', 'item', 'trait', 'hex')，用于确定基础路径。
    Returns:
        QPixmap or None: 缓存的或新加载的 Pixmap 对象，失败则返回 None。
    """
    if not icon_relative_path:
        return None

    # 根据类型确定基础目录
    if base_path_type == 'hero':
        base_dir = HERO_ICON_BASE_DIR
    elif base_path_type == 'item':
        base_dir = ITEM_ICON_BASE_DIR
    elif base_path_type == 'trait':
        base_dir = TRAIT_ICON_BASE_DIR
    elif base_path_type == 'hex':
        base_dir = HEX_ICON_BASE_DIR
    else:
        base_dir = BASE_DIR # 默认为项目根目录

    full_path_str = get_icon_path(base_dir, icon_relative_path)
    if not full_path_str:
        # print(f"警告: 图标路径无效: {icon_relative_path}")
        return None

    # 使用完整路径作为缓存键
    current_time = time.time()
    
    # 当缓存中存在该图标时，更新其最近使用记录
    if full_path_str in ICON_CACHE:
        # 更新LRU缓存顺序 (将其移到最新位置)
        if full_path_str in ICON_CACHE_LRU:
            ICON_CACHE_LRU.pop(full_path_str)
        ICON_CACHE_LRU[full_path_str] = None  # 值本身不重要，仅用于跟踪顺序
        ICON_CACHE_ACCESS_TIME[full_path_str] = current_time
        return ICON_CACHE[full_path_str]
        
    # 缓存未命中，需要加载图标
    # 先检查是否需要清理缓存
    check_and_clean_icon_cache()
    
    # 加载图标并缓存
    pixmap = QPixmap(full_path_str)
    if not pixmap.isNull():
        ICON_CACHE[full_path_str] = pixmap
        ICON_CACHE_LRU[full_path_str] = None
        ICON_CACHE_ACCESS_TIME[full_path_str] = current_time
        # print(f"图标已加载并缓存: {full_path_str}") # Debug
    else:
        # print(f"警告: 无法加载图标文件: {full_path_str}")
        ICON_CACHE[full_path_str] = None # 标记为加载失败，避免重复尝试
        ICON_CACHE_LRU[full_path_str] = None
        ICON_CACHE_ACCESS_TIME[full_path_str] = current_time
        return None

    return ICON_CACHE[full_path_str]

def check_and_clean_icon_cache():
    """
    检查图标缓存大小，如果超出阈值则清理最近最少使用的图标
    """
    if not ENABLE_ICON_CACHE_LIMIT:
        return
        
    if len(ICON_CACHE) > ICON_CACHE_CLEAN_THRESHOLD:
        print(f"图标缓存达到阈值 ({len(ICON_CACHE)}个)，开始清理...")
        # 计算要保留的图标数量
        keep_count = min(ICON_CACHE_KEEP_COUNT, len(ICON_CACHE))
        
        # 清理缓存，只保留最近使用的图标
        items_to_keep = list(ICON_CACHE_LRU.keys())[-keep_count:]
        
        # 创建新的 OrderedDict 仅包含要保留的项
        new_lru = OrderedDict()
        new_cache = {}
        new_access_time = {}
        
        for key in items_to_keep:
            new_lru[key] = None
            if key in ICON_CACHE:
                new_cache[key] = ICON_CACHE[key]
            if key in ICON_CACHE_ACCESS_TIME:
                new_access_time[key] = ICON_CACHE_ACCESS_TIME[key]
                
        # 用新字典替换旧的
        ICON_CACHE.clear()
        ICON_CACHE.update(new_cache)
        ICON_CACHE_LRU.clear()
        ICON_CACHE_LRU.update(new_lru)
        ICON_CACHE_ACCESS_TIME.clear()
        ICON_CACHE_ACCESS_TIME.update(new_access_time)
        
        print(f"图标缓存清理完成，现有缓存数量: {len(ICON_CACHE)}个")

def clear_icon_cache():
    """完全清空图标缓存"""
    ICON_CACHE.clear()
    ICON_CACHE_LRU.clear()
    ICON_CACHE_ACCESS_TIME.clear()
    print("图标缓存已完全清空")

# --- 可点击标签 ---
class ClickableLabel(QLabel):
    """一个可以发出点击信号的 QLabel"""
    clicked = Signal(object) # 发出关联的数据 (可以是字符串、字典等)

    def __init__(self, data=None, parent=None):
        super().__init__(parent)
        self._data = data
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self._hover = False

    def set_data(self, data):
        """设置与此标签关联的数据"""
        self._data = data

    def get_data(self):
        """获取与此标签关联的数据"""
        return self._data

    def enterEvent(self, event):
        self._hover = True
        self.update() # 触发重绘以显示悬停效果 (如果需要)
        super().enterEvent(event)

    def leaveEvent(self, event):
        self._hover = False
        self.update() # 触发重绘以移除悬停效果
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            # print(f"ClickableLabel clicked with data: {self._data}") # Debug
            if self._data is not None: # 只有 data 非 None 时才发出信号
                self.clicked.emit(self._data)
        super().mousePressEvent(event)

    # 可选：添加简单的悬停效果，例如下划线
    # def paintEvent(self, event):
    #     super().paintEvent(event)
    #     if self._hover:
    #         painter = QPainter(self)
    #         pen = QPen(QColor(TEXT_COLOR_HIGHLIGHT))
    #         pen.setWidth(1)
    #         painter.setPen(pen)
    #         font_metrics = QFontMetrics(self.font())
    #         text_width = font_metrics.horizontalAdvance(self.text())
    #         # 在文本下方绘制下划线
    #         y = self.height() - 2 # 稍微向上偏移
    #         # 根据对齐方式调整 x 坐标 (简化处理，假设居左)
    #         x_start = 0
    #         if self.alignment() & Qt.AlignmentFlag.AlignHCenter:
    #              x_start = (self.width() - text_width) / 2
    #         elif self.alignment() & Qt.AlignmentFlag.AlignRight:
    #              x_start = self.width() - text_width
    #         painter.drawLine(x_start, y, x_start + text_width, y)


# --- 带图标和占位符的标签 ---
class IconLabel(ClickableLabel):
    """用于显示英雄、装备等图标的标签，带加载和占位符逻辑"""
    def __init__(self, data=None, icon_size=ICON_SIZE_LARGE, icon_type='item', placeholder_text='?', parent=None):
        super().__init__(data, parent)
        self.icon_size = icon_size
        self.icon_type = icon_type
        self.placeholder_text = placeholder_text
        self.setMinimumSize(icon_size, icon_size) # 保证最小尺寸
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed) # 策略设为固定
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setContentsMargins(0, 0, 0, 0)
        # --- 样式中字体大小也用 icon_size 计算，确保占位符合适 ---
        self.placeholder_style = f"""
            QLabel {{
                background-color: {ICON_PLACEHOLDER_BG};
                color: {TEXT_COLOR_LIGHT};
                font-size: {max(FONT_SIZE_SMALL, int(icon_size * 0.3))}px;
                font-weight: {FONT_WEIGHT_BOLD};
                border-radius: {int(icon_size * 0.1)}px;
                border: 1px solid {BORDER_COLOR};
                padding: 0px;
            }}
        """
        self.icon_style = f"""
            QLabel {{
                border-radius: {int(icon_size * 0.1)}px;
                border: none;
                background-color: transparent;
                padding: 0px;
            }}
        """ 
        self.setStyleSheet(self.placeholder_style) # 初始为占位符样式
        self.pixmap = None
        self.current_pixmap_path = None # 存储当前显示的pixmap路径，避免重复设置
        self.scaled_pixmap = None  # 存储缩放后的pixmap，避免重复缩放

    def set_icon(self, icon_relative_path, name_for_placeholder=None):
        """设置图标"""
        # 优化：如果请求的路径和当前相同，则不处理
        if icon_relative_path == self.current_pixmap_path:
             return
             
        # 释放之前的pixmap以减少内存占用
        if self.pixmap is not None:
            self.pixmap = None
        if self.scaled_pixmap is not None:
            self.scaled_pixmap = None
             
        self.pixmap = get_cached_pixmap(icon_relative_path, self.icon_type)
        self.current_pixmap_path = icon_relative_path # 更新当前路径

        if self.pixmap:
            # --- 修改：使用 self.icon_size 来缩放 pixmap，而不是 self.width/height ---
            target_size = QSize(self.icon_size, self.icon_size)
            
            self.scaled_pixmap = self.pixmap.scaled(
                target_size, # 使用 icon_size 创建的目标尺寸
                Qt.AspectRatioMode.KeepAspectRatio, # 保持比例
                Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(self.scaled_pixmap) # 设置缩放后的 Pixmap
            self.setStyleSheet(self.icon_style)
            self.setText("")
        else:
            placeholder = self.placeholder_text
            if name_for_placeholder:
                if len(name_for_placeholder) >= 2 and '\u4e00' <= name_for_placeholder[0] <= '\u9fff':
                    placeholder = name_for_placeholder[:2]
                elif len(name_for_placeholder) >= 1:
                    placeholder = name_for_placeholder[0].upper()

            self.setPixmap(QPixmap()) # 清除旧 Pixmap
            self.setText(placeholder)
            self.setStyleSheet(self.placeholder_style)
            
    def clearPixmap(self):
        """清除图标并释放内存"""
        self.setPixmap(QPixmap())
        self.setText(self.placeholder_text)
        self.setStyleSheet(self.placeholder_style)
        self.pixmap = None
        self.scaled_pixmap = None
        self.current_pixmap_path = None

# --- 水平滚动区域 (鼠标滚轮控制) ---
class WheelHorizontalScrollArea(QScrollArea):
    """一个可以通过鼠标滚轮水平滚动的 QScrollArea"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff) # 隐藏水平滚动条
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)   # 隐藏垂直滚动条
        self.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE) # 应用基本样式

    def wheelEvent(self, event: QWheelEvent):
        """处理鼠标滚轮事件，转换为水平滚动"""
        angle_delta = event.angleDelta().y() # Y方向滚动通常更常见
        if not angle_delta:
             angle_delta = event.angleDelta().x() # 备用：处理水平滚轮

        h_bar = self.horizontalScrollBar()
        if h_bar:
            # 调整滚动幅度，可以根据需要修改 0.4 这个系数
            new_value = h_bar.value() - int(angle_delta * 0.4)
            h_bar.setValue(new_value)
            event.accept() # 事件已被处理
        else:
            event.ignore() # 交给父级处理

# --- 带清除按钮的搜索框 ---
class SearchLineEdit(QLineEdit):
    """一个带有清除按钮的 QLineEdit"""
    searchChanged = Signal(str) # 搜索文本变化时发出信号

    def __init__(self, placeholder_text="搜索...", parent=None):
        super().__init__(parent)
        self.placeholder_text = placeholder_text
        self.setStyleSheet(LINE_EDIT_STYLE)
        self.setPlaceholderText(placeholder_text)

        # 创建清除按钮
        self.clear_button = QPushButton(self)
        # 使用 Qt 内置图标 SP_DialogCloseButton
        close_icon = self.style().standardIcon(getattr(QStyle, 'SP_DialogCloseButton', QStyle.StandardPixmap.SP_TitleBarCloseButton))
        self.clear_button.setIcon(close_icon)
        self.clear_button.setIconSize(QSize(10, 10)) # 调整图标大小
        self.clear_button.setCursor(Qt.CursorShape.ArrowCursor)
        self.clear_button.setStyleSheet("QPushButton { border: none; padding: 0px; background: transparent; }")
        self.clear_button.setFlat(True)
        self.clear_button.setFixedSize(16, 16) # 固定按钮大小
        self.clear_button.hide() # 默认隐藏

        # 布局按钮
        layout = QHBoxLayout(self)
        layout.addStretch()
        layout.addWidget(self.clear_button)
        layout.setContentsMargins(0, 0, 5, 0) # 按钮靠右边距
        self.setLayout(layout)

        # 连接信号
        self.clear_button.clicked.connect(self.clear_text)
        self.textChanged.connect(self.update_clear_button_visibility)
        self.textChanged.connect(self.searchChanged) # 直接转发信号

        # 添加防抖定时器
        self._debounce_timer = QTimer(self)
        self._debounce_timer.setSingleShot(True)
        self._debounce_timer.setInterval(300) # 300毫秒防抖
        self._debounce_timer.timeout.connect(self._emit_debounced_search)
        self.textChanged.connect(self._start_debounce)

    def _start_debounce(self, text):
        """文本变化时重启防抖定时器"""
        self._debounce_timer.stop() # 停止之前的计时
        self._debounce_timer.start() # 重新开始计时

    def _emit_debounced_search(self):
        """防抖后发出搜索信号"""
        self.searchChanged.emit(self.text())

    def update_clear_button_visibility(self, text):
        """根据是否有文本显示或隐藏清除按钮"""
        self.clear_button.setVisible(bool(text))

    def clear_text(self):
        """清除文本并发出信号"""
        self.clear() # 使用 QLineEdit 自带的 clear 方法

    def resizeEvent(self, event):
        """调整大小时重新布局清除按钮"""
        super().resizeEvent(event)
        # 确保布局在调整大小后更新
        self.layout().activate()

# --- 可折叠区域 (可选，如果需要复杂布局) ---
# 暂时不实现，如果需要可以后续添加

# --- 加载指示器 (可选) ---
# 暂时不实现，如果需要可以后续添加