# TFT常驻助手重构设计文档

## 概述

本设计文档描述了如何将现有的基于PySide6的TFT常驻助手重构为使用Tauri + Vue 3 + TypeScript + Tailwind CSS的现代化桌面应用。设计重点是完全复刻现有功能和交互逻辑，确保用户体验的一致性。

## 架构设计

### 整体架构

```
TFT常驻助手/
├── src-tauri/           # Tauri后端 (Rust)
│   ├── src/
│   │   ├── main.rs      # 主程序入口
│   │   ├── database.rs  # 数据库操作模块
│   │   ├── window.rs    # 窗口管理模块
│   │   └── lib.rs       # 库入口
│   ├── Cargo.toml       # Rust依赖配置
│   └── tauri.conf.json  # Tauri配置
├── src/                 # Vue前端
│   ├── components/      # Vue组件
│   │   ├── common/      # 通用组件
│   │   ├── views/       # 视图组件
│   │   └── ui/          # UI组件
│   ├── stores/          # Pinia状态管理
│   ├── types/           # TypeScript类型定义
│   ├── utils/           # 工具函数
│   ├── styles/          # 样式文件
│   ├── App.vue          # 根组件
│   └── main.ts          # 前端入口
├── public/              # 静态资源
├── package.json         # Node.js依赖
├── vite.config.ts       # Vite配置
├── tailwind.config.js   # Tailwind配置
└── tsconfig.json        # TypeScript配置
```

### 技术栈选择

- **前端框架**: Vue 3 (Composition API)
- **类型系统**: TypeScript
- **样式框架**: Tailwind CSS
- **构建工具**: Vite
- **状态管理**: Pinia
- **桌面框架**: Tauri
- **后端语言**: Rust
- **数据库**: SQLite (复用现有数据库)

## 组件设计

### 主窗口组件 (MainWindow.vue)

复刻 `主窗口.py` 的 `MainWindow` 类功能：

```typescript
interface MainWindowState {
  currentPage: number;
  isMinimized: boolean;
  historyStack: Array<{pageIndex: number, dataKey: string}>;
  viewsLoaded: Record<number, boolean>;
}

interface PageConstants {
  PAGE_COMP_LIST: 0;
  PAGE_HERO_LIST: 1;
  PAGE_ITEM_LIST: 2;
  PAGE_HEX_LIST: 3;
  PAGE_COMP_DETAIL: 4;
  PAGE_HERO_DETAIL: 5;
  PAGE_ITEM_DETAIL: 6;
}
```

**主要功能**:
- 无边框窗口管理
- 控制条拖拽和点击切换
- 导航历史记录管理
- 页面切换逻辑
- 资源管理和内存优化

### 控制条组件 (ControlBar.vue)

复刻控制条功能：

```typescript
interface ControlBarProps {
  isMinimized: boolean;
  hasHistory: boolean;
}

interface ControlBarEmits {
  'toggle-window': void;
  'go-back': void;
  'close-app': void;
}
```

**组件结构**:
- 返回按钮 (动态宽度)
- 展开/收起指示器
- 关闭按钮
- 拖拽区域处理

### 导航栏组件 (NavigationBar.vue)

复刻导航栏功能：

```typescript
interface NavigationItem {
  name: string;
  pageIndex: number;
}

interface NavigationBarProps {
  currentPage: number;
  isVisible: boolean;
}
```

**导航项目**:
- 阵容 (PAGE_COMP_LIST)
- 英雄 (PAGE_HERO_LIST) 
- 装备 (PAGE_ITEM_LIST)
- 海克斯 (PAGE_HEX_LIST)

### 视图组件

#### 列表视图组件

**阵容列表视图 (CompListView.vue)**
```typescript
interface CompListItem {
  name: string;
  tier: string;
  traits: string[];
  heroes: string[];
}
```

**英雄列表视图 (HeroListView.vue)**
```typescript
interface HeroListItem {
  cn_name: string;
  en_name: string;
  cost: number;
  traits: string[];
  icon_path: string;
}
```

**装备列表视图 (ItemListView.vue)**
```typescript
interface ItemListItem {
  name: string;
  en_name: string;
  icon_path: string;
  components: string[];
}
```

**海克斯列表视图 (HexListView.vue)**
```typescript
interface HexListItem {
  name: string;
  tier: string;
  description: string;
  icon_path: string;
}
```

#### 详情视图组件

**阵容详情视图 (CompDetailView.vue)**
- 阵容基本信息
- 英雄配置展示
- 装备推荐
- 相关链接跳转

**英雄详情视图 (HeroDetailView.vue)**
- 英雄基本属性
- 技能信息
- 推荐装备
- 羁绊信息

**装备详情视图 (ItemDetailView.vue)**
- 装备属性
- 合成路径
- 适用英雄
- 相关装备

### 自定义组件

#### 图标标签组件 (IconLabel.vue)

复刻 `IconLabel` 类功能：

```typescript
interface IconLabelProps {
  data?: any;
  iconSize: number;
  iconType: 'hero' | 'item' | 'trait' | 'hex';
  placeholderText: string;
  iconPath?: string;
  nameForPlaceholder?: string;
}

interface IconLabelEmits {
  'click': (data: any) => void;
}
```

#### 可点击标签组件 (ClickableLabel.vue)

复刻 `ClickableLabel` 类功能：

```typescript
interface ClickableLabelProps {
  data?: any;
  text: string;
}

interface ClickableLabelEmits {
  'click': (data: any) => void;
}
```

#### 水平滚动区域组件 (HorizontalScrollArea.vue)

复刻 `WheelHorizontalScrollArea` 类功能：

```typescript
interface HorizontalScrollAreaProps {
  hideScrollbar: boolean;
}
```

#### 搜索输入框组件 (SearchInput.vue)

复刻 `SearchLineEdit` 类功能：

```typescript
interface SearchInputProps {
  placeholder: string;
  debounceMs: number;
}

interface SearchInputEmits {
  'search-changed': (text: string) => void;
}
```

## 数据管理设计

### 数据库操作 (Tauri Commands)

将 `数据库操作.py` 的功能迁移到Rust：

```rust
// src-tauri/src/database.rs
use tauri::command;
use rusqlite::{Connection, Result};
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize)]
pub struct QueryResult {
    data: Vec<serde_json::Value>,
    error: Option<String>,
}

#[command]
pub async fn execute_query(sql: String, params: Vec<String>) -> QueryResult {
    // 实现异步数据库查询逻辑
}

#[command]
pub async fn get_comp_list() -> QueryResult {
    // 获取阵容列表
}

#[command]
pub async fn get_hero_list() -> QueryResult {
    // 获取英雄列表
}

#[command]
pub async fn get_item_list() -> QueryResult {
    // 获取装备列表
}

#[command]
pub async fn get_hex_list() -> QueryResult {
    // 获取海克斯列表
}

#[command]
pub async fn get_comp_detail(comp_name: String) -> QueryResult {
    // 获取阵容详情
}

#[command]
pub async fn get_hero_detail(hero_name: String) -> QueryResult {
    // 获取英雄详情
}

#[command]
pub async fn get_item_detail(item_name: String) -> QueryResult {
    // 获取装备详情
}
```

### 状态管理 (Pinia Stores)

#### 主应用状态 (useAppStore)

```typescript
// src/stores/app.ts
export const useAppStore = defineStore('app', () => {
  const currentPage = ref<number>(0);
  const isMinimized = ref<boolean>(false);
  const historyStack = ref<Array<{pageIndex: number, dataKey: string}>>([]);
  const viewsLoaded = ref<Record<number, boolean>>({});

  const switchMainPage = (index: number) => {
    // 复刻页面切换逻辑
  };

  const recordNavigation = (pageIndex: number, dataKey: string) => {
    // 复刻导航记录逻辑
  };

  const goBack = () => {
    // 复刻返回逻辑
  };

  return {
    currentPage,
    isMinimized,
    historyStack,
    viewsLoaded,
    switchMainPage,
    recordNavigation,
    goBack
  };
});
```

#### 数据缓存状态 (useDataStore)

```typescript
// src/stores/data.ts
export const useDataStore = defineStore('data', () => {
  const globalHeroInfoMap = ref<Record<string, any>>({});
  const globalTraitIconMap = ref<Record<string, string>>({});
  const globalItemInfoMap = ref<Record<string, any>>({});
  const queryCache = ref<Record<string, any>>({});

  const preloadGlobalData = async () => {
    // 复刻全局数据预加载逻辑
  };

  const getCachedQuery = (key: string) => {
    // 复刻查询缓存逻辑
  };

  return {
    globalHeroInfoMap,
    globalTraitIconMap,
    globalItemInfoMap,
    queryCache,
    preloadGlobalData,
    getCachedQuery
  };
});
```

### 图标缓存管理

```typescript
// src/utils/iconCache.ts
interface IconCacheItem {
  url: string;
  timestamp: number;
}

class IconCacheManager {
  private cache = new Map<string, IconCacheItem>();
  private lruOrder: string[] = [];
  private maxSize = 300;

  getCachedIcon(iconPath: string, baseType: string): string | null {
    // 复刻图标缓存逻辑
  }

  clearCache(): void {
    // 复刻缓存清理逻辑
  }
}

export const iconCache = new IconCacheManager();
```

## 窗口管理设计

### 窗口配置 (tauri.conf.json)

```json
{
  "tauri": {
    "windows": [
      {
        "title": "云顶之弈助手 (TFT Helper)",
        "width": 580,
        "height": 700,
        "minWidth": 580,
        "minHeight": 42,
        "decorations": false,
        "alwaysOnTop": true,
        "resizable": true,
        "center": true
      }
    ]
  }
}
```

### 窗口控制 (Tauri Commands)

```rust
// src-tauri/src/window.rs
use tauri::{command, Window};

#[command]
pub fn toggle_window_size(window: Window) {
    // 实现窗口展开/收起逻辑
}

#[command]
pub fn set_window_position(window: Window, x: i32, y: i32) {
    // 设置窗口位置
}

#[command]
pub fn get_window_position(window: Window) -> (i32, i32) {
    // 获取窗口位置
}
```

## 样式设计

### Tailwind配置

```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{vue,js,ts}'],
  theme: {
    extend: {
      colors: {
        // 复刻常量与配置.py中的颜色配置
        'window-border': '#0F172A',
        'window-bg': '#1E293B',
        'control-bar-bg': '#0F172A',
        'section-bg': '#293548',
        'item-row-hover': '#334155',
        'border-color': '#4A5568',
        'text-light': '#E2E8F0',
        'text-medium': '#A0AEC0',
        'text-dark': '#1A202C',
        'text-highlight': '#8EBBFF',
        'text-error': '#FF6B6B',
        'tier-s': '#FF5A5F',
        'tier-a': '#FFA756',
        'tier-b': '#FFD700',
        'tier-c': '#F3FF59',
        'tier-d': '#8AFF40',
        'cost-1': '#D3D3D3',
        'cost-2': '#32CD32',
        'cost-3': '#00BFFF',
        'cost-4': '#BF00FF',
        'cost-5': '#FFD700'
      },
      fontSize: {
        'xs': '11px',
        'sm': '12px',
        'base': '13px',
        'lg': '14px',
        'xl': '16px'
      },
      spacing: {
        'control-bar': '40px',
        'nav-bar': '40px'
      }
    }
  }
}
```

### 全局样式

```css
/* src/styles/global.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 复刻滚动条样式 */
@layer components {
  .custom-scrollbar {
    @apply scrollbar-thin scrollbar-track-window-bg scrollbar-thumb-border-color;
  }
  
  .icon-placeholder {
    @apply bg-gray-600 text-text-light rounded border border-border-color flex items-center justify-center;
  }
  
  .tier-badge {
    @apply px-2 py-1 rounded text-xs font-bold;
  }
  
  .tier-s { @apply bg-tier-s text-text-dark; }
  .tier-a { @apply bg-tier-a text-text-dark; }
  .tier-b { @apply bg-tier-b text-text-dark; }
  .tier-c { @apply bg-tier-c text-text-dark; }
  .tier-d { @apply bg-tier-d text-text-dark; }
}
```

## 错误处理

### 前端错误处理

```typescript
// src/utils/errorHandler.ts
export class ErrorHandler {
  static handleDatabaseError(error: any): void {
    console.error('数据库错误:', error);
    // 显示用户友好的错误信息
  }

  static handleWindowError(error: any): void {
    console.error('窗口操作错误:', error);
  }

  static handleIconLoadError(iconPath: string): void {
    console.warn('图标加载失败:', iconPath);
  }
}
```

### 后端错误处理

```rust
// src-tauri/src/error.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub enum AppError {
    DatabaseError(String),
    WindowError(String),
    FileNotFound(String),
}

impl std::fmt::Display for AppError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            AppError::DatabaseError(msg) => write!(f, "数据库错误: {}", msg),
            AppError::WindowError(msg) => write!(f, "窗口错误: {}", msg),
            AppError::FileNotFound(msg) => write!(f, "文件未找到: {}", msg),
        }
    }
}
```

## 测试策略

### 单元测试

- Vue组件测试 (Vitest + Vue Test Utils)
- Rust函数测试 (内置测试框架)
- 工具函数测试

### 集成测试

- 数据库操作测试
- 窗口管理测试
- 组件交互测试

### 端到端测试

- 用户操作流程测试
- 页面导航测试
- 数据加载测试

## 性能优化

### 前端优化

- 组件懒加载
- 虚拟滚动 (大列表)
- 图标缓存管理
- 防抖搜索

### 后端优化

- 数据库连接池
- 查询结果缓存
- 异步操作优化

### 内存管理

- 组件卸载时清理资源
- 图标缓存大小限制
- 定期垃圾回收