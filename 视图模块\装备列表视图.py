# 视图模块/装备列表视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
                               QSpacerItem, QSizePolicy, QFrame, QPushButton, QCheckBox)
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QCursor

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_HIGHLIGHT,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ITEM_ROW_BG_HOVER,
                       ICON_SIZE_LARGE, BUTTON_BG, CONTROL_BAR_BG, TEXT_COLOR_DARK,
                       FONT_SIZE_NORMAL, FONT_SIZE_MEDIUM, FONT_SIZE_SMALL)
from 自定义组件 import IconLabel, ClickableLabel, SearchLineEdit # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

class 装备列表视图(QWidget):
    """显示装备列表的视图"""
    item_selected = Signal(str) # 点击装备时发出信号，参数为装备名称

    def __init__(self, parent=None):
        super().__init__(parent)
        self.all_item_data = []
        self.filtered_item_data = []
        self.current_sort_column = 'play_rate'
        self.current_sort_order = Qt.SortOrder.DescendingOrder
        self.header_widgets = {}
        self.category_checkboxes = {}
        self._updating_checkboxes = False

        self._is_loading_items = False      # 新增：装备数据加载状态
        self._is_loading_categories = False # 新增：类别数据加载状态

        self.init_ui()
        # 数据由主窗口的 switch_main_page 触发加载
        # self.load_data() 
        # self.load_categories_and_create_filters()

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(8)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 搜索框 ---
        self.search_box = SearchLineEdit(placeholder_text="搜索装备名称...")
        self.search_box.searchChanged.connect(self.apply_filters_and_sort)
        self.main_layout.addWidget(self.search_box)

        # --- 装备类型筛选区域 ---
        self.category_filter_layout = QHBoxLayout()
        self.category_filter_layout.setContentsMargins(5, 0, 5, 5)
        self.category_filter_layout.setSpacing(10)
        self.category_label = QLabel("装备类型:") # 保存引用以便显示/隐藏
        self.category_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 10px;")
        self.category_filter_layout.addWidget(self.category_label)
        self.category_filter_layout.addStretch(1)
        self.main_layout.addLayout(self.category_filter_layout)
        self.category_label.hide() # 初始隐藏，直到类别加载完成

        # --- 加载提示 QLabel ---
        self.loading_label = QLabel("正在加载装备数据...", alignment=Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 14px; padding: 30px;")
        self.main_layout.addWidget(self.loading_label) # 直接添加到主布局
        self.loading_label.show() # 默认显示加载中

        # --- 新增：固定表头区域 ---
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet(f"background-color: {CONTROL_BAR_BG}; border: 1px solid {BORDER_COLOR}; border-radius: 4px;")
        self.header_frame.setFixedHeight(50)  # 固定表头高度
        self.header_layout = QVBoxLayout(self.header_frame)
        self.header_layout.setContentsMargins(0, 0, 0, 0)
        self.header_layout.setSpacing(0)
        self.main_layout.addWidget(self.header_frame)
        self.header_frame.hide()  # 初始隐藏，等数据加载完成后显示

        # --- 装备列表滚动区域 (初始隐藏) ---
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.hide() # 初始隐藏

        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(1)

        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area, 1)

    def load_data_if_needed(self):
        """如果数据尚未加载，则启动加载过程。"""
        if not self.all_item_data and not self._is_loading_items:
            self.load_data()
        if not self.category_checkboxes and not self._is_loading_categories:
            self.load_categories_and_create_filters()
        # 如果已加载但UI未正确显示，尝试更新
        elif self.all_item_data and self.category_checkboxes and not self.is_loading() and self.scroll_area.isHidden():
            self.check_all_loaded_and_update_ui()

    def is_loading(self):
        """检查是否有任何数据仍在加载中"""
        return self._is_loading_items or self._is_loading_categories

    def load_categories_and_create_filters(self):
        """从数据库加载装备类别并创建复选框"""
        if self._is_loading_categories or (self.category_checkboxes and len(self.category_checkboxes) > 1): # >1 因为至少有'全部'
            print("装备类别正在加载或已加载，跳过。")
            return
        print("开始加载装备类别...")
        self._is_loading_categories = True
        self.show_loading_state() # 确保加载状态显示

        sql = "SELECT category_name FROM item_categories ORDER BY rowid" # 按数据库顺序获取
        query_key = "item_categories_list"

        execute_query_async(
            sql,
            on_success=self.on_categories_loaded,
            on_error=lambda err: print(f"加载装备类别失败: {err}"),
            query_key=query_key
        )

    def on_categories_loaded(self, results):
        """装备类别加载成功后创建复选框"""
        print(f"装备类别加载成功: {results}")
        self._is_loading_categories = False # 标记类别加载完成

        # 清理旧的复选框和 stretch
        if hasattr(self, 'category_filter_layout') and self.category_filter_layout is not None:
            while self.category_filter_layout.count() > 1:
                item = self.category_filter_layout.takeAt(1)
                if item is None: continue
                widget = item.widget()
                if widget:
                    widget.deleteLater()
                else:
                    self.category_filter_layout.removeItem(item)
        else:
            print("错误: category_filter_layout 未初始化或已被删除")
            self.check_all_loaded_and_update_ui() # 仍然尝试更新UI
            return

        self.category_checkboxes.clear()

        if not results:
            print("未能加载到装备类别。将不显示类别筛选。")
            self.category_label.hide()
            # 即使类别加载失败，也要确保 _is_loading_categories 为 False 并尝试更新UI
            self.check_all_loaded_and_update_ui()
            return

        self.category_label.show()

        checkbox_style = f"""
            QCheckBox {{
                color: {TEXT_COLOR_MEDIUM};
                font-size: 10px;
                spacing: 5px; /* 文字和框的间距 */
            }}
            QCheckBox::indicator {{
                width: 12px;
                height: 12px;
                border: 1px solid {BORDER_COLOR};
                border-radius: 3px;
                background-color: {BUTTON_BG};
            }}
            QCheckBox::indicator:checked {{
                background-color: {TEXT_COLOR_HIGHLIGHT};
                border: 1px solid {TEXT_COLOR_HIGHLIGHT};
                /* image: url(icons/checkmark.png); 可选：添加勾选图标 */
            }}
            QCheckBox::indicator:hover {{
                border: 1px solid {TEXT_COLOR_LIGHT};
            }}
            QCheckBox:hover {{
                color: {TEXT_COLOR_LIGHT};
            }}
        """
        # 添加 "全部" 选项
        all_checkbox = QCheckBox("全部")
        all_checkbox.setStyleSheet(checkbox_style)
        all_checkbox.setChecked(True) # 默认选中全部
        all_checkbox.stateChanged.connect(self.handle_all_categories_change)
        self.category_filter_layout.addWidget(all_checkbox)
        self.category_checkboxes["全部"] = all_checkbox # 特殊键

        for row in results:
            category_name = row.get('category_name')
            if category_name:
                checkbox = QCheckBox(category_name)
                checkbox.setStyleSheet(checkbox_style)
                checkbox.setProperty("category_name", category_name) # 存储类别名
                # 修改：连接到新的处理函数 handle_category_change
                checkbox.stateChanged.connect(self.handle_category_change)
                self.category_filter_layout.addWidget(checkbox)
                self.category_checkboxes[category_name] = checkbox

        self.category_filter_layout.addStretch(1) # 添加回 stretch，将复选框推到左侧
        self.check_all_loaded_and_update_ui() # 尝试更新主UI

    def handle_all_categories_change(self, state):
        """处理 "全部" 复选框的变化"""
        if self._updating_checkboxes: # 防止信号循环
            return
        self._updating_checkboxes = True
        is_checked = (state == Qt.CheckState.Checked.value)
        # 如果 "全部" 被选中，取消其他所有选中
        if is_checked:
            for name, checkbox in self.category_checkboxes.items():
                if name != "全部":
                    checkbox.setChecked(False)
        # 如果 "全部" 被取消选中，并且没有其他任何类别被选中，则强制重新选中 "全部"
        elif not any(cb.isChecked() for name, cb in self.category_checkboxes.items() if name != "全部"):
             print("没有选中任何类别，自动选中'全部'")
             self.category_checkboxes["全部"].setChecked(True) # 重新选中"全部"会再次触发此函数，但会被 _updating_checkboxes 阻止

        self._updating_checkboxes = False
        # 触发筛选更新
        self.apply_filters_and_sort()

    def handle_category_change(self, state):
        """处理单个类别复选框的变化"""
        if self._updating_checkboxes: # 防止信号循环
            return
        self._updating_checkboxes = True
        is_checked = (state == Qt.CheckState.Checked.value)
        sender_checkbox = self.sender() # 获取发出信号的复选框

        if is_checked:
            # 如果有任何一个特定类别被选中，取消 "全部" 的选中
            if self.category_checkboxes.get("全部"):
                self.category_checkboxes["全部"].setChecked(False)
        else:
            # 如果所有特定类别都被取消选中了，则自动选中 "全部"
            if not any(cb.isChecked() for name, cb in self.category_checkboxes.items() if name != "全部"):
                 print("所有特定类别已取消，自动选中'全部'")
                 if self.category_checkboxes.get("全部"):
                    self.category_checkboxes["全部"].setChecked(True) # 这会触发 handle_all_categories_change

        self._updating_checkboxes = False
        self.apply_filters_and_sort()

    def load_data(self):
        """从数据库异步加载所有装备数据及其类别"""
        if self._is_loading_items or self.all_item_data:
            print("装备数据正在加载或已加载，跳过。")
            return
        print("开始异步加载装备列表数据（包含类别）...")
        self._is_loading_items = True
        self.show_loading_state()

        sql = """
            SELECT
                i.name, i.en_name, i.tier, i.icon_path, i.avg_placement, i.top1_rate, i.play_rate,
                GROUP_CONCAT(DISTINCT icr.category_name) as categories
            FROM items i
            LEFT JOIN item_category_relations icr ON i.name = icr.item_name
            GROUP BY i.name
            ORDER BY i.name -- 初始加载可以不排序，后续处理
        """
        query_key = "all_items_list_with_categories" # 定义缓存键

        execute_query_async(
            sql,
            on_success=self.on_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key
        )

    def on_data_loaded(self, results):
        """数据加载成功后的回调函数"""
        print(f"装备列表数据（含类别）加载成功，共 {len(results)} 条。")
        self.all_item_data = results if results else []
        self._is_loading_items = False # 标记装备数据加载完成
        self.check_all_loaded_and_update_ui() # 尝试更新主UI

    def on_data_load_error(self, error_message, query_key=None):
        """数据加载失败后的回调函数"""
        print(f"装备列表数据加载失败 (查询: {query_key}): {error_message}")
        if query_key == "all_items_list_with_categories":
            self._is_loading_items = False
            print("装备主要数据加载失败。")
        elif query_key == "item_categories_list":
            self._is_loading_categories = False
            print("装备类别数据加载失败。")
        else:
            # 如果是未知的 query_key，为了安全起见，将两个都设为False，以防卡住
            print(f"未知的查询键 {query_key} 加载失败，将所有相关加载状态设为False。")
            self._is_loading_items = False
            self._is_loading_categories = False

        self.check_all_loaded_and_update_ui() # 尝试更新UI，可能会显示错误

    def check_all_loaded_and_update_ui(self):
        """检查所有必需数据是否已加载，如果完成则更新UI或显示错误。"""
        if self.is_loading():
            loading_parts = []
            if self._is_loading_items: loading_parts.append("装备")
            if self._is_loading_categories: loading_parts.append("类别")
            print(f"等待其他数据加载完成: {', '.join(loading_parts)} 未完成...")
            # 更新加载标签文本
            if self.loading_label and self.loading_label.isVisible():
                self.loading_label.setText(f"正在加载 {', '.join(loading_parts)} 数据...")
            return

        print("所有装备列表相关数据已加载完成，准备更新UI。")
        if not self.all_item_data:
            self.display_error("未能加载到装备数据。")
            self.category_label.hide() # 同时隐藏类别筛选，因为没有数据可筛选
        else:
            self.loading_label.hide()    # 隐藏加载提示
            self.scroll_area.show()      # 显示内容区域
            if not self.category_checkboxes or len(self.category_checkboxes) <=1: # 如果类别也加载失败
                print("装备类别数据加载失败或为空，类别筛选不可用。")
                self.category_label.hide()
            else:
                self.category_label.show() # 确保类别筛选区可见
            self.apply_filters_and_sort() # 应用筛选和排序并更新UI

    def show_loading_state(self):
        """显示加载状态，隐藏内容。"""
        if not self.loading_label.isVisible():
            self.loading_label.show()
        if not self.scroll_area.isHidden():
            self.scroll_area.hide()
        if not self.category_label.isHidden() and self._is_loading_categories:
             self.category_label.hide() # 加载类别时也隐藏类别标签

        loading_parts = []
        if self._is_loading_items: loading_parts.append("装备")
        if self._is_loading_categories: loading_parts.append("类别")
        if loading_parts:
            self.loading_label.setText(f"正在加载 {', '.join(loading_parts)} 数据...")
        else: # 应该不会到这里，因为 is_loading() 会先判断
            self.loading_label.setText("正在加载数据...")

    def display_error(self, message):
        """在界面上显示错误信息"""
        self._is_loading_items = False      # 确保加载状态已清除
        self._is_loading_categories = False

        self.scroll_area.hide() # 隐藏主内容区
        self.category_label.hide() # 隐藏类别筛选区

        self.loading_label.setText(message)
        self.loading_label.setWordWrap(True)
        self.loading_label.show()
        print(f"显示错误: {message}")

    def apply_filters_and_sort(self):
        """应用搜索和类别筛选，然后排序并更新UI"""
        if self.is_loading():
            print("apply_filters_and_sort 调用，但数据仍在加载中，跳过。")
            return
        if not hasattr(self, 'search_box'): return # 未初始化

        search_term = self.search_box.text().strip().lower()
        selected_categories = set()
        # 检查 "全部" 复选框是否存在且被选中
        all_selected = self.category_checkboxes.get("全部") and self.category_checkboxes["全部"].isChecked()

        if not all_selected:
            for name, checkbox in self.category_checkboxes.items():
                 if name != "全部" and checkbox.isChecked():
                    selected_categories.add(name)

        print(f"应用筛选: 搜索='{search_term}', 类别={selected_categories if not all_selected else '全部'}")
        # print(f"待筛选数据量: {len(self.all_item_data)}") # Debug

        temp_filtered_data = []
        for item in self.all_item_data:
            # 名称匹配
            name_match = (not search_term) or (search_term in item.get('name', '').lower())

            # 类别匹配
            category_match = False
            item_categories_str = item.get('categories') # 获取数据库返回的类别字符串
            # print(f"  装备: {item.get('name')}, 原始类别字符串: '{item_categories_str}'") # Debug
            item_categories = set()
            if item_categories_str: # 确保字符串不是 None 或空
                # 严格按照逗号分割，并去除可能的空格
                item_categories = set(cat.strip() for cat in item_categories_str.split(',') if cat.strip())
            # print(f"  解析后类别集合: {item_categories}") # Debug


            if all_selected:
                category_match = True # 如果选中"全部"，则类别匹配
            elif selected_categories:
                # 检查选中的类别与装备的类别是否有交集
                if selected_categories.intersection(item_categories):
                     category_match = True
            else:
                # 如果没有选中任何特定类别，并且"全部"也没选中，则不匹配任何项
                # （因为 handle_category_change 会确保至少"全部"是选中的）
                 category_match = False

            # print(f"  名称匹配: {name_match}, 类别匹配: {category_match}") # Debug
            if name_match and category_match:
                temp_filtered_data.append(item)

        self.filtered_item_data = temp_filtered_data
        # print(f"筛选后数据量: {len(self.filtered_item_data)}") # Debug
        self.sort_items() # 应用排序并更新UI

    def sort_items(self):
        """根据当前排序规则对 filtered_item_data 进行排序并更新UI"""
        print(f"开始排序装备: 列={self.current_sort_column}, 顺序={self.current_sort_order}")
        try:
            key_func = lambda x: x.get(self.current_sort_column)
            is_reversed = (self.current_sort_order == Qt.SortOrder.DescendingOrder)
            default_value = float('-inf') if is_reversed else float('inf')

            # 特殊处理评级排序
            if self.current_sort_column == 'tier':
                tier_map = {"S": 0, "A": 1, "B": 2, "C": 3, "D": 4, "DEFAULT": 5}
                key_func = lambda x: tier_map.get(x.get('tier', 'DEFAULT') or 'DEFAULT', 5)
                # --- 修改开始：修正评级排序方向 ---
                # 评级默认降序 (S->D)，对应映射值升序 (0->5)
                # 所以，当 order 是 Descending 时，实际 reverse 应该是 False
                # 当 order 是 Ascending 时 (D->S)，实际 reverse 应该是 True
                is_reversed = (self.current_sort_order == Qt.SortOrder.AscendingOrder)
                default_value = 5 # DEFAULT 排最后
                # --- 修改结束 ---

            # 安全的 key 函数，处理 None 情况
            safe_key_func = lambda x: key_func(x) if key_func(x) is not None else default_value

            self.filtered_item_data.sort(
                key=safe_key_func,
                reverse=is_reversed # 使用修正后的 is_reversed
            )
        except TypeError as e:
            print(f"排序错误: {e}. 列: {self.current_sort_column}. 可能存在混合数据类型。")
            # 回退排序
            try:
                self.filtered_item_data.sort(key=lambda x: x.get('name', ''))
                print("排序错误，已回退到按名称排序。")
            except Exception as fallback_e:
                print(f"回退排序也失败: {fallback_e}")
        except Exception as e:
            print(f"排序时发生未知错误: {e}")

        self.update_ui() # 排序后更新列表

    @Slot(str) # 明确这是一个槽函数，接收字符串参数
    def handle_sort_request(self, column_key):
        """处理表头点击事件，更新排序规则"""
        print(f"收到排序请求: {column_key}")
        if self.current_sort_column == column_key:
            # 如果点击的是当前排序列，切换排序顺序
            self.current_sort_order = Qt.SortOrder.AscendingOrder if self.current_sort_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
        else:
            # 如果点击的是新列，设置新列为排序列，并设置默认排序规则
            self.current_sort_column = column_key
            # 默认排序逻辑，可以根据列调整
            if column_key in ['avg_placement']: # 平均排名越低越好
                 self.current_sort_order = Qt.SortOrder.AscendingOrder
            else: # 其他默认降序 (S > A > B..., 出场率高>低, 登顶率高>低)
                 self.current_sort_order = Qt.SortOrder.DescendingOrder

        # 更新表头样式
        self.update_header_styles()
        # 执行排序
        self.sort_items() # 修改：调用 sort_items 而不是 sort_data

    def update_header_styles(self):
        """更新表头标签的样式以指示当前排序列和顺序"""
        sort_indicator = " ▼" if self.current_sort_order == Qt.SortOrder.DescendingOrder else " ▲"
        # 增大表头字体
        base_style = f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: bold;"
        selected_style = f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: bold;" # 当前排序列高亮

        for key, label in self.header_widgets.items():
            text = label.property("original_text") # 获取原始文本
            if text is None: continue # 安全检查
            if key == self.current_sort_column:
                label.setStyleSheet(selected_style)
                label.setText(text + sort_indicator)
            else:
                label.setStyleSheet(base_style)
                label.setText(text) # 恢复原始文本

    def update_ui(self):
        """根据 self.filtered_item_data 更新UI中的装备列表和表头"""
        if self.is_loading():
            print("update_ui 调用，但数据仍在加载中，跳过UI更新。")
            return

        # 确保加载提示已隐藏，内容区已显示 (除非是错误状态)
        if not self.loading_label.text().startswith("未能加载") and not self.loading_label.text().startswith("加载装备数据失败"):
            self.loading_label.hide()
            self.scroll_area.show()
            self.header_frame.show()  # 显示固定表头
            if self.category_checkboxes and len(self.category_checkboxes) > 1:
                self.category_label.show()
        else:
            # 如果是错误状态，保持loading_label显示错误，隐藏其他
            self.scroll_area.hide()
            self.header_frame.hide()  # 隐藏表头
            self.category_label.hide()
            return

        current_scroll_pos = self.scroll_area.verticalScrollBar().value()

        # 更新固定表头
        self.update_header()

        # 清空滚动区域内容（不再包含表头）
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
            # 也要移除 SpacerItem
            elif item and item.spacerItem():
                 self.content_layout.removeItem(item)

        # --- 添加装备行 ---
        if not self.filtered_item_data:
            status_text = "无装备数据"
            # 更准确地判断状态
            search_term = self.search_box.text().strip()
            all_selected = self.category_checkboxes.get("全部") and self.category_checkboxes["全部"].isChecked()
            has_category_filter = not all_selected

            if not self.all_item_data and not self.loading_label: # 初始数据未加载完成
                 status_text = "正在加载装备数据..."
            elif search_term or has_category_filter: # 有筛选条件但无结果
                 status_text = "未找到匹配的装备"
            # else: # 无筛选条件但 all_item_data 为空 (不太可能，除非数据库为空)
            #      status_text = "数据库中无装备数据"

            no_results_label = QLabel(status_text, alignment=Qt.AlignmentFlag.AlignCenter)
            no_results_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 12px; padding: 20px;")
            self.content_layout.addWidget(no_results_label)
        else:
            for item_detail in self.filtered_item_data:
                item_widget = self.create_item_row_widget(item_data=item_detail)
                if item_widget: # 确保成功创建
                    self.content_layout.addWidget(item_widget)

        # 添加弹性空间, 调整垂直大小策略
        self.content_layout.addSpacerItem(QSpacerItem(20, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

        self.scroll_area.verticalScrollBar().setValue(current_scroll_pos)

    def update_header(self):
        """更新固定表头"""
        # 清空现有表头内容
        while self.header_layout.count():
            item = self.header_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 创建新的表头
        header_widget = self.create_item_row_widget(is_header=True)
        if header_widget:
            self.header_layout.addWidget(header_widget)
            # 更新表头样式以反映当前排序状态
            self.update_header_styles()

    def create_item_row_widget(self, item_data=None, is_header=False):
        """创建装备列表中的一行 (表头或数据行)"""
        # 修改：函数名从 item_detail 改为 item_data
        row_widget = QWidget()
        row_widget.setMinimumHeight(45) # 行高度
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(8, 4, 8, 4) # 行内边距
        row_layout.setSpacing(10) # 列间距

        # --- 列定义和宽度 ---
        # 定义列的 key，用于排序和查找
        col_keys = ['icon', 'name', 'tier', 'play_rate', 'avg_placement', 'top1_rate']
        col_widths = {
            'icon': ICON_SIZE_LARGE,
            'name': -1, # 自动伸展
            'tier': 45,
            'play_rate': 70,
            'avg_placement': 70,
            'top1_rate': 70
        }
        col_alignments = {
            'icon': Qt.AlignmentFlag.AlignCenter,
            'name': Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter,
            'tier': Qt.AlignmentFlag.AlignCenter,
            'play_rate': Qt.AlignmentFlag.AlignCenter,
            'avg_placement': Qt.AlignmentFlag.AlignCenter,
            'top1_rate': Qt.AlignmentFlag.AlignCenter
        }
        col_titles = {
            'icon': '', # 表头图标列无标题
            'name': '装备',
            'tier': '评级',
            'play_rate': '出场率',
            'avg_placement': '平均排名',
            'top1_rate': '登顶率'
        }

        # --- 创建列控件 ---
        self.header_widgets = {} if is_header else self.header_widgets # 表头行重置字典
        col_widgets = {}
        for key in col_keys:
            # 修改：表头图标列完全跳过
            if is_header and key == 'icon':
                continue # 不添加任何控件或占位符到布局

            if is_header: # 其他表头列
                label = ClickableLabel(data=key) # data 设为列的 key
                label.clicked.connect(self.handle_sort_request)
                label.setProperty("original_text", col_titles[key]) # 存储原始文本
                self.header_widgets[key] = label # 存储表头标签引用
            else: # 数据行
                if key == 'icon':
                    label = IconLabel(icon_size=ICON_SIZE_LARGE, icon_type='item', placeholder_text='?')
                else:
                    label = QLabel() # 数据行的标签

            label.setAlignment(col_alignments[key])
            if col_widths[key] > 0:
                label.setFixedWidth(col_widths[key])

            col_widgets[key] = label
            # 修改：只有在 label 实际创建时才添加到布局
            if key != 'icon' or not is_header: # 非表头图标列 或 数据行
                row_layout.addWidget(label, 1 if col_widths[key] == -1 else 0) # 名称列伸展


        # --- 填充内容 ---
        if is_header:
            # 表头文本和样式在 update_header_styles 中处理
            pass # 不在此处设置文本
        elif item_data:
            # 设置整行可点击，并关联数据
            row_widget.setCursor(Qt.CursorShape.PointingHandCursor)
            item_name = item_data.get('name', 'N/A')
            # 使用 lambda 捕获当前的 item_name
            row_widget.mousePressEvent = lambda event, name=item_name: self.item_selected.emit(name) if event.button() == Qt.MouseButton.LeftButton else None
            # 添加悬停效果
            row_widget.enterEvent = lambda event, w=row_widget: w.setStyleSheet(f"background-color: {ITEM_ROW_BG_HOVER}; border-radius: 4px;")
            row_widget.leaveEvent = lambda event, w=row_widget: w.setStyleSheet("background-color: transparent;")


            # 图标 (数据行)
            icon_path = item_data.get('icon_path')
            if 'icon' in col_widgets: # 确保控件存在
                col_widgets['icon'].set_icon(icon_path, item_name)

            # 名称 (数据行不再使用 ClickableLabel, 整行可点)
            if 'name' in col_widgets:
                col_widgets['name'].setText(item_name)
                # 增大名称字体
                col_widgets['name'].setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px;")

            # 评级
            if 'tier' in col_widgets:
                tier = item_data.get('tier', '').upper() if item_data.get('tier') else ''
                tier_color = TIER_COLORS.get(tier, TIER_COLORS['DEFAULT'])
                col_widgets['tier'].setText(tier if tier else "-")
                # 增大评级字体
                col_widgets['tier'].setStyleSheet(f"""
                    QLabel {{
                        background-color: {tier_color};
                        color: {TEXT_COLOR_DARK}; /* 深色文字 */
                        font-size: {FONT_SIZE_SMALL}px; /* 评级字体可以稍小或用 FONT_SIZE_NORMAL */
                        font-weight: bold;
                        border-radius: 4px;
                        padding: 3px 5px; /* 上下 3px，左右 5px */
                        min-width: 25px; /* 保证最小宽度 */
                    }}
                """)

            # 其他统计数据
            # 增大统计数据字体
            value_style = f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px;"
            if 'play_rate' in col_widgets:
                play_rate = item_data.get('play_rate')
                if play_rate is not None:
                     # 判断数据范围
                    if play_rate > 1:
                        col_widgets['play_rate'].setText(f"{play_rate:.1f}%")
                    else:
                        col_widgets['play_rate'].setText(f"{play_rate:.1%}")
                else:
                    col_widgets['play_rate'].setText("--")
                col_widgets['play_rate'].setStyleSheet(value_style)

            if 'avg_placement' in col_widgets:
                avg_placement = item_data.get('avg_placement')
                col_widgets['avg_placement'].setText(f"{avg_placement:.2f}" if avg_placement is not None else "--")
                col_widgets['avg_placement'].setStyleSheet(value_style)

            if 'top1_rate' in col_widgets:
                top1_rate = item_data.get('top1_rate')
                col_widgets['top1_rate'].setText(f"{top1_rate:.1f}%" if top1_rate is not None else "--")
                col_widgets['top1_rate'].setStyleSheet(value_style)

        # 如果是空表头行 (仅在 very rare case 下可能发生，但做个检查)
        if is_header and not self.header_widgets:
             return None

        return row_widget

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置装备列表视图到初始状态")
        
        # 重置搜索框
        if hasattr(self, 'search_box') and self.search_box:
            self.search_box.clear()
        
        # 重置类别筛选为"全部"选中状态
        if hasattr(self, 'category_checkboxes') and self.category_checkboxes:
            self._updating_checkboxes = True  # 防止信号循环
            try:
                # 选中"全部"，取消其他选项
                if "全部" in self.category_checkboxes:
                    self.category_checkboxes["全部"].setChecked(True)
                
                for name, checkbox in self.category_checkboxes.items():
                    if name != "全部":
                        checkbox.setChecked(False)
            finally:
                self._updating_checkboxes = False
        
        # 重置排序状态为默认
        self.current_sort_column = 'play_rate'
        self.current_sort_order = Qt.SortOrder.DescendingOrder
        
        # 重置滚动位置（如果滚动区域可见）
        if hasattr(self, 'scroll_area') and self.scroll_area and self.scroll_area.isVisible():
            self.scroll_area.verticalScrollBar().setValue(0)
        
        # 重新应用筛选和排序（如果数据已加载）
        if hasattr(self, 'all_item_data') and self.all_item_data and not self.is_loading():
            self.apply_filters_and_sort()