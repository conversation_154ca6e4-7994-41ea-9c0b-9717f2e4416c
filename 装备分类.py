#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import asyncio
import json
import random
import time
import re
import traceback
from pathlib import Path
from urllib.parse import urlparse, urljoin
import aiohttp
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, <PERSON><PERSON><PERSON> as PlaywrightError
from typing import Dict, Any, Optional, List, Tuple

# --- 常量 ---
EQUIPMENT_URL = "https://www.metatft.com/items"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
BROWSER_ARGS = [
    "--disable-gpu", "--disable-dev-shm-usage", "--no-sandbox",
    "--disable-setuid-sandbox", "--disable-accelerated-2d-canvas",
    "--disable-infobars", "--disable-extensions", "--block-new-web-contents",
    "--no-default-browser-check", "--js-flags=--max-old-space-size=512",
    f"--user-agent={USER_AGENT}"
]
# 超时设置
NAV_TIMEOUT = 60000  # ms (导航超时)
PAGE_TIMEOUT = 45000 # ms (页面操作超时)
SELECTOR_TIMEOUT = 25000 # ms (选择器等待超时)
# 滚动后等待动态内容加载的时间 (毫秒)
ROW_LOAD_DELAY_MS = 150

class EquipmentClassifier:
    """
    装备分类工具，支持用户手动选择装备类型并爬取分类数据
    """

    def __init__(self, base_dir: Optional[str] = None, max_concurrent_downloads: int = 5,
                 min_delay: float = 0.1, max_delay: float = 0.3, download_icons: bool = True):
        """
        初始化分类工具
        """
        self.base_dir = Path(base_dir or Path(__file__).parent.resolve())
        self.equip_icons_dir = self.base_dir / "装备图标"
        self.mapping_dir = self.base_dir / "映射文件"
        self.classification_file_path = self.mapping_dir / "装备分类映射.json"

        self.equip_icons_dir.mkdir(parents=True, exist_ok=True)
        self.mapping_dir.mkdir(parents=True, exist_ok=True)

        self.max_concurrent_downloads = max_concurrent_downloads
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.download_icons = download_icons
        self.equipment_url = EQUIPMENT_URL

        # 分类数据结构
        self.equipment_categories: Dict[str, List[Dict[str, Any]]] = {}
        # 保存已处理过的装备，避免重复
        self.processed_equipment: Dict[str, Dict[str, Any]] = {}
        
        self.error_count = 0
        self.max_errors = 20
        
        self._load_existing_classifications()

    def _load_existing_classifications(self):
        """如果文件存在，加载现有的装备分类数据"""
        if self.classification_file_path.exists():
            try:
                with open(self.classification_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                loaded_categories = data.get('categories', {})
                loaded_equipment = data.get('processed_equipment', {})
                
                if isinstance(loaded_categories, dict):
                    self.equipment_categories = loaded_categories
                    print(f"成功加载现有装备分类数据: {len(self.equipment_categories)} 个分类")
                else:
                    print(f"警告: 装备分类文件中的 'categories' 不是有效的字典，将重新创建。")
                    self.equipment_categories = {}
                
                if isinstance(loaded_equipment, dict):
                    self.processed_equipment = loaded_equipment
                    print(f"成功加载现有处理过的装备数据: {len(self.processed_equipment)} 个装备")
                else:
                    print(f"警告: 装备分类文件中的 'processed_equipment' 不是有效的字典，将重新创建。")
                    self.processed_equipment = {}
            except json.JSONDecodeError:
                print(f"警告: 装备分类文件 {self.classification_file_path} 格式错误，将重新创建。")
                self.equipment_categories = {}
                self.processed_equipment = {}
            except Exception as e:
                print(f"加载装备分类文件失败: {e}")
                self.equipment_categories = {}
                self.processed_equipment = {}
        else:
             print("装备分类文件不存在，将创建新的映射。")
             self.equipment_categories = {}
             self.processed_equipment = {}

    async def _add_random_delay(self):
        """添加随机延迟"""
        await asyncio.sleep(random.uniform(self.min_delay, self.max_delay))

    def _extract_equipment_name_from_url(self, url: str) -> Optional[str]:
        """从图标 URL 中提取英文装备名称"""
        if not url: return None
        match = re.search(r'/([^/]+)\.png(?:\?|$)|\/items\/([^/]+)\.png', url)
        if match:
            name = next((group for group in match.groups() if group), None)
            if name:
                name = re.sub(r'^TFT\d+_Item_', '', name)
                name = re.sub(r'^TFT\d+_SupportItem_', '', name)
            return name
        print(f"警告: 无法从URL提取装备名称: {url}")
        return None

    def _sanitize_filename(self, name: str) -> str:
        """清理字符串以用作文件名"""
        sanitized = re.sub(r'[\\/*?:"<>|]', '_', name)
        sanitized = re.sub(r'_+', '_', sanitized)
        sanitized = sanitized.strip('_. ')
        return sanitized if sanitized else "unknown_equipment"

    async def _download_image(self, session: aiohttp.ClientSession, url: str, file_path: Path) -> bool:
        """使用提供的会话下载单个图像"""
        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=20)) as response:
                if response.status == 200:
                    content = await response.read()
                    file_path.parent.mkdir(parents=True, exist_ok=True)
                    with open(file_path, 'wb') as f: f.write(content)
                    return True
                else:
                    print(f"下载失败 (状态 {response.status}): {url}")
                    return False
        except asyncio.TimeoutError: print(f"下载超时: {url}"); return False
        except aiohttp.ClientError as e: print(f"下载时发生客户端错误: {url}, 错误: {e}"); return False
        except Exception as e: print(f"下载图片时发生未知错误: {url}, 错误: {e}"); return False

    async def _extract_data_row_by_row(self, page) -> List[Dict[str, Any]]:
        """
        逐行滚动并提取数据
        核心逻辑：获取所有行 -> 遍历行 -> 滚动到行 -> 等待 -> 提取行内数据 -> 收集结果
        """
        print("准备逐行提取当前页面的装备数据...")
        results = []
        table_body_selector = "#content-wrap table tbody"
        row_selector = f"{table_body_selector} tr"

        try:
            # 1. 获取所有行的 ElementHandles
            print("正在定位所有表格行...")
            row_elements = await page.locator(row_selector).all()
            total_rows = len(row_elements)
            print(f"找到 {total_rows} 行。开始逐行处理...")

            if total_rows == 0:
                print("警告：未在表格中找到任何行。")
                return []

            # 2. 遍历每一行
            for i, row in enumerate(row_elements):
                item_data = {
                    "name": "", "icon_url": "", "tier": "",
                    "avg_placement": "", "top_1_rate": ""
                }
                row_processed = False
                try:
                    # 3. 滚动到当前行使其可见
                    await row.scroll_into_view_if_needed(timeout=5000) # 增加滚动超时

                    # 4. 等待一小段时间，让可能的动态内容加载
                    await page.wait_for_timeout(ROW_LOAD_DELAY_MS)

                    # 5. 在当前行的上下文中提取数据
                    # --- 图标和名称 (第 1 列) ---
                    img_element = row.locator('td:nth-child(1) img')
                    if await img_element.count() > 0:
                        item_data["name"] = await img_element.get_attribute('alt', timeout=1000) or ""
                        item_data["icon_url"] = await img_element.get_attribute('src', timeout=1000) or ""
                        item_data["name"] = item_data["name"].strip()
                        item_data["icon_url"] = item_data["icon_url"].strip()
                    else:
                        continue # 跳过没有图像的行

                    if not item_data["name"] or not item_data["icon_url"]:
                        continue # 跳过基本数据不全的行

                    # --- 评级 (第 2 列) ---
                    tier_element = row.locator('td:nth-child(2) > div')
                    if await tier_element.count() > 0:
                        # 尝试多种方式获取评级 (直接文本, 类名, 子元素)
                        tier_text = (await tier_element.text_content(timeout=1000) or "").strip().upper()
                        if tier_text in ['S', 'A', 'B', 'C', 'D']:
                            item_data["tier"] = tier_text
                        else:
                            class_name = await tier_element.get_attribute('class', timeout=1000) or ""
                            if 'Badge_S' in class_name: item_data["tier"] = 'S'
                            elif 'Badge_A' in class_name: item_data["tier"] = 'A'
                            elif 'Badge_B' in class_name: item_data["tier"] = 'B'
                            elif 'Badge_C' in class_name: item_data["tier"] = 'C'
                            elif 'Badge_D' in class_name: item_data["tier"] = 'D'
                            else: # 尝试查找子 span
                                tier_span = tier_element.locator('span')
                                if await tier_span.count() > 0:
                                    span_text = (await tier_span.text_content(timeout=1000) or "").strip().upper()
                                    if span_text in ['S', 'A', 'B', 'C', 'D']:
                                        item_data["tier"] = span_text
                    # 如果没找到，tier 保持 ""

                    # --- 平均排名 (第 3 列) ---
                    placement_element = row.locator('td:nth-child(3) > div')
                    if await placement_element.count() > 0:
                        item_data["avg_placement"] = (await placement_element.text_content(timeout=1000) or "").strip()

                    # --- 登顶率 (第 5 列) ---
                    top_rate_element = row.locator('td:nth-child(5) > div')
                    if await top_rate_element.count() > 0:
                        rate_text = (await top_rate_element.text_content(timeout=1000) or "").strip()
                        item_data["top_1_rate"] = rate_text.replace('%', '').strip()

                    # --- 新增：出场率 (第 6 列) ---
                    play_rate_element = row.locator('td:nth-child(6) > div')
                    if await play_rate_element.count() > 0:
                        play_rate_text = (await play_rate_element.text_content(timeout=1000) or "").strip()
                        #  我们在这里直接保存包含百分号的原始文本，或者处理掉它
                        item_data["play_rate"] = play_rate_text.replace('%', '').strip()
                    else:
                        item_data["play_rate"] = "" # 确保字段存在

                    results.append(item_data)
                    row_processed = True

                    # 打印进度 (可选, 每处理 N 行打印一次)
                    if (i + 1) % 20 == 0 or (i + 1) == total_rows:
                         print(f"  已处理 {i + 1}/{total_rows} 行...")

                except PlaywrightTimeoutError as e:
                    print(f"  处理行 {i+1} 时发生超时错误: {e}")
                    self.error_count += 1
                except PlaywrightError as e: # 更通用的 Playwright 错误
                    print(f"  处理行 {i+1} 时发生 Playwright 错误: {e}")
                    self.error_count += 1
                except Exception as e:
                    print(f"  处理行 {i+1} 时发生未知错误: {e}")
                    self.error_count += 1

                if not row_processed:
                    print(f"  未能成功处理行 {i+1} 的数据。")

                if self.error_count >= self.max_errors:
                    print(f"处理行错误达到上限 ({self.max_errors})，停止提取。")
                    break # 停止处理后续行

        except PlaywrightTimeoutError:
            print(f"错误: 等待表格行 '{row_selector}' 超时。")
            await page.screenshot(path=self.base_dir / "equipment_error_screenshot_row_find_timeout.png", full_page=True)
            print("已保存错误截图。")
            return []
        except Exception as e:
            print(f"提取数据时发生意外错误: {e}")
            print(traceback.format_exc())
            try:
                await page.screenshot(path=self.base_dir / "equipment_error_screenshot_extract_general.png", full_page=True)
                print("已保存错误截图。")
            except Exception as se:
                 print(f"保存错误截图失败: {se}")
            return []

        print(f"逐行提取完成。共提取 {len(results)} 条数据。发生 {self.error_count} 次行处理错误。")
        return results

    def _process_extracted_data(self, extracted_data: List[Dict[str, Any]], category_name: str, overwrite: bool = False) -> None:
        """
        处理从页面提取的原始数据并分类,不再返回下载队列
        :param extracted_data: 页面提取的原始数据
        :param category_name: 用户指定的分类名称
        :param overwrite: 是否覆盖已有的同名分类
        """
        print(f"开始处理 {len(extracted_data)} 条提取到的数据，分类为：{category_name}...")
        processed_count = 0
        skipped_count = 0
        added_to_category_count = 0

        # 如果是覆盖模式，需要先清理旧数据
        if overwrite and category_name in self.equipment_categories:
            print(f"覆盖模式开启：将清理并重建分类 '{category_name}'。")
            
            # 1. 从 `processed_equipment` 中移除该分类下的所有旧装备
            # 使用列表推导式以避免在迭代时修改字典
            item_names_to_remove = [item.get("name") for item in self.equipment_categories[category_name] if item.get("name")]
            
            removed_count = 0
            for name in item_names_to_remove:
                if name in self.processed_equipment:
                    del self.processed_equipment[name]
                    removed_count += 1
            
            if removed_count > 0:
                print(f"已从全局装备列表(processed_equipment)中移除 {removed_count} 个属于'{category_name}'的旧装备。")

            # 2. 清空该分类的列表
            self.equipment_categories[category_name] = []
        
        # 确保分类列表存在（用于新分类）
        if category_name not in self.equipment_categories:
            self.equipment_categories[category_name] = []

        for item_data in extracted_data:
            try:
                name = item_data.get("name")
                icon_url = item_data.get("icon_url")
                tier = item_data.get("tier", "")
                avg_placement = item_data.get("avg_placement", "")
                top_1_rate = item_data.get("top_1_rate", "")
                play_rate = item_data.get("play_rate", "")

                if not name or not icon_url:
                    skipped_count += 1
                    continue

                if not urlparse(icon_url).scheme:
                    icon_url = urljoin(self.equipment_url, icon_url)

                en_name = self._extract_equipment_name_from_url(icon_url)
                sanitized_name = self._sanitize_filename(name)
                file_name = f"{sanitized_name}.png"
                file_path = self.equip_icons_dir / file_name
                relative_path = Path(os.path.relpath(file_path, self.base_dir)).as_posix()

                # 准备装备数据
                equipment_info = {
                    "name": name,
                    "en_name": en_name,
                    "icon_url": icon_url,
                    "icon_path": relative_path,
                    "tier": tier,
                    "avg_placement": avg_placement,
                    "top_1_rate": top_1_rate,
                    "play_rate": play_rate
                }
                
                # 保存到已处理装备字典
                self.processed_equipment[name] = equipment_info
                
                # 新增：准备一个只包含核心识别信息的"纯粹"条目用于分类
                pure_category_entry = {
                    "name": name,
                    "en_name": en_name
                }
                
                # 添加到当前分类中（如果不存在），只添加纯粹的条目
                category_items = self.equipment_categories[category_name]
                if not any(item["name"] == name for item in category_items):
                    self.equipment_categories[category_name].append(pure_category_entry)
                    added_to_category_count += 1

                processed_count += 1

            except Exception as e:
                print(f"处理数据项时出错: {item_data}, 错误: {e}")
                skipped_count += 1 # 将处理错误也视为跳过

        print(f"数据处理完成。有效装备: {processed_count}, 添加到分类'{category_name}': {added_to_category_count}, 跳过/处理错误: {skipped_count}")

    async def _save_screenshot(self, page, filename: str):
        """尝试保存页面截图"""
        try:
            filepath = self.base_dir / filename
            await page.screenshot(path=filepath, full_page=True)
            print(f"已保存错误截图到: {filepath}")
        except Exception as screenshot_error:
            print(f"保存错误截图失败: {screenshot_error}")

    async def save_classification(self):
        """将分类数据保存到 JSON 文件"""
        print(f"准备保存装备分类数据到: {self.classification_file_path}")
        try:
            final_data_to_save = {
                "categories": self.equipment_categories,
                "processed_equipment": self.processed_equipment
            }
            with open(self.classification_file_path, 'w', encoding='utf-8') as f:
                json.dump(final_data_to_save, f, ensure_ascii=False, indent=2)
            
            # 打印分类统计信息
            print(f"成功保存分类数据。分类总数: {len(self.equipment_categories)}")
            for category, items in self.equipment_categories.items():
                print(f"  - {category}: {len(items)} 个装备")
            
            print(f"已处理装备总数: {len(self.processed_equipment)}")
            return True
        except Exception as e:
            print(f"保存装备分类数据时出错: {e}")
            print(traceback.format_exc())
            return False

    async def run_interactive_classification(self):
        """
        运行交互式分类，用户手动选择装备类型并输入分类名称
        """
        print("开始交互式装备分类，请在浏览器中手动选择装备类型...")

        async with async_playwright() as playwright:
            browser = None
            context = None
            page = None
            try:
                print("启动浏览器...")
                # 使用有头模式
                browser = await playwright.chromium.launch(
                    headless=False, args=BROWSER_ARGS, slow_mo=50
                )
                print("创建浏览器上下文...")
                context = await browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    locale='zh-CN', timezone_id='Asia/Shanghai',
                    bypass_csp=True, user_agent=USER_AGENT,
                    java_script_enabled=True
                )
                context.set_default_navigation_timeout(NAV_TIMEOUT)
                context.set_default_timeout(PAGE_TIMEOUT)
                page = await context.new_page()

                print(f"导航到: {self.equipment_url}")
                await page.goto(self.equipment_url, wait_until="domcontentloaded", timeout=NAV_TIMEOUT)
                print("页面初步 DOM 加载完成。")

                # 设置中文语言
                print("尝试将语言设置为中文...")
                try:
                    await page.evaluate("localStorage.setItem('language', 'zh_cn');")
                    print("已设置 localStorage 中的 language 为 zh_cn")
                    print("刷新页面以应用语言设置...")
                    await page.reload(wait_until="domcontentloaded", timeout=NAV_TIMEOUT)
                    print("页面已刷新。")
                    current_language = await page.evaluate("localStorage.getItem('language');")
                    print(f"当前 localStorage 语言设置: {current_language}")
                    print("等待页面稳定...")
                    await asyncio.sleep(3) # 等待 JS 执行和渲染
                except Exception as e:
                    print(f"设置中文语言时出错: {e}")
                
                # 等待网络稳定
                try:
                    print("等待网络稳定...")
                    await page.wait_for_load_state("networkidle", timeout=20000)
                    print("网络已稳定。")
                except Exception as e:
                    print(f"等待网络稳定时出错: {e}, 继续执行...")

                # 提示用户操作
                print("\n" + "="*80)
                print("浏览器已启动，请按照以下步骤操作：")
                print("1. 在浏览器中手动选择装备类型（例如点击筛选器等）")
                print("2. 当页面显示所需装备后，在控制台输入该装备分类的名称并按Enter")
                print("3. 如果输入的分类已存在，系统会提示您是否覆盖")
                print("4. 重复此过程，直到完成所有分类")
                print("5. 输入 'exit' 或 'quit' 结束程序")
                print("="*80 + "\n")

                # 新增：显示已存在的分类
                if self.equipment_categories:
                    print("当前已存在的分类:")
                    for cat_name in self.equipment_categories.keys():
                        print(f"  - {cat_name}")
                    print("-" * 20)
                else:
                    print("当前没有已存在的分类。")


                download_queue = []
                
                # 循环等待用户输入
                while True:
                    # 提示用户输入类别名称
                    category_name = input("\n请输入要新增或更新的装备分类名称（或输入'exit'/'quit'退出）：").strip()
                    
                    # 检查是否退出
                    if category_name.lower() in ['exit', 'quit']:
                        print("用户请求退出，正在结束程序...")
                        break
                    
                    if not category_name:
                        print("分类名称不能为空，请重新输入。")
                        continue

                    # 检查分类是否存在，并决定是否覆盖
                    overwrite_category = False
                    if category_name in self.equipment_categories:
                        confirm_overwrite = input(f"分类 '{category_name}' 已存在。是否要重新爬取并覆盖它？ (y/n): ").lower()
                        if confirm_overwrite == 'y':
                            overwrite_category = True
                            print(f"好的，稍后将覆盖分类 '{category_name}'。")
                        else:
                            print("操作取消。")
                            continue
                    
                    # 确认用户已经在页面上选择了相应的装备类型
                    confirm = input(f"请在浏览器中完成 '{category_name}' 的筛选，然后按Enter键继续爬取... (输入 'c' 可取消本次操作): ").lower()
                    if confirm == 'c':
                        print("已取消当前操作。")
                        continue
                    
                    # 提取当前页面装备数据
                    print(f"开始提取'{category_name}'类别的装备数据...")
                    extracted_data = await self._extract_data_row_by_row(page)
                    
                    if not extracted_data:
                        print(f"警告: 未从当前页面提取到任何装备数据，请检查页面是否正确显示装备。")
                        continue
                    
                    # 处理数据并归类
                    self._process_extracted_data(extracted_data, category_name, overwrite=overwrite_category)
                    
                    # 保存当前进度
                    print("保存当前分类进度...")
                    await self.save_classification()
                    
                    print(f"'{category_name}'类别的数据已成功保存。")
                
                # 最终保存
                await self.save_classification()

            except PlaywrightTimeoutError as e:
                print(f"Playwright 操作超时: {e}")
                print(traceback.format_exc())
                if page: await self._save_screenshot(page, "equipment_timeout_error.png")
            except Exception as e:
                print(f"爬取过程中发生严重错误: {e}")
                print(traceback.format_exc())
                if page: await self._save_screenshot(page, "equipment_general_error.png")
            finally:
                print("关闭浏览器...")
                if page: await page.close()
                if context: await context.close()
                if browser: await browser.close()
                print("浏览器已关闭。")

    async def run_full_scrape_mode(self):
        """
        运行全量数据更新模式。
        此模式会静默运行，爬取所有装备的最新数据，并与现有分类进行合并。
        """
        print("开始全量数据更新...")
        
        if not self.equipment_categories:
            print("错误：装备分类数据为空。无法执行全量更新，因为没有基准分类。")
            print("请先至少运行一次交互模式来创建分类。")
            return

        async with async_playwright() as playwright:
            browser = None
            try:
                print("启动无头浏览器...")
                browser = await playwright.chromium.launch(headless=True, args=BROWSER_ARGS)
                context = await browser.new_context(
                    locale='zh-CN', user_agent=USER_AGENT, java_script_enabled=True
                )
                page = await context.new_page()

                print(f"导航到装备页面: {self.equipment_url}")
                await page.goto(self.equipment_url, wait_until="domcontentloaded", timeout=NAV_TIMEOUT)

                print("尝试设置语言并刷新...")
                try:
                    await page.evaluate("localStorage.setItem('language', 'zh_cn');")
                    await page.reload(wait_until="domcontentloaded", timeout=NAV_TIMEOUT)
                    await asyncio.sleep(2) # 等待JS渲染
                except Exception as e:
                    print(f"设置语言时出错 (可忽略): {e}")

                print("等待网络稳定...")
                try:
                    await page.wait_for_load_state("networkidle", timeout=30000)
                except Exception as e:
                    print(f"等待网络稳定超时 (可忽略): {e}, 继续执行...")

                print("开始从页面提取所有装备数据...")
                all_extracted_data = await self._extract_data_row_by_row(page)
                
                if not all_extracted_data:
                    print("错误：未能从页面提取到任何数据。更新中止。")
                    return
                
                print(f"成功从页面提取 {len(all_extracted_data)} 条原始数据。")
                print("开始整理新数据并与现有分类进行合并...")

                # 将新数据转换为字典以便快速查找
                new_data_map = {item['name']: item for item in all_extracted_data}
                
                # 创建一个新的装备详情字典
                new_processed_equipment = {}
                
                # 遍历我们已有的分类结构
                for category_name, items_in_category in self.equipment_categories.items():
                    print(f"  -> 正在处理分类: {category_name}")
                    for item_ref in items_in_category:
                        item_name = item_ref['name']
                        
                        # 如果装备在新的数据中被找到，则更新它
                        if item_name in new_data_map:
                            fresh_item_data = new_data_map[item_name]
                            
                            # 使用标准处理流程来生成完整的装备信息
                            # 这里会处理 en_name, icon_path 等
                            en_name = self._extract_equipment_name_from_url(fresh_item_data.get("icon_url", ""))
                            sanitized_name = self._sanitize_filename(item_name)
                            file_name = f"{sanitized_name}.png"
                            relative_path = Path(os.path.relpath(self.equip_icons_dir / file_name, self.base_dir)).as_posix()

                            equipment_info = {
                                "name": item_name,
                                "en_name": en_name,
                                "icon_url": fresh_item_data.get("icon_url"),
                                "icon_path": relative_path,
                                "tier": fresh_item_data.get("tier", ""),
                                "avg_placement": fresh_item_data.get("avg_placement", ""),
                                "top_1_rate": fresh_item_data.get("top_1_rate", ""),
                                "play_rate": fresh_item_data.get("play_rate", "")
                            }
                            new_processed_equipment[item_name] = equipment_info
                            
                            # 更新分类定义中的 en_name，以防变更
                            item_ref['en_name'] = en_name
                        else:
                            # 关键修改：如果装备在分类中定义，但在网站上未找到，则不再从旧数据中保留。
                            # 这样可以确保任何从分类中删除的装备，或在网站上不存在的装备，都会从最终结果中消失。
                            print(f"警告: 装备 '{item_name}' 在您的分类 '{category_name}' 中有定义，但在当前网站数据中未找到。它将不会被包含在本次更新的结果中。")

                # 用全新的、经过整理的装备详情字典替换旧的
                self.processed_equipment = new_processed_equipment
                
                print("数据合并完成。")
                await self.save_classification()

            except Exception as e:
                print(f"在全量更新模式下发生严重错误: {e}")
                print(traceback.format_exc())
            finally:
                if browser:
                    await browser.close()
                print("无头浏览器已关闭。")

# --- 主函数 ---
async def main():
    import argparse
    parser = argparse.ArgumentParser(description='云顶之弈装备分类与数据更新工具')
    parser.add_argument(
        '--mode', 
        type=str, 
        default='interactive', 
        choices=['interactive', 'full'],
        help="运行模式: 'interactive' (交互式分类) 或 'full' (全量数据更新)"
    )
    parser.add_argument('--dir', type=str, default=None, help='指定基础目录')
    # 注意：在全量更新模式下，--no-icons 通常是首选，因为图标下载由其他脚本处理
    parser.add_argument('--no-icons', action='store_true', help='不下载装备图标')
    args = parser.parse_args()
    
    start_time = time.time()
    
    # 创建分类器
    classifier = EquipmentClassifier(
        base_dir=args.dir, 
        download_icons=not args.no_icons
    )
    
    # 根据模式选择执行的函数
    if args.mode == 'interactive':
        print("--- 进入交互式分类模式 ---")
        await classifier.run_interactive_classification()
    elif args.mode == 'full':
        print("--- 进入全量数据更新模式 ---")
        # 此处将来会调用新的全量更新方法
        await classifier.run_full_scrape_mode()

    end_time = time.time()
    duration = end_time - start_time
    
    print(f"操作完成。总耗时: {duration:.2f} 秒")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行时发生错误: {e}")
        print(traceback.format_exc()) 