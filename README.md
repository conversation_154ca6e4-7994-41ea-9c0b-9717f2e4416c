# 云顶之弈助手 (TFT Helper)

## 简介

云顶之弈助手是一个基于 Python 和 PySide6 开发的桌面应用程序，旨在为《英雄联盟》云顶之弈模式的玩家提供全面的游戏数据查询与辅助功能。用户可以通过该助手快速查询阵容推荐、英雄信息、装备合成、海克斯效果等，帮助玩家更好地理解游戏，提升游戏水平。

## 主要功能

*   **阵容列表与详情**：展示热门阵容搭配，包括英雄站位、核心英雄、装备推荐、羁绊效果等。
*   **英雄列表与详情**：提供所有英雄的详细信息，包括技能、属性、推荐装备、所属羁绊等。
*   **装备列表与详情**：展示所有装备的合成路径、属性效果以及适用英雄。
*   **海克斯列表**：列出所有海克斯强化的效果描述。
*   **数据可视化**：通过图标和清晰的布局展示数据，方便用户快速获取信息。
*   **窗口控制**：支持窗口拖动、展开/收起等便捷操作。
*   **数据本地化**：游戏数据存储在本地 SQLite 数据库中，方便快速查询。

## 技术栈

*   **编程语言**：Python
*   **GUI框架**：PySide6 (Qt for Python)
*   **数据库**：SQLite
*   **数据来源**：通过爬虫脚本（`GUI总爬取程序.py`）从网络获取数据。

## 目录结构说明

```
.
├── 主窗口.py                # 应用程序主窗口逻辑
├── 数据库建立.py            # 用于创建和初始化本地数据库的脚本
├── 数据库操作.py            # 封装数据库查询等操作的模块
├── 常量与配置.py            # 定义项目中使用的常量和配置信息
├── 自定义组件.py            # 包含自定义的UI组件
├── GUI总爬取程序.py         # 用于爬取云顶之弈相关数据的脚本
├── 视图模块/                # 存放各个功能界面的视图逻辑
│   ├── 阵容列表视图.py
│   ├── 英雄列表视图.py
│   ├── 装备列表视图.py
│   ├── 海克斯列表视图.py
│   ├── 阵容详情视图.py
│   ├── 英雄详情视图.py
│   └── 装备详情视图.py
├── 英雄.py                  # 可能与英雄数据处理或定义相关
├── 装备.py                  # 可能与装备数据处理或定义相关
├── 阵容.py                  # 可能与阵容数据处理或定义相关
├── 海克斯.py                # 可能与海克斯数据处理或定义相关
├── 羁绊.py                  # 可能与羁绊数据处理或定义相关
├── 高清图标下载.py          # 用于下载高清游戏图标的脚本
├── tft_data.db              # SQLite数据库文件，存储游戏数据
├── 英雄图标/                # 存放英雄图标
├── 装备图标/                # 存放装备图标
├── 羁绊图标/                # 存放羁绊图标
├── 阵容数据/                # 可能存放阵容相关原始数据
├── 英雄装备数据/            # 可能存放英雄装备相关原始数据
├── 海克斯数据/              # 可能存放海克斯相关原始数据
├── 映射文件/                # 可能存放一些数据映射文件
├── __pycache__/             # Python缓存目录
├── .git/                    # Git版本控制目录
└── .gitignore               # Git忽略配置文件
```

## 如何运行

1.  **环境准备**：
    *   确保已安装 Python 环境。
    *   安装必要的 Python 包：`pip install PySide6` (以及爬虫脚本可能需要的其他库，如 `requests`, `beautifulsoup4` 等，请根据实际情况补充)。

2.  **数据初始化**：
    *   首先运行 `数据库建立.py` 脚本，该脚本会创建 `tft_data.db` 数据库文件，并可能包含初始数据的填充逻辑（或者需要先运行 `GUI总爬取程序.py` 来获取数据）。
    *   如果图标是动态下载的，可能需要运行 `高清图标下载.py`。

3.  **运行主程序**：
    *   执行 `主窗口.py` 文件即可启动应用程序：`python 主窗口.py`

## 注意事项

*   首次运行前，务必确保 `tft_data.db` 数据库文件已通过 `数据库建立.py` 或相关爬虫脚本成功创建和填充。
*   如果遇到图标不显示等问题，请检查图标路径配置是否正确，以及图标文件是否存在于对应目录。
*   网络数据源可能会发生变化，爬虫脚本 (`GUI总爬取程序.py`) 可能需要定期维护以保证数据获取的准确性。

---
*该 README.md 由 AI 辅助生成，请根据项目实际情况进行调整和完善。* 