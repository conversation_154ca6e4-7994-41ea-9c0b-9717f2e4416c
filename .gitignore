# 忽略所有文件
*
# 不忽略所有目录，这样可以递归到子目录
!*/
# 不忽略所有.py文件
!*.py
# 不忽略所有.md文件
!*.md
# 不忽略filtered_equipment.json文件
!/数据库生成与更新/数据库生成/filtered_equipment.json
# 不忽略.gitignore文件本身
!.gitignore
!*.bat
!*.iss
# 忽略PyInstaller打包生成的文件
/dist/
/build/
!*.spec
!requirements.txt
!update_config.json
!hash_bases.json
# TF
T常驻助手项目相关
# 不忽略前端开发文件
!*.vue
!*.ts
!*.js
!*.json
!*.html
!*.css
!*.scss
!*.toml
!*.rs
# 前端依赖和构建产物
TFT常驻助手/node_modules/
TFT常驻助手/dist/
TFT常驻助手/src-tauri/target/
TFT常驻助手/.vite/

# 数据文件
阵容数据/
英雄装备数据/
海克斯数据/
映射文件/
modules/tesseract/