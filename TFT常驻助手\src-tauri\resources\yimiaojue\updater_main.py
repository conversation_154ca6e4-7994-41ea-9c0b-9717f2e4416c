# -*- coding: utf-8 -*-
"""
独立的更新器/启动器程序 (Updater/Launcher) v3.0

本程序是用户实际启动的应用入口。
它的职责是：
1. 检查应用本身是否有新版本。
2. 如果有，则自动下载、解压并替换旧文件（支持目录结构）。
3. 如果没有，则直接启动主程序。
4. 启动主程序后，本程序即退出。

[v3.0 重要变更]
- 支持目录结构的更新（onedir打包）
- 解决文件占用问题（延迟批处理替换）
- 解决DLL缺失问题（完整目录复制）
"""
import os
import sys
import time
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import zipfile
import requests
import subprocess
import json
from packaging.version import parse as parse_version
import hashlib
import shutil
import traceback

# 假设 updater.py 和主程序在同一目录下
import updater

# --- 全局配置 ---
# [修复] 使用基于updater.exe所在目录的绝对路径
def get_app_base_dir():
    """获取updater.exe所在的基础目录"""
    if getattr(sys, 'frozen', False):
        # 打包后的exe环境
        base_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境
        base_dir = os.path.dirname(os.path.abspath(__file__))
    return base_dir

APP_BASE_DIR = get_app_base_dir()
MAIN_APP_DIR = "YimiaoJue"                 # 主程序目录名
MAIN_APP_EXE = os.path.join(APP_BASE_DIR, MAIN_APP_DIR, "YimiaoJue.exe")  # [修复] 绝对路径
UPDATE_ARCHIVE_NAME = "YimiaoJue-update.zip"
TEMP_UPDATE_DIR = "temp_update"

def _calculate_sha256(file_path):
    """计算文件的SHA256校验和"""
    sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            while chunk := f.read(4096):
                sha256.update(chunk)
        return sha256.hexdigest()
    except IOError:
        return None

class UpdaterApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("弈秒决 - 启动器 v3.0")
        self.geometry("400x180")
        self.resizable(False, False)
        self.configure(bg="#f0f0f0")

        # 将窗口居中
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width / 2) - (400 / 2)
        y = (screen_height / 2) - (180 / 2)
        self.geometry(f'+{int(x)}+{int(y)}')

        self.status_label = ttk.Label(self, text="正在初始化...", font=("Microsoft YaHei", 12))
        self.status_label.pack(pady=20, expand=True)
        
        self.progress = ttk.Progressbar(self, orient="horizontal", length=350, mode="determinate")
        self.progress.pack(pady=10)

        self.after(200, self.start_update_check)

    def set_status(self, text, value=None):
        """更新状态标签和进度条"""
        self.status_label.config(text=text)
        if value is not None:
            self.progress['value'] = value
        self.update_idletasks()

    def start_update_check(self):
        """在新线程中启动更新检查，避免UI卡死"""
        threading.Thread(target=self._update_thread, daemon=True).start()

    def _update_thread(self):
        """实际执行更新检查和应用的线程"""
        try:
            # 1. 检查版本
            self.set_status("正在检查更新...", 10)
            # --- [新] 根据本地配置文件决定更新渠道 ---
            channel = 'release'
            config_path = os.path.join(APP_BASE_DIR, 'update_config.json')
            if os.path.exists(config_path):
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                        if config_data.get('channel') == 'beta':
                            channel = 'beta'
                except Exception as e:
                    print(f"读取 update_config.json 失败: {e}")
            
            self.set_status(f"当前渠道: {'Beta测试' if channel == 'beta' else '正式版'}", 5)
            time.sleep(1)
            # --- [新] 结束 ---

            version_filename = "version-beta.json" if channel == 'beta' else "version.json"
            version_url = updater.REMOTE_REPO_BASE_URL + version_filename
            remote_info = updater._get_remote_version(url=version_url)

            if not remote_info:
                self.set_status("无法连接更新服务器，即将启动...", 90)
                time.sleep(1)
                self.launch_main_app()
                return

            # 从 local_version.json 读取当前版本
            local_info = updater._get_local_version()
            local_version = parse_version(local_info.get("app", {}).get("version", "0.0.0"))

            remote_app_info = remote_info.get("app", {})
            remote_version = parse_version(remote_app_info.get("version", "0.0.0"))
            
            # 2. 对比版本
            if remote_version > local_version:
                self.set_status(f"发现新版本 v{remote_version}，准备下载...", 20)
                
                # 3. 下载更新包
                update_url = remote_app_info.get("url")
                if not update_url:
                    messagebox.showerror("错误", "更新信息中未找到下载地址(URL)！")
                    self.launch_main_app()
                    return

                self._download_update(update_url)

                # 4. 文件完整性校验
                self.set_status("校验文件完整性...", 75)
                remote_checksum = remote_app_info.get("checksum")
                if remote_checksum:
                    local_checksum = _calculate_sha256(UPDATE_ARCHIVE_NAME)
                    if local_checksum != remote_checksum:
                        messagebox.showerror("更新错误", "下载的文件已损坏(校验和不匹配)！\n\n更新已中止，请重试。")
                        if os.path.exists(UPDATE_ARCHIVE_NAME):
                            os.remove(UPDATE_ARCHIVE_NAME)
                        self.launch_main_app()
                        return
                    print("文件校验成功！")
                else:
                    messagebox.showwarning("安全警告", "更新服务器未提供文件校验和，无法验证更新包的完整性。")

                # 5. 应用更新（使用延迟批处理）
                self.set_status("下载完成，准备应用更新...", 80)
                if self._prepare_delayed_update():
                    # 更新本地版本文件
                    self._update_local_version_file(remote_app_info)
                    self.set_status("更新准备完成，即将重启...", 95)
                    time.sleep(1)
                    self._execute_delayed_update()
                    return
                else:
                    # 更新失败，启动旧版本
                    self.launch_main_app()
                    return

            else:
                self.set_status("当前已是最新版本，即将启动...", 90)
                time.sleep(1)
            
            # 6. 启动主程序
            self.launch_main_app()

        except Exception as e:
            messagebox.showerror("更新错误", f"更新过程中发生未知错误: {e}")
            # 即使更新失败，也尝试启动主程序
            self.launch_main_app()

    def _update_local_version_file(self, new_app_info):
        """将新的应用版本信息写入 local_version.json 文件"""
        try:
            # 先读取完整的本地版本信息，只更新 app 部分
            local_info = updater._get_local_version()
            local_info["app"] = new_app_info
            
            with open(updater.LOCAL_VERSION_FILE, 'w', encoding='utf-8') as f:
                json.dump(local_info, f, ensure_ascii=False, indent=2)
            print(f"本地版本文件已更新至 app v{new_app_info['version']}")
        except Exception as e:
            # 即使这里失败了，也要提示用户，但不要中断主流程
            messagebox.showerror("重要提示", f"无法写入本地版本文件: {e}\n\n这可能导致下次启动时重复更新，但不影响本次使用。")

    def _download_update(self, url):
        """下载更新文件并显示进度"""
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            bytes_downloaded = 0
            
            with open(UPDATE_ARCHIVE_NAME, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    bytes_downloaded += len(chunk)
                    if total_size > 0:
                        progress_value = 20 + (bytes_downloaded / total_size) * 55 # 进度条在20%到75%之间
                        self.set_status(f"正在下载更新... {bytes_downloaded/1024/1024:.1f}MB / {total_size/1024/1024:.1f}MB", progress_value)
                    else:
                        self.set_status(f"正在下载更新... {bytes_downloaded/1024/1024:.1f}MB", 50)
        
        except requests.exceptions.RequestException as e:
            raise Exception(f"下载失败: {e}")

    def _prepare_delayed_update(self):
        """[核心方法] 准备延迟更新：解压文件并创建批处理脚本"""
        try:
            # 1. 清理可能存在的临时目录
            if os.path.exists(TEMP_UPDATE_DIR):
                shutil.rmtree(TEMP_UPDATE_DIR)
            
            # 2. 解压更新包到临时目录
            print(f"正在解压更新包到 {TEMP_UPDATE_DIR}...")
            with zipfile.ZipFile(UPDATE_ARCHIVE_NAME, 'r') as zip_ref:
                zip_ref.extractall(TEMP_UPDATE_DIR)
            
            # 3. 验证解压后的结构
            expected_main_dir = os.path.join(TEMP_UPDATE_DIR, MAIN_APP_DIR)
            if not os.path.exists(expected_main_dir):
                raise Exception(f"更新包结构错误：找不到 {MAIN_APP_DIR} 目录")
            
            # 4. 创建延迟更新批处理脚本
            self._create_delayed_update_script()
            
            return True
            
        except Exception as e:
            print(f"准备更新失败: {e}")
            messagebox.showerror("更新失败", f"准备更新时出错: {e}")
            # 清理临时文件
            if os.path.exists(TEMP_UPDATE_DIR):
                shutil.rmtree(TEMP_UPDATE_DIR)
            if os.path.exists(UPDATE_ARCHIVE_NAME):
                os.remove(UPDATE_ARCHIVE_NAME)
            return False

    def _create_delayed_update_script(self):
        """[v3.2 最终修复版] 创建一个使用相对路径、自给自足的延迟更新批处理脚本"""
        
        script_content = f"""@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo =========================================
echo    弈秒决 - 自动更新程序 v3.2
echo =========================================
echo.

:: 等待启动器完全退出
echo [1/6] 等待启动器退出...
timeout /t 3 /nobreak > nul

:: [关键修复] 检测当前版本结构并备份
echo [2/6] 检测并备份当前版本...

:: 创建备份目录
if exist "backup_old_version" rmdir /s /q "backup_old_version" 2>nul
mkdir "backup_old_version" 2>nul

set "backup_success=0"

:: 情况1: 检查是否存在旧的目录结构
if exist "{MAIN_APP_DIR}" (
    echo   - 检测到目录版本，备份整个目录...
    xcopy /s /e /y "{MAIN_APP_DIR}" "backup_old_version\\{MAIN_APP_DIR}\\" > nul 2>&1
    if not errorlevel 1 (
        rmdir /s /q "{MAIN_APP_DIR}" 2>nul
        if not errorlevel 1 (
            echo   - 目录版本备份成功
            set "backup_success=1"
        ) else (
            echo   - [错误] 无法删除旧目录，可能有文件被占用！
            echo   - 请关闭所有弈秒决相关程序后重试。
            pause
            goto :restore_and_exit
        )
    ) else (
        echo   - [错误] 备份目录版本失败！
        pause
        goto :restore_and_exit
    )
)

:: 情况2: 检查是否存在旧的单文件版本
if exist "YimiaoJue.exe" (
    echo   - 检测到单文件版本，备份exe文件...
    copy "YimiaoJue.exe" "backup_old_version\\YimiaoJue.exe" > nul 2>&1
    if not errorlevel 1 (
        del "YimiaoJue.exe" 2>nul
        if not errorlevel 1 (
            echo   - 单文件版本备份成功
            set "backup_success=1"
        ) else (
            echo   - [错误] 无法删除旧exe文件，可能被占用！
            echo   - 请关闭所有弈秒决相关程序后重试。
            pause
            goto :restore_and_exit
        )
    ) else (
        echo   - [错误] 备份单文件版本失败！
        pause
        goto :restore_and_exit
    )
)

:: [新增] 检查其他可能的旧文件并清理
echo   - 检查其他旧版本文件...
if exist "main.exe" (
    echo   - 发现旧的main.exe，正在备份并删除...
    copy "main.exe" "backup_old_version\\main.exe" > nul 2>&1
    del "main.exe" 2>nul
)

:: 检查是否成功备份了任何版本
if "%backup_success%"=="0" (
    echo   - 未检测到现有版本，这是首次安装
)

:: [关键修复] 安装新的目录版本
echo [3/6] 安装新版本...

:: 确保源目录存在
if not exist "{TEMP_UPDATE_DIR}\\{MAIN_APP_DIR}" (
    echo [错误] 更新包中缺少主程序目录！
    echo 更新包结构: {TEMP_UPDATE_DIR}\\{MAIN_APP_DIR}
    dir "{TEMP_UPDATE_DIR}" /b
    pause
    goto :restore_and_exit
)

:: 复制新版本目录
echo   - 正在安装新的目录版本...
xcopy /s /e /y "{TEMP_UPDATE_DIR}\\{MAIN_APP_DIR}" "{MAIN_APP_DIR}\\" > nul 2>&1
if errorlevel 1 (
    echo [错误] 安装新版本失败！
    echo 正在恢复备份...
    goto :restore_and_exit
)

:: [最终修复] 使用相对路径验证安装结果，避免路径问题导致崩溃
set "RELATIVE_EXE_PATH={MAIN_APP_DIR}\\YimiaoJue.exe"
if not exist "%RELATIVE_EXE_PATH%" (
    echo [错误] 安装后找不到主程序文件！
    echo 预期相对位置: %RELATIVE_EXE_PATH%
    pause
    goto :restore_and_exit
)

:: [重要] 最终清理：确保删除所有可能的旧版本文件
echo   - 最终清理旧版本文件...
if exist "YimiaoJue.exe" (
    echo   - 发现残留的YimiaoJue.exe，正在删除...
    del "YimiaoJue.exe" > nul 2>&1
)

echo   - 新版本安装成功！

:: 检查并更新启动器
echo [4/6] 检查启动器更新...
if exist "{TEMP_UPDATE_DIR}\\updater.exe" (
    echo   - 发现启动器更新，正在替换...
    
    :: 备份当前启动器
    if exist "updater.exe" (
        copy "updater.exe" "backup_old_version\\updater.exe" > nul 2>&1
    )
    
    :: 尝试替换启动器
    copy "{TEMP_UPDATE_DIR}\\updater.exe" "updater_new.exe" > nul 2>&1
    if exist "updater_new.exe" (
        move "updater_new.exe" "updater.exe" > nul 2>&1
        if errorlevel 1 (
            echo   - 启动器被占用，将在下次启动时更新
            move "updater_new.exe" "updater_pending.exe" > nul 2>&1
        ) else (
            echo   - 启动器更新成功！
        )
    )
) else (
    echo   - 无启动器更新
)

:: 清理临时文件
echo [5/6] 清理临时文件...
if exist "{TEMP_UPDATE_DIR}" (
    rmdir /s /q "{TEMP_UPDATE_DIR}" 2>nul
    echo   - 清理更新临时目录
)
if exist "{UPDATE_ARCHIVE_NAME}" (
    del "{UPDATE_ARCHIVE_NAME}" 2>nul
    echo   - 清理更新包文件
)

:: 清理旧备份（保留最新备份）
echo [6/6] 整理备份文件...
if exist "backup_old_version" (
    echo   - 当前版本已备份到 backup_old_version 目录
    echo   - 如需回滚，请手动恢复该目录中的文件
)

echo.
echo =========================================
echo         更新完成！正在启动程序...
echo =========================================
echo.

:: [最终修复] 使用相对路径启动新版本
if exist "%RELATIVE_EXE_PATH%" (
    echo 启动主程序: %RELATIVE_EXE_PATH%
    start "" "%RELATIVE_EXE_PATH%"
    timeout /t 3 /nobreak > nul
    echo 程序启动成功！
) else (
    echo [错误] 找不到主程序文件: %RELATIVE_EXE_PATH%
    echo.
    echo 请检查安装是否正确，或联系技术支持。
    pause
)

goto :end_script

:restore_and_exit
echo.
echo =========================================
echo         更新失败，正在恢复备份...
echo =========================================
echo.

:: 恢复备份
if exist "backup_old_version\\{MAIN_APP_DIR}" (
    echo 恢复目录版本...
    xcopy /s /e /y "backup_old_version\\{MAIN_APP_DIR}" "{MAIN_APP_DIR}\\" > nul 2>&1
)

if exist "backup_old_version\\YimiaoJue.exe" (
    echo 恢复单文件版本...
    copy "backup_old_version\\YimiaoJue.exe" "YimiaoJue.exe" > nul 2>&1
)

if exist "backup_old_version\\updater.exe" (
    echo 恢复启动器...
    copy "backup_old_version\\updater.exe" "updater.exe" > nul 2>&1
)

echo 备份恢复完成。
pause

:end_script
:: 清理批处理脚本自身
del "%~f0" 2>nul
exit /b 0
"""
        
        # [最终修复] 写入批处理文件，使用 GBK 编码，确保在各种 Windows 环境下的最大兼容性
        with open('delayed_update.bat', 'w', encoding='gbk') as f:
            f.write(script_content)
        
        print("已创建最终版的延迟更新脚本")

    def _execute_delayed_update(self):
        """执行延迟更新并退出启动器"""
        try:
            # 启动延迟更新脚本
            print("启动延迟更新脚本...")
            
            # [最终修复] 移除 CREATE_NO_WINDOW，允许命令行窗口闪现，以确保脚本在拥有完整权限的环境下运行。
            # 这是解决在某些环境下权限不足导致脚本中断的最稳健方法。
            subprocess.Popen('delayed_update.bat', shell=True)
            
            # 立即退出启动器，释放所有文件锁
            print("启动器退出，延迟更新脚本将在后台接管后续流程")
            self.destroy()
            sys.exit(0)
            
        except Exception as e:
            print(f"执行延迟更新失败: {e}")
            # 如果隐藏方式失败，尝试传统方式
            try:
                os.startfile('delayed_update.bat')
                print("使用备用方式启动更新脚本")
                self.destroy()
                sys.exit(0)
            except Exception as e2:
                print(f"备用启动方式也失败: {e2}")
                messagebox.showerror("更新失败", f"无法执行更新脚本: {e}\n\n请手动重启程序。")
                # 清理文件
                self._cleanup_update_files()
                self.launch_main_app()

    def _cleanup_update_files(self):
        """清理更新相关的临时文件"""
        try:
            if os.path.exists(TEMP_UPDATE_DIR):
                shutil.rmtree(TEMP_UPDATE_DIR)
            if os.path.exists(UPDATE_ARCHIVE_NAME):
                os.remove(UPDATE_ARCHIVE_NAME)
        except Exception as e:
            print(f"清理临时文件失败: {e}")

    def start_force_update(self):
        """[v3.2 新增] 启动强制更新线程，用于自动修复"""
        self.set_status("准备修复...", 5)
        threading.Thread(target=self._force_update_thread, daemon=True).start()

    def _force_update_thread(self):
        """[v3.2 新增] 强制更新线程，跳过版本检查，直接下载并应用更新"""
        try:
            # 1. 获取远程版本信息
            self.set_status("正在获取最新版本信息...", 10)
            remote_info = updater._get_remote_version()
            if not remote_info:
                messagebox.showerror("修复失败", "无法连接更新服务器，请检查网络后重试。")
                self.after(200, self.destroy)
                return
            
            remote_app_info = remote_info.get("app", {})
            remote_version = parse_version(remote_app_info.get("version", "0.0.0"))
            
            # --- 核心区别：跳过版本比较，直接进入更新流程 ---
            self.set_status(f"开始强制修复至 v{remote_version}...", 20)
            
            # 2. 下载更新包
            update_url = remote_app_info.get("url")
            if not update_url:
                messagebox.showerror("修复失败", "更新信息中未找到下载地址(URL)！")
                self.after(200, self.destroy)
                return

            self._download_update(update_url)

            # 3. 文件完整性校验
            self.set_status("校验文件完整性...", 75)
            remote_checksum = remote_app_info.get("checksum")
            if remote_checksum:
                local_checksum = _calculate_sha256(UPDATE_ARCHIVE_NAME)
                if local_checksum != remote_checksum:
                    messagebox.showerror("修复失败", "下载的文件已损坏(校验和不匹配)！\n\n请重试。")
                    if os.path.exists(UPDATE_ARCHIVE_NAME):
                        os.remove(UPDATE_ARCHIVE_NAME)
                    self.after(200, self.destroy)
                    return
                print("文件校验成功！")
            
            # 4. 应用更新（使用延迟批处理）
            self.set_status("下载完成，准备应用更新...", 80)
            if self._prepare_delayed_update():
                self._update_local_version_file(remote_app_info)
                self.set_status("修复完成，即将重启...", 95)
                time.sleep(1)
                self._execute_delayed_update()
            else:
                messagebox.showerror("修复失败", "无法应用更新，请手动重新安装。")
                self.after(200, self.destroy)

        except Exception as e:
            messagebox.showerror("修复失败", f"修复过程中发生未知错误: {e}")
            self.after(200, self.destroy)

    def launch_main_app(self):
        """启动主程序并关闭自身，[v3.3 修复] 改用更稳定的启动方式"""
        self.set_status("正在启动主程序...", 100)
        
        try:
            if os.path.exists(MAIN_APP_EXE):
                # [v3.3 关键修复] 使用subprocess.Popen并明确设置工作目录(cwd)
                # 这是为了模拟手动双击exe的效果，确保程序能找到其依赖文件
                main_app_dir = os.path.dirname(MAIN_APP_EXE)
                print(f"✅ 找到主程序，准备启动: {MAIN_APP_EXE}")
                print(f"   > 设置工作目录为: {main_app_dir}")
                
                subprocess.Popen([MAIN_APP_EXE], cwd=main_app_dir, creationflags=subprocess.CREATE_NO_WINDOW)
                
                print("✅ 启动命令已发送，更新器将退出。")
                self.after(500, self.destroy)
            else:
                # 如果文件确实不存在，这通常是安装不完整或被杀软删除
                print(f"❌ 找不到主程序文件: {MAIN_APP_EXE}")
                error_msg = f"""启动失败：找不到主程序文件。

这可能是由于安装不完整或文件被杀毒软件误删。

请尝试：
1. 暂时关闭杀毒软件后重新安装。
2. 检查路径是否正确:
   {MAIN_APP_EXE}"""
                messagebox.showerror("启动失败", error_msg)
                self.after(500, self.destroy)
                    
        except Exception as e:
            error_msg = f"启动主程序时发生未知错误: {e}\n\n请尝试重新安装，或联系技术支持。"
            print(f"❌ 启动失败，异常: {traceback.format_exc()}")
            messagebox.showerror("启动失败", error_msg)
            self.after(500, self.destroy)

if __name__ == "__main__":
    app = UpdaterApp()
    app.mainloop() 