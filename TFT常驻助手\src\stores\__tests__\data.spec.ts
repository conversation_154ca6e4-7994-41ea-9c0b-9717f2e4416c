import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useDataStore } from '../data';

// Mock Tauri API
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: vi.fn()
}));

describe('Data Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  it('应该正确初始化状态', () => {
    const store = useDataStore();
    
    expect(store.globalHeroInfoMap).toEqual({});
    expect(store.globalTraitIconMap).toEqual({});
    expect(store.globalItemInfoMap).toEqual({});
    expect(store.queryCache).toEqual({});
    expect(store.isGlobalDataLoaded).toBe(false);
    expect(store.isPreloading).toBe(false);
    expect(store.isDataReady).toBe(false);
  });

  it('应该正确管理查询缓存', () => {
    const store = useDataStore();
    const testData = { id: 1, name: 'test' };
    const cacheKey = 'test_key';
    
    // 设置缓存
    store.setCachedQuery(cacheKey, testData);
    expect(store.getCachedQuery(cacheKey)).toEqual(testData);
    
    // 清除特定缓存
    store.clearQueryCache(cacheKey);
    expect(store.getCachedQuery(cacheKey)).toBeNull();
    
    // 设置多个缓存并清除所有
    store.setCachedQuery('key1', 'data1');
    store.setCachedQuery('key2', 'data2');
    store.clearQueryCache();
    expect(store.getCachedQuery('key1')).toBeNull();
    expect(store.getCachedQuery('key2')).toBeNull();
  });

  it('应该正确获取英雄信息', () => {
    const store = useDataStore();
    const heroData = {
      cn_name: '测试英雄',
      en_name: 'TestHero',
      cost: 3,
      traits: ['测试羁绊'],
      icon_path: '/path/to/icon.png'
    };
    
    // 手动设置英雄数据
    store.globalHeroInfoMap['测试英雄'] = heroData;
    store.globalHeroInfoMap['TestHero'] = heroData;
    
    expect(store.getHeroInfo('测试英雄')).toEqual(heroData);
    expect(store.getHeroInfo('TestHero')).toEqual(heroData);
    expect(store.getHeroInfo('不存在的英雄')).toBeNull();
  });

  it('应该正确获取装备信息', () => {
    const store = useDataStore();
    const itemData = {
      name: '测试装备',
      en_name: 'TestItem',
      icon_path: '/path/to/item.png',
      components: ['组件1', '组件2']
    };
    
    // 手动设置装备数据
    store.globalItemInfoMap['测试装备'] = itemData;
    store.globalItemInfoMap['TestItem'] = itemData;
    
    expect(store.getItemInfo('测试装备')).toEqual(itemData);
    expect(store.getItemInfo('TestItem')).toEqual(itemData);
    expect(store.getItemInfo('不存在的装备')).toBeNull();
  });

  it('应该正确获取羁绊图标', () => {
    const store = useDataStore();
    const iconPath = '/path/to/trait.png';
    
    // 手动设置羁绊图标数据
    store.globalTraitIconMap['测试羁绊'] = iconPath;
    
    expect(store.getTraitIcon('测试羁绊')).toBe(iconPath);
    expect(store.getTraitIcon('不存在的羁绊')).toBeNull();
  });

  it('应该正确重置缓存', () => {
    const store = useDataStore();
    
    // 设置一些数据
    store.setCachedQuery('test', 'data');
    store.globalHeroInfoMap['hero'] = { cn_name: 'hero' } as any;
    store.globalItemInfoMap['item'] = { name: 'item' } as any;
    store.globalTraitIconMap['trait'] = '/icon.png';
    
    // 重置缓存
    store.resetCache();
    
    expect(store.globalHeroInfoMap).toEqual({});
    expect(store.globalItemInfoMap).toEqual({});
    expect(store.globalTraitIconMap).toEqual({});
    expect(store.queryCache).toEqual({});
    expect(store.isGlobalDataLoaded).toBe(false);
    expect(store.isPreloading).toBe(false);
  });

  it('应该正确获取缓存状态', () => {
    const store = useDataStore();
    
    const status = store.getCacheStatus();
    
    expect(status).toHaveProperty('isLoaded');
    expect(status).toHaveProperty('isPreloading');
    expect(status).toHaveProperty('stats');
    expect(status).toHaveProperty('totalSize');
    expect(status.stats).toHaveProperty('heroCount');
    expect(status.stats).toHaveProperty('itemCount');
    expect(status.stats).toHaveProperty('traitCount');
    expect(status.stats).toHaveProperty('queryCacheSize');
  });
});