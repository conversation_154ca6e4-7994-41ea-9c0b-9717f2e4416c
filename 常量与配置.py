# 常量与配置.py

from pathlib import Path
from PySide6.QtGui import QColor

# --- 基础路径 ---
# 获取此文件所在的目录作为基础目录
BASE_DIR = Path(__file__).parent.resolve()
DB_FILE = BASE_DIR / "tft_data.db"

# --- 图标文件夹路径 (相对于 BASE_DIR) ---
# 注意：这些路径需要与您实际的文件夹名称和数据库中存储的相对路径匹配
# 如果数据库中 icon_path 存的是 "英雄图标/1费/xxx.png"，则这里保持为空或 '.' 即可
# 如果数据库中 icon_path 存的是 "1费/xxx.png"，则这里应为 "英雄图标"
HERO_ICON_BASE_DIR = BASE_DIR # 假设 heroes.icon_path 包含 "英雄图标/..."
ITEM_ICON_BASE_DIR = BASE_DIR # 假设 items.icon_path 包含 "装备图标/..."
TRAIT_ICON_BASE_DIR = BASE_DIR # 假设 traits.icon_path 包含 "羁绊图标/..."
HEX_ICON_BASE_DIR = BASE_DIR   # 假设 hexes.icon_path 包含 "海克斯图标/..."

# --- 颜色配置 ---
# 鲜艳风格评级颜色
TIER_COLORS_VIBRANT = {
    "S": "#FF5A5F",
    "A": "#FFA756",
    "B": "#FFD700",
    "C": "#F3FF59", # 调整C级颜色以便区分
    "D": "#8AFF40",
    "DEFAULT": "#64748B", # 未知或无评级
    "ERROR": "#FF0000"    # 错误状态
}
# 当前使用的评级颜色主题
TIER_COLORS = TIER_COLORS_VIBRANT

# 窗口和控件颜色
WINDOW_BORDER_COLOR = "#0F172A"  # 窗口外边框/标题栏背景
WINDOW_BG_COLOR = "#1E293B"      # 主要内容区域背景
CONTROL_BAR_BG = WINDOW_BORDER_COLOR # 控制按钮栏背景 (如顶部条、Tab栏背景)
SECTION_BG_COLOR = "#293548"     # 区块背景色 (例如详情页顶部信息区)
ITEM_ROW_BG_HOVER = "#334155"   # 列表行悬停背景
BORDER_COLOR = "#4A5568"         # 边框、分隔线颜色

# 文本颜色
TEXT_COLOR_LIGHT = "#E2E8F0"     # 主要亮色文本 (白色系)
TEXT_COLOR_MEDIUM = "#A0AEC0"    # 中等亮度文本 (原为 #94A3B8)
TEXT_COLOR_DARK = "#1A202C"      # 深色文本 (用于浅色背景上, 如评级标签)
TEXT_COLOR_HIGHLIGHT = "#8EBBFF" # 高亮/链接文本颜色
TEXT_COLOR_ERROR = "#FF6B6B"     # 错误信息文本颜色

# 图标占位符背景
ICON_PLACEHOLDER_BG = "#4A5568"

# 按钮颜色
BUTTON_BG = "#334155"
BUTTON_BG_HOVER = "#475569"
BUTTON_BG_PRESSED = "#293548"
BUTTON_TEXT = TEXT_COLOR_LIGHT

# 导航栏颜色
NAV_BUTTON_BG_SELECTED = BUTTON_BG_HOVER # 导航按钮选中背景色 (可以调整)
NAV_TEXT_COLOR = TEXT_COLOR_MEDIUM
NAV_TEXT_COLOR_SELECTED = TEXT_COLOR_LIGHT
NAV_INDICATOR_COLOR = TIER_COLORS["A"] # 导航选中下划线颜色

# 英雄费用颜色 (最终调整为更亮丽)
COST_COLORS = {
    1: "#D3D3D3",  # 非常亮的灰色
    2: "#32CD32",  # 青色系亮绿
    3: "#00BFFF",  # 更亮的蓝色
    4: "#BF00FF",  # 亮紫色
    5: "#FFD700",  # 明亮的黄色/金色
    0: "#B0BEC5"   # 亮蓝灰色
}

# --- 字体配置 (可选) ---
# FONT_FAMILY_DEFAULT = "Microsoft YaHei" # 默认字体族
# 定义标准字号
FONT_SIZE_SMALL = 11
FONT_SIZE_NORMAL = 12
FONT_SIZE_MEDIUM = 13
FONT_SIZE_LARGE = 14
FONT_SIZE_XLARGE = 16
# FONT_SIZE_XXLARGE = 18

# 定义标准字体权重 (CSS/Qt 常用值)
FONT_WEIGHT_NORMAL = 400
FONT_WEIGHT_MEDIUM = 500
FONT_WEIGHT_SEMIBOLD = 600
FONT_WEIGHT_BOLD = 700

# --- 尺寸配置 ---
WINDOW_DEFAULT_WIDTH = 580
WINDOW_DEFAULT_HEIGHT = 700 # 稍微增加高度以容纳更复杂布局
CONTROL_BAR_HEIGHT = 40
NAV_BAR_HEIGHT = 40

# 图标尺寸
ICON_SIZE_SMALL = 16  # 羁绊图标
ICON_SIZE_MEDIUM = 24 # 列表中的小图标
ICON_SIZE_LARGE = 36  # 英雄/装备列表图标, 阵容详情英雄图标
ICON_SIZE_XLARGE = 50 # 详情页顶部主图标

# --- 资源管理配置 ---
# 图标缓存 (运行时填充)
ICON_CACHE = {}
# 图标缓存大小限制
MAX_ICON_CACHE_SIZE = 300  # 最多缓存300个图标
# 启用图标缓存大小限制
ENABLE_ICON_CACHE_LIMIT = True
# 图标缓存清理阈值 - 当缓存大小超过此值时触发清理
ICON_CACHE_CLEAN_THRESHOLD = 250
# 图标缓存清理后保留的数量 (保留最近使用的图标)
ICON_CACHE_KEEP_COUNT = 150

# --- 内存管理配置 ---
# 启用详情页视图卸载 - 当切换到其他页面时释放内存
ENABLE_VIEW_UNLOADING = True
# 视图卸载延时 (毫秒) - 切换页面后多久卸载不可见视图的内存
VIEW_UNLOAD_DELAY_MS = 15000  # 15秒后卸载不可见的详情页

# --- 新增：全局预加载数据字典 ---
GLOBAL_HERO_INFO_MAP = {}    # {en_lower: {'cn_name': ..., 'icon_path': ...}}
GLOBAL_TRAIT_ICON_MAP = {}   # {trait_name: icon_path}
GLOBAL_HERO_BASE_STATS = {} # {cn_name: {'avg_place': ...}}
GLOBAL_ITEM_INFO_MAP = {}    # {item_name: {'icon_path': ..., 'en_name': ...}}

# 数据库查询结果缓存 (可选，用于优化重复查询)
QUERY_CACHE = {}
CACHE_EXPIRY_SECONDS = 300 # 缓存过期时间 (例如5分钟)
# 数据库查询缓存大小限制
MAX_QUERY_CACHE_SIZE = 100 # 最多缓存100个查询结果
# 启用查询缓存大小限制
ENABLE_QUERY_CACHE_LIMIT = True

# 异步任务配置
MAX_THREADS = 3 # 数据库查询等后台任务的最大线程数 (降低到3)
# 定时清理资源间隔 (毫秒)
RESOURCE_CLEANUP_INTERVAL_MS = 180000  # 3分钟清理一次资源

# --- 辅助函数 ---
def get_color(color_key, default="#FFFFFF"):
    """安全地获取颜色值"""
    # 可以扩展为支持从配置或主题获取
    if color_key in TIER_COLORS: return TIER_COLORS[color_key]
    # 可以添加其他颜色映射
    # ...
    return default

def get_icon_path(base_dir, relative_path):
    """根据基础目录和相对路径构建完整的图标路径"""
    if not relative_path:
        return None
    full_path = base_dir / relative_path
    # 可以在这里添加检查文件是否存在的逻辑，但为了性能可能不在每次调用时检查
    return str(full_path) # 返回字符串路径

# --- 样式表片段 (方便复用) ---
SCROLL_AREA_STYLE_SIMPLE = f"""
    QScrollArea {{
        background-color: transparent;
        border: none;
    }}
    QScrollBar:vertical {{
        border: none;
        background: {WINDOW_BG_COLOR};
        width: 8px;
        margin: 0px 0px 0px 0px;
    }}
    QScrollBar::handle:vertical {{
        background: {BORDER_COLOR};
        min-height: 20px;
        border-radius: 4px;
    }}
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        border: none;
        background: none;
        height: 0px;
    }}
    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
        background: none;
    }}
    QScrollBar:horizontal {{
        border: none;
        background: {WINDOW_BG_COLOR};
        height: 8px;
        margin: 0px 0px 0px 0px;
    }}
    QScrollBar::handle:horizontal {{
        background: {BORDER_COLOR};
        min-width: 20px;
        border-radius: 4px;
    }}
     QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
        border: none;
        background: none;
        width: 0px;
    }}
    QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
        background: none;
    }}
"""

# 基础按钮样式
BUTTON_STYLE_BASE = f"""
    QPushButton {{
        background-color: {BUTTON_BG};
        color: {BUTTON_TEXT};
        border: none;
        border-radius: 4px;
        padding: 5px 10px;
        font-size: {FONT_SIZE_NORMAL}px; /* 使用调整后的常量 */
        font-weight: {FONT_WEIGHT_MEDIUM}; /* 使用中等权重 */
        min-height: 22px; /* 稍微增加最小高度以适应字体 */
    }}
    QPushButton:hover {{
        background-color: {BUTTON_BG_HOVER};
    }}
    QPushButton:pressed {{
        background-color: {BUTTON_BG_PRESSED};
    }}
    QPushButton:disabled {{
        background-color: #2A3B4D; /* 禁用状态颜色 */
        color: #64748B;
    }}
"""
# 主导航按钮样式
NAV_BUTTON_STYLE = f"""
    QPushButton {{
        background-color: transparent;
        color: {NAV_TEXT_COLOR};
        border: none;
        padding: 8px 12px;
        font-size: {FONT_SIZE_MEDIUM}px; /* 使用调整后的常量 */
        font-weight: {FONT_WEIGHT_SEMIBOLD}; /* 导航字体半粗 */
    }}
    QPushButton:hover {{
        color: {NAV_TEXT_COLOR_SELECTED};
    }}
    QPushButton:checked {{ /* 使用 checked 状态来表示选中 */
        color: {NAV_TEXT_COLOR_SELECTED};
        border-bottom: 1px solid {WINDOW_BG_COLOR}; /* 选中时底部边框与背景融合 */
        /* 选中时保持半粗体 */
    }}
"""

# TabWidget 样式
TAB_STYLE = f"""
    QTabWidget::pane {{
        border: 1px solid {BORDER_COLOR};
        border-top: none;
        background-color: {WINDOW_BG_COLOR};
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
    }}
    QTabBar::tab {{
        background: {CONTROL_BAR_BG};
        color: {TEXT_COLOR_MEDIUM};
        border: 1px solid {BORDER_COLOR};
        border-bottom: none;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        padding: 6px 15px; /* 使用调整后的常量 */
        margin-right: 2px;
        min-width: 70px; /* 调整最小宽度 */
        font-size: {FONT_SIZE_NORMAL}px; /* 使用调整后的常量 */
    }}
    QTabBar::tab:selected {{
        background: {WINDOW_BG_COLOR};
        color: {TEXT_COLOR_LIGHT};
        border: 1px solid {BORDER_COLOR};
        border-bottom: none; # 选中时移除底部边框，避免与 pane 重叠
    }}
"""

# 搜索框样式
LINE_EDIT_STYLE = f"""
    QLineEdit {{
        color: {TEXT_COLOR_LIGHT};
        background-color: {BUTTON_BG};
        border: 1px solid {BORDER_COLOR};
        border-radius: 4px;
        padding: 4px 8px;
        font-size: {FONT_SIZE_NORMAL}px;
    }}
    QLineEdit:focus {{
        border: 1px solid {TEXT_COLOR_HIGHLIGHT};
    }}
"""

# 工具提示样式
TOOLTIP_STYLE = f"""
    QToolTip {{
        color: {TEXT_COLOR_LIGHT};
        background-color: {SECTION_BG_COLOR};
        border: 1px solid {BORDER_COLOR};
        border-radius: 4px;
        padding: 5px;
        font-size: {FONT_SIZE_SMALL}px;
    }}
"""

# --- 标记需要释放资源的视图页面 ---
MEMORY_INTENSIVE_VIEWS = [4, 5, 6]  # 阵容详情、英雄详情、装备详情