# 视图模块/阵容列表视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
                               QSpacerItem, QSizePolicy, QFrame)
from PySide6.QtCore import Qt, Signal, Slot
from PySide6.QtGui import QCursor, QColor, QFontMetrics

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_DARK,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ICON_PLACEHOLDER_BG,
                       ICON_SIZE_LARGE, FONT_SIZE_SMALL, FONT_SIZE_MEDIUM)
from 自定义组件 import ClickableLabel, WheelHorizontalScrollArea, SearchLineEdit, IconLabel # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

import os
import json
from pathlib import Path

# --- 阵容图标 Widget ---
class CompIconWidget(QWidget):
    """显示单个阵容图标和名称的 Widget"""
    comp_clicked = Signal(str) # 点击时发出阵容名称

    def __init__(self, comp_name, comp_heroes=None, parent=None):
        super().__init__(parent)
        self._comp_name = comp_name
        self._comp_heroes = comp_heroes if comp_heroes else [] # 阵容包含的英雄列表 (用于尝试找核心)
        self.setFixedSize(70, 80)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        self.setToolTip(f"查看 {comp_name} 详情")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(3) # 图标和名称间距
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # --- 图标标签 ---
        # 使用 IconLabel 来处理图标加载和占位符
        self.icon_label = IconLabel(data=self._comp_name, icon_size=ICON_SIZE_LARGE + 10, icon_type='hero', placeholder_text='阵容') # 图标稍大
        self.icon_label.setStyleSheet(f"background-color:{ICON_PLACEHOLDER_BG}; color:{TEXT_COLOR_LIGHT}; font-size:11px; font-weight:bold; border-radius:6px; border:1px solid {BORDER_COLOR};")
        # 覆盖点击事件，使得点击图标也触发 comp_clicked
        self.icon_label.clicked.connect(lambda data: self.comp_clicked.emit(self._comp_name))

        # --- 名称标签 ---
        self.name_label = QLabel()
        # 计算省略后的文本
        fm = QFontMetrics(self.name_label.font())
        # 设置名称标签的最大宽度略小于CompIconWidget的宽度
        max_name_width = self.width() - 6
        elided_name = fm.elidedText(comp_name, Qt.TextElideMode.ElideRight, max_name_width)
        self.name_label.setText(elided_name)
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # 使用常量配置中的小号字体
        # 添加 font-weight: bold 使其加粗
        self.name_label.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size:{FONT_SIZE_SMALL}px; font-weight: bold;")
        # self.name_label.setWordWrap(True) # 移除换行
        self.name_label.setMaximumWidth(max_name_width) # 限制最大宽度
        # 设置固定高度确保对齐
        self.name_label.setFixedHeight(fm.height() + 2) # 单行文本高度加一点padding

        layout.addWidget(self.icon_label)
        layout.addWidget(self.name_label)

        # 尝试加载核心英雄图标 (异步)
        self.load_core_hero_icon()

    def load_core_hero_icon(self):
        """尝试从 comp_heroes 列表或数据库加载核心英雄图标"""
        # 简化逻辑：优先使用 comp_heroes 列表中的第一个英雄作为代表
        # 更复杂的逻辑可以查询 comp_detail_unit_stats 找最高出场率英雄
        core_hero_name = self._comp_heroes[0] if self._comp_heroes else None

        if core_hero_name:
            # 异步查询该英雄的图标路径
            sql = "SELECT icon_path FROM heroes WHERE cn_name = ?"
            query_key = f"hero_icon_{core_hero_name}"
            execute_query_async(
                sql, (core_hero_name,),
                on_success=self.on_icon_path_loaded,
                on_error=lambda e: print(f"查询核心英雄 {core_hero_name} 图标失败: {e}"),
                query_key=query_key
            )
        else:
            # 如果没有英雄信息，保持默认占位符
            self.icon_label.set_icon(None, self._comp_name)


    def on_icon_path_loaded(self, results):
        """核心英雄图标路径加载成功回调"""
        if results and results[0].get('icon_path'):
            icon_path = results[0]['icon_path']
            self.icon_label.set_icon(icon_path, self._comp_name)
        else:
            # 如果查询失败或无路径，保持默认占位符
             self.icon_label.set_icon(None, self._comp_name)


    def mousePressEvent(self, event):
        """处理整个 Widget 的点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.comp_clicked.emit(self._comp_name)
        super().mousePressEvent(event)

# --- 主视图 ---
class 阵容列表视图(QWidget):
    """显示按评级分组的阵容列表"""
    comp_selected = Signal(str) # 点击阵容时发出信号
    comps_displayed = Signal(list) # 新增：当阵容列表更新显示后发出

    def __init__(self, parent=None):
        super().__init__(parent)
        self.comp_data = {} # 按评级存储阵容数据: {'S': [comp1, comp2], 'A': [...]}
        self.comp_hero_map = {} # 存储阵容到其英雄列表的映射: {comp_name: [hero1, hero2]}
        self.comp_trait_map = {} # 存储阵容到其羁绊列表的映射: {comp_name: [trait1, trait2]}
        self.filtered_comp_data = {} # 存储筛选后的数据
        self.tier_order = ["S", "A", "B", "C", "D", "DEFAULT"] # 评级显示顺序
        
        # 新增：存储阵容顺序的字典
        self.comp_order_map = {}
        # 加载阵容映射文件获取顺序
        self._load_comp_order_map()

        self.tier_widgets = {} # 存储对 QWidget 的引用，用于显示/隐藏

        self._is_loading_base = False # 跟踪基础数据加载状态
        self._is_loading_heroes = False # 跟踪英雄数据加载状态
        self._is_loading_traits = False # 跟踪羁绊数据加载状态

        self.init_ui()
        # 不在初始化时加载数据，由主窗口控制
        # self.load_data()

    def _load_comp_order_map(self):
        """加载阵容映射文件，获取阵容的原始顺序"""
        try:
            # 获取当前文件的绝对路径
            current_dir = Path(__file__).parent.parent.absolute()
            mapping_file = current_dir / "映射文件" / "阵容数据映射.json"
            
            if mapping_file.exists():
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    comp_mapping = json.load(f)
                    
                # 为每个阵容分配一个顺序索引
                for index, (comp_name, _) in enumerate(comp_mapping.items()):
                    self.comp_order_map[comp_name] = index
                    
                print(f"成功加载阵容映射文件，获取了 {len(self.comp_order_map)} 个阵容的顺序")
            else:
                print(f"警告：阵容映射文件 {mapping_file} 不存在")
        except Exception as e:
            print(f"加载阵容映射文件失败: {e}")
            # 出错不影响程序运行，但会使用默认排序

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(8)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 搜索框 ---
        self.search_box = SearchLineEdit(placeholder_text="搜索阵容名称、英雄或羁绊...")
        self.search_box.searchChanged.connect(self.filter_comps)
        self.main_layout.addWidget(self.search_box)

        # --- 加载提示 QLabel ---
        self.loading_label = QLabel("正在加载阵容数据...", alignment=Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 14px; padding: 30px;")
        self.main_layout.addWidget(self.loading_label)
        self.loading_label.hide() # 初始隐藏

        # --- 阵容列表滚动区域 (初始隐藏) ---
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.hide() # 初始隐藏

        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(10) # 评级区块之间的间距

        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area, 1) # scroll_area 占据剩余空间

    def load_data(self):
        """异步加载阵容基础数据、英雄关联和羁绊关联"""
        # 确保只在未加载时执行，或者没有正在加载时
        if (hasattr(self, 'comp_data') and self.comp_data and not self.is_loading()) or self.is_loading():
            if self.is_loading():
                print("阵容列表数据正在加载中，请稍候...")
            else:
                print("阵容列表数据已加载，跳过重复加载。")
                # 如果数据已加载且没有在加载，确保UI是正确的状态
                if not self.loading_label.isHidden() or self.scroll_area.isHidden():
                    self.loading_label.hide()
                    self.scroll_area.show()
                    self.update_ui() # 确保内容是筛选后的最新状态
            return

        print("开始异步加载阵容列表数据...")
        self._is_loading_base = True
        self._is_loading_heroes = True
        self._is_loading_traits = True

        self.comp_data = {}
        self.comp_hero_map = {}
        self.comp_trait_map = {}
        self.filtered_comp_data = {}
        self.tier_widgets = {}

        # 显示加载提示，隐藏内容区
        self.loading_label.setText("正在加载阵容数据...")
        self.loading_label.show()
        self.scroll_area.hide()
        # 清理旧的内容 (如果之前有的话)
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()


        # 1. 加载阵容基础数据 (添加 comp_id用于排序)
        sql_base = "SELECT name, tier, frequency, comp_id FROM comps_base ORDER BY tier, CAST(comp_id AS INTEGER) ASC"
        query_key_base = "all_comps_base_list_with_freq"
        execute_query_async(
            sql_base,
            on_success=self.on_base_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key_base
        )

        # 2. 加载阵容英雄关联数据 (添加 ORDER BY)
        sql_heroes = "SELECT comp_name, hero_cn_name FROM comp_heroes ORDER BY comp_name, hero_order ASC"
        query_key_heroes = "all_comp_heroes_map_ordered"
        execute_query_async(
            sql_heroes,
            on_success=self.on_hero_map_loaded,
            on_error=lambda e: print(f"加载阵容英雄关联失败: {e}"), # 出错不阻塞主流程
            query_key=query_key_heroes
        )

        # 3. 加载阵容羁绊关联数据 (使用 comp_detail_trait_stats)
        sql_traits_with_name = """
            SELECT cts.comp_name, t.name as trait_cn_name
            FROM comp_detail_trait_stats cts
            JOIN traits t ON cts.trait_id = t.en_name OR cts.trait_id = t.name
        """
        query_key_traits = "all_comp_traits_map"
        execute_query_async(
            sql_traits_with_name,
            on_success=self.on_trait_map_loaded,
            on_error=lambda e: print(f"加载阵容羁绊关联失败: {e}"),
            query_key=query_key_traits
        )

    def on_base_data_loaded(self, results):
        """阵容基础数据加载成功 (包含 frequency)"""
        print(f"阵容基础数据加载成功，共 {len(results)} 条。")
        temp_comp_data = {}
        if results:
            for row in results:
                tier = row.get('tier') if row.get('tier') else "DEFAULT"
                if tier not in temp_comp_data:
                    temp_comp_data[tier] = []
                temp_comp_data[tier].append(row)
        self.comp_data = temp_comp_data
        self._is_loading_base = False # 标记基础数据加载完成
        self.check_and_update_ui()

    def on_hero_map_loaded(self, results):
        """阵容英雄关联加载成功"""
        print(f"阵容英雄关联加载成功，共 {len(results)} 条。")
        temp_hero_map = {}
        if results:
            for row in results:
                comp_name = row['comp_name']
                hero_name = row['hero_cn_name']
                if comp_name not in temp_hero_map:
                    temp_hero_map[comp_name] = []
                temp_hero_map[comp_name].append(hero_name)
        self.comp_hero_map = temp_hero_map
        self._is_loading_heroes = False # 标记英雄数据加载完成
        self.check_and_update_ui()

    def on_trait_map_loaded(self, results):
        """阵容羁绊关联加载成功"""
        print(f"阵容羁绊关联加载成功，共 {len(results)} 条。")
        temp_trait_map = {}
        if results:
            for row in results:
                comp_name = row['comp_name']
                trait_name = row['trait_cn_name']
                if comp_name not in temp_trait_map:
                    temp_trait_map[comp_name] = set() # 使用集合去重
                temp_trait_map[comp_name].add(trait_name)
        # 将集合转换回列表
        self.comp_trait_map = {comp: list(traits) for comp, traits in temp_trait_map.items()}
        self._is_loading_traits = False # 标记羁绊数据加载完成
        self.check_and_update_ui() # 这里也调用，以防这是最后一个完成的

    def on_data_load_error(self, error_message, query_key=None):
        """数据加载失败"""
        print(f"阵容列表数据加载失败 (查询: {query_key}): {error_message}")
        # 根据失败的查询类型，更新对应的加载状态标志
        if query_key == "all_comps_base_list_with_freq":
            self._is_loading_base = False
        elif query_key == "all_comp_heroes_map_ordered":
            self._is_loading_heroes = False
        elif query_key == "all_comp_traits_map":
            self._is_loading_traits = False

        # 即使部分数据加载失败，也尝试更新UI，或者显示更具体的错误信息
        # 如果所有加载尝试都已结束（无论成功或失败），则更新UI
        if not self.is_loading():
            self.display_error(f"部分数据加载失败，请稍后重试。错误详情: {error_message}")
            self.loading_label.setText(f"加载阵容数据失败: {error_message[:100]}...") # 显示错误信息
            self.loading_label.show() # 确保错误信息可见
            self.scroll_area.hide()
        else:
            # 如果还有其他数据正在加载，暂时不改变UI，等待其他回调
            print(f"查询 {query_key} 失败，但其他数据仍在加载中...")

    def is_loading(self):
        """检查是否有任何数据仍在加载中"""
        return self._is_loading_base or self._is_loading_heroes or self._is_loading_traits

    def check_and_update_ui(self):
        """检查所有数据是否已加载，如果完成则更新UI"""
        if not self.is_loading(): # 只有当所有数据都加载完毕
            print("所有阵容列表相关数据已加载完成，准备更新UI。")
            if not self.comp_data: # 检查核心数据 comp_data 是否为空
                self.display_error("未能加载到阵容基础数据。")
            else:
                self.loading_label.hide() # 隐藏加载提示
                self.scroll_area.show()   # 显示内容区域
                self.filter_comps(self.search_box.text()) # 使用当前搜索词应用筛选并更新UI
        else:
            loading_parts = []
            if self._is_loading_base: loading_parts.append("基础")
            if self._is_loading_heroes: loading_parts.append("英雄")
            if self._is_loading_traits: loading_parts.append("羁绊")
            print(f"等待其他数据加载完成: {', '.join(loading_parts)} 未完成...")

    def display_error(self, message):
        """显示错误信息"""
        if self.loading_label: self.loading_label.hide()
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()
        error_label = QLabel(message, alignment=Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_MEDIUM}px; padding: 20px;")
        error_label.setWordWrap(True)
        self.content_layout.addWidget(error_label)
        self.loading_label = None

    def filter_comps(self, search_term):
        """根据搜索框内容筛选阵容"""
        search_term = search_term.strip().lower()
        self.filtered_comp_data = {}

        if not search_term:
            # 使用深拷贝确保不影响原始数据
            import copy
            self.filtered_comp_data = {tier: copy.deepcopy(comps) for tier, comps in self.comp_data.items()}
        else:
            for tier, comp_list in self.comp_data.items():
                filtered_list = []
                for comp in comp_list:
                    comp_name = comp.get('name', '').lower()
                    if search_term in comp_name:
                        filtered_list.append(comp)
                        continue
                    # 英雄和羁绊筛选需要等待 map 加载完成
                    if self.comp_hero_map:
                        heroes = self.comp_hero_map.get(comp['name'], [])
                        if any(search_term in hero.lower() for hero in heroes):
                            filtered_list.append(comp)
                            continue
                    if self.comp_trait_map:
                        traits = self.comp_trait_map.get(comp['name'], [])
                        if any(search_term in trait.lower() for trait in traits):
                            filtered_list.append(comp)
                            continue
                if filtered_list:
                    self.filtered_comp_data[tier] = filtered_list
        self.update_ui()

    def update_ui(self):
        """根据 self.filtered_comp_data 更新UI元素"""
        if self.is_loading(): # 如果仍在加载数据，则不更新UI
            print("update_ui 调用，但仍在加载数据，跳过UI更新。")
            return

        # 清理旧的内容
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()
            
        # 重置tier_widgets字典，避免引用已删除的widget
        self.tier_widgets = {}

        displayed_comps_for_signal = []
        for tier in self.tier_order:
            if tier in self.filtered_comp_data and self.filtered_comp_data[tier]:
                comp_list_for_tier = self.filtered_comp_data[tier]
                # 按照映射文件中的顺序排序
                comp_list_for_tier.sort(key=lambda x: self.comp_order_map.get(x.get('name', ''), 9999))

                tier_frame, tier_layout = self.create_tier_section(tier, comp_list_for_tier)
                self.tier_widgets[tier] = tier_frame # 存储 QFrame 引用
                self.content_layout.addWidget(tier_frame)
                displayed_comps_for_signal.extend([comp['name'] for comp in comp_list_for_tier]) # 添加到信号列表
            # 移除这个条件，因为我们已经重置了tier_widgets字典
            # elif tier in self.tier_widgets:
            #     self.tier_widgets[tier].setVisible(False) # 隐藏没有阵容的评级区

        # 如果没有任何阵容数据显示（例如搜索结果为空）
        if not displayed_comps_for_signal and not self.is_loading(): # 确保不是因为正在加载而为空
            no_results_label = self.content_layout.findChild(QLabel, "NoResultsLabel")
            if not no_results_label:
                no_results_label = QLabel("没有找到匹配的阵容。", alignment=Qt.AlignmentFlag.AlignCenter)
                no_results_label.setObjectName("NoResultsLabel") # 设置对象名以便查找
                no_results_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 13px; padding: 20px;")
                self.content_layout.addWidget(no_results_label)
            no_results_label.show()
        else:
            no_results_label = self.content_layout.findChild(QLabel, "NoResultsLabel")
            if no_results_label:
                no_results_label.hide()

        self.content_layout.addStretch(1) # 确保内容从上往下排列
        self.comps_displayed.emit(displayed_comps_for_signal) # 发出信号
        print(f"阵容列表UI更新完成，显示 {len(displayed_comps_for_signal)} 个阵容。")
        # 确保加载提示已隐藏，内容区已显示
        self.loading_label.hide()
        self.scroll_area.show()

    def create_tier_section(self, tier, comp_list):
        """创建并返回一个评级区域的 Widget"""
        tier_widget = QWidget()
        tier_layout = QHBoxLayout(tier_widget)
        tier_layout.setContentsMargins(0, 0, 0, 0)
        tier_layout.setSpacing(0) # 标签和滚动区域无间距

        # --- 评级标签 ---
        tier_label = QLabel(tier if tier != "DEFAULT" else "其他")
        tier_label.setFixedWidth(55)
        tier_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        tier_color = TIER_COLORS.get(tier, TIER_COLORS["DEFAULT"])
        tier_label.setStyleSheet(f"""
            QLabel {{
                background-color: {tier_color};
                color: {TEXT_COLOR_DARK};
                font-size: 22px;
                font-weight: bold;
                padding: 10px 0px;
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }}
        """)
        tier_layout.addWidget(tier_label)

        # --- 图标滚动区域 ---
        icons_scroll_area = WheelHorizontalScrollArea()
        icons_scroll_area.setFixedHeight(95)

        start_qcolor = QColor(tier_color)
        end_qcolor = QColor(WINDOW_BG_COLOR)
        mixed_r = int(end_qcolor.red() * 0.8 + start_qcolor.red() * 0.2)
        mixed_g = int(end_qcolor.green() * 0.8 + start_qcolor.green() * 0.2)
        mixed_b = int(end_qcolor.blue() * 0.8 + start_qcolor.blue() * 0.2)
        gradient_end_color = QColor(mixed_r, mixed_g, mixed_b).name()

        icons_scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0,
                                                stop:0 {tier_color}, stop:0.8 {gradient_end_color});
                border: 1px solid {tier_color};
                border-left: none;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }}
            QWidget {{ background-color: transparent; }}
        """)

        self.populate_comp_icons(icons_scroll_area, comp_list)

        tier_layout.addWidget(icons_scroll_area)
        return tier_widget, tier_layout

    def populate_comp_icons(self, scroll_area, comp_list):
        """向滚动区域填充阵容图标"""
        icons_content_widget = QWidget()
        icons_content_layout = QHBoxLayout(icons_content_widget)
        icons_content_layout.setContentsMargins(10, 5, 10, 5)
        icons_content_layout.setSpacing(8)
        icons_content_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # 需要英雄关联数据才能创建图标
        if not self.comp_hero_map:
             loading_label = QLabel("...") # 或者其他提示
             loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM};")
             icons_content_layout.addWidget(loading_label)
        else:
            for comp_detail in comp_list:
                comp_name = comp_detail.get('name')
                if comp_name:
                    heroes = self.comp_hero_map.get(comp_name, []) # 可能还未加载
                    comp_icon = CompIconWidget(comp_name, heroes)
                    comp_icon.comp_clicked.connect(self.comp_selected)
                    icons_content_layout.addWidget(comp_icon)

        icons_content_layout.addStretch(1)
        scroll_area.setWidget(icons_content_widget)

    def release_resources(self):
        """释放视图占用的资源，减少内存占用"""
        print(f"释放阵容列表视图的资源")
        
        # 停止任何可能的异步任务
        self._is_loading_base = False
        self._is_loading_heroes = False
        self._is_loading_traits = False
        
        # 清理内容区域中的所有图标
        if hasattr(self, 'content_layout'):
            while self.content_layout.count() > 0:
                item = self.content_layout.takeAt(0)
                widget = item.widget()
                if widget: 
                    # 递归查找并清理IconLabel
                    self._clear_pixmaps_recursive(widget)
                    widget.deleteLater()
        
        # 清理tier_widgets字典
        self.tier_widgets = {}
        
        # 可选：减少数据占用，但保留基础数据以便快速恢复
        if hasattr(self, 'filtered_comp_data'):
            self.filtered_comp_data = {}
            
    def load_data_if_needed(self):
        """如果数据尚未加载，则加载数据（用于延迟加载）"""
        if (not hasattr(self, 'comp_data') or not self.comp_data) and not self.is_loading():
            self.load_data()
        else:
            # 如果数据已加载，确保UI在正确状态
            if self.loading_label and not self.loading_label.isHidden():
                self.loading_label.hide()
            if self.scroll_area and self.scroll_area.isHidden():
                self.scroll_area.show()
                self.update_ui()  # 刷新UI显示

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置阵容列表视图到初始状态")
        
        # 重置搜索框
        if hasattr(self, 'search_box') and self.search_box:
            self.search_box.clear()  # 这会触发搜索信号，自动重新筛选和显示
        
        # 重置滚动位置（如果滚动区域可见）
        if hasattr(self, 'scroll_area') and self.scroll_area and self.scroll_area.isVisible():
            self.scroll_area.verticalScrollBar().setValue(0)

    def _clear_pixmaps_recursive(self, widget):
        """递归清理Widget中的所有IconLabel的图像缓存"""
        # 从自定义组件模块导入IconLabel
        from 自定义组件 import IconLabel
        
        # 查找所有IconLabel并清除其pixmap
        for child in widget.findChildren(IconLabel):
            if hasattr(child, 'clearPixmap'):
                try:
                    child.clearPixmap()
                except Exception as e:
                    print(f"清理IconLabel时出错: {e}")