# -*- coding: utf-8 -*-
"""
OCR核心处理模块

本文件封装了所有与OCR（光学字符识别）直接相关的操作。
包括Tesseract引擎的初始化、图像截图、以及针对不同场景（海克斯、装备）的文本识别流程。
"""
import os
import threading
from logging.handlers import RotatingFileHandler
from concurrent.futures import ThreadPoolExecutor, as_completed
from tkinter import messagebox
import time
import logging
import traceback

from PIL import ImageGrab, ImageEnhance
from rapidocr_onnxruntime import RapidOCR
import cv2
import numpy as np

import config
import utils

# --- RapidOCR 引擎初始化 ---
def initialize_ocr_engine():
    """初始化OCR引擎并记录详细信息"""
    global ocr_engine
    
    # 获取日志系统实例
    from logger_setup import get_logging_system
    logging_system = get_logging_system()
    
    try:
        logging_system.log_ocr_engine_initialization('RapidOCR', 'starting')
        
        # RapidOCR会自动处理模型文件，无需手动配置路径
        start_time = time.time()
        ocr_engine = RapidOCR()
        init_duration = time.time() - start_time
        
        logging_system.log_ocr_engine_initialization(
            'RapidOCR', 
            'success', 
            f"初始化耗时: {init_duration:.3f}s"
        )
        
        return True
        
    except Exception as e:
        logging_system.log_ocr_engine_initialization('RapidOCR', 'failed', str(e))
        messagebox.showerror("OCR引擎错误", f"无法初始化RapidOCR引擎: {e}")
        ocr_engine = None
        return False

# 初始化OCR引擎
ocr_engine = None
initialize_ocr_engine()

# --- OCR 日志记录器 ---
# 所有的配置都在 logger_setup.py 中完成。这里只获取实例。
ocr_logger = logging.getLogger('OCR_DEBUG')




# --- 核心OCR功能函数 ---

def capture_screen(region):
    """
    根据指定区域进行截图。
    
    Args:
        region (tuple): 截图区域的(x1, y1, x2, y2)坐标元组。
        
    Returns:
        Image: PIL图像对象，如果截图失败则返回None。
    """
    return ImageGrab.grab(bbox=region)

def ocr_recognize_region(img, strategy='default', psm=None):
    """
    [重构] 对单个图像区域进行OCR识别 (使用RapidOCR)。
    """
    if not ocr_engine:
        ocr_logger.error("RapidOCR引擎未初始化，无法识别。")
        return None
    
    # 获取日志系统实例
    from logger_setup import get_logging_system
    logging_system = get_logging_system()
        
    if img:
        start_time = time.time()
        
        # 记录图像预处理
        logging_system.log_ocr_processing('preprocess', {'strategy': strategy})
        
        # [v4.2 修复] 针对轮次识别，使用原始图像，避免预处理抹除文字特征
        if strategy == 'round':
            processed = img
        else:
            processed = utils.preprocess_image(img, strategy=strategy)
        
        try:
            # RapidOCR需要numpy数组格式的图像
            img_np = np.array(processed)

            # [v4.1 修复] 针对轮次识别，使用白名单功能，而不是切换模型
            kwargs = {}
            if strategy == 'round':
                # 为轮次识别创建一个只包含数字和'-'的白名单
                kwargs['char_dict'] = '0123456789-'
            
            # 调用RapidOCR进行识别，并传入可能的额外参数
            result, _ = ocr_engine(img_np, **kwargs)
            
            duration = time.time() - start_time
            
            if result is None:
                # 记录无结果的识别
                logging_system.log_ocr_processing(
                    'recognize', 
                    result={'text': '', 'strategy': strategy}, 
                    duration=duration
                )
                return ""
            
            # 拼接识别结果
            raw_text = " ".join([item[1] for item in result])
            cleaned = utils.clean_ocr_text(raw_text)
            
            # 记录成功的识别结果
            logging_system.log_ocr_processing(
                'recognize', 
                result={'text': cleaned, 'raw_text': raw_text, 'strategy': strategy}, 
                duration=duration
            )
            
            return cleaned
            
        except Exception as e:
            duration = time.time() - start_time
            
            # 使用新的错误日志记录方法
            logging_system.log_ocr_error(
                'recognize', 
                e, 
                {'strategy': strategy, 'duration': duration}
            )
            return None
    return None

def equip_preprocess_image(image):
    """[新增] 为装备名称OCR特别优化的图像预处理。"""
    if not image:
        return None
    # 转换为灰度图
    img_gray = image.convert('L')
    # 增强对比度
    img_contrast = ImageEnhance.Contrast(img_gray).enhance(1.5)
    # 二值化，阈值设定较为严格以突出文字
    img_binary = img_contrast.point(lambda x: 0 if x < 200 else 255)
    return img_binary

# --- [新增] 集成学习策略中使用的辅助函数 ---

def _count_contours_internal(image_cv, min_area=2000):
    """在给定的（已预处理的）图像上查找并计数轮廓（内部辅助函数）"""
    try:
        contours, _ = cv2.findContours(image_cv, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        valid_contours = sum(1 for contour in contours if cv2.contourArea(contour) > min_area)
        return valid_contours
    except Exception:
        return 0

def _strategy_hsv_base(bgr_image):
    """策略一 (基础HSV): 原始的HSV颜色掩码"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([15, 80, 80])
    upper_gold = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((5,5), np.uint8)
    return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)

def _strategy_hsv_wider_hue(bgr_image):
    """策略二 (HSV变种): 更宽的色相范围"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([10, 80, 80])
    upper_gold = np.array([40, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((5,5), np.uint8)
    return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)

def _strategy_hsv_more_closing(bgr_image):
    """策略三 (HSV变种): 更强的闭合操作"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([15, 80, 80])
    upper_gold = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((7,7), np.uint8)
    return cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=3)

def find_equipment_contours(image):
    """
    [重构] 使用三种HSV策略的"集成学习"方法，以确定装备的数量。
    通过"投票"选出最可能正确的数量，极大增强鲁棒性。

    Args:
        image (PIL.Image): 包含所有装备图标的大范围截图。

    Returns:
        int: 检测到的装备数量。
    """
    if not image:
        return 0

    try:
        bgr_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # 定义要并行执行的策略
        strategies = [
            _strategy_hsv_base,
            _strategy_hsv_wider_hue,
            _strategy_hsv_more_closing
        ]
        
        counts = []
        # （可选）如果未来性能需要，这里可以轻松改为多线程并行处理
        for strategy in strategies:
            processed_img = strategy(bgr_image)
            count = _count_contours_internal(processed_img)
            counts.append(count)

        # 使用Counter进行投票，找到出现次数最多的结果
        from collections import Counter
        if not counts:
            ocr_logger.warning("[Equip Counter] 所有策略均未返回结果。")
            return 0
        
        vote = Counter(counts).most_common(1)[0][0]
        
        # 只在结果发生变化或者策略结果不一致时记录详细信息
        if len(set(counts)) > 1:  # 策略结果不一致
            ocr_logger.debug(f"[Equip Counter] 策略结果不一致，投票结果: {counts} -> 最终数量: {vote}")
        else:
            # 结果一致时，只记录简要信息
            ocr_logger.debug(f"[Equip Counter] 检测到 {vote} 个装备")
        
        return vote

    except Exception as e:
        ocr_logger.error(f"Error in find_equipment_contours_ensemble: {e}", exc_info=True)
        return 0

def enhanced_ocr_recognize(image, hex_query, confidence_threshold):
    """
    对单张图片进行混合模式的增强OCR识别。
    1. 快速试探：首先使用最快的'default'策略。
    2. 深度识别：如果快速试探失败，则并行启动所有重量级策略。

    Args:
        image (Image): PIL图像对象。
        hex_query (EnhancedHextechQuery): 海克斯查询器实例。
        confidence_threshold (int): 成功识别的分数阈值。

    Returns:
        dict: 一个包含 'status' 和 'data' 的字典。
    """
    if not image:
        return {'status': 'NO_TEXT', 'data': None}

    # 获取日志系统实例
    from logger_setup import get_logging_system
    logging_system = get_logging_system()
    
    total_start_time = time.time()

    # --- 阶段一：快速试探 ---
    try:
        quick_start_time = time.time()
        # 直接调用ocr和匹配的核心逻辑
        text = ocr_recognize_region(image, strategy='default')
        if text:
            match, candidates, score = hex_query.fuzzy_match_whitelist(text, hex_query.all_names)
            duration = time.time() - quick_start_time
            
            # 记录置信度信息
            logging_system.log_ocr_processing(
                'recognize', 
                result={'text': match, 'strategy': 'default'}, 
                duration=duration, 
                confidence=f"{score:.1f}/{confidence_threshold}"
            )
            
            if score >= confidence_threshold:
                ocr_logger.info(f"[Enhanced OCR] 快速试探成功! 策略: 'default', 结果: '{match}', 分数: {score:.1f}/{confidence_threshold}, 耗时: {duration:.2f}s")
                return {'status': 'SUCCESS', 'data': {'match': match, 'candidates': candidates, 'score': score}}
    except Exception as e:
        logging_system.log_ocr_error('enhanced_quick_probe', e, {'strategy': 'default'})

    # --- 阶段二：深度并行识别 (如果快速试探失败) ---
    ocr_logger.debug("[Enhanced OCR] 快速试探失败，进入深度识别模式...")
    strategy_list = ['alt1', 'alt2', 'contrast'] # 排除已经试过的 'default'
    best_failure = {'score': -1, 'match': None, 'strategy': None}
    
    # 记录批量处理开始
    batch_start_time = time.time()
    success_count = 0
    
    with ThreadPoolExecutor(max_workers=len(strategy_list)) as executor:
        
        def ocr_and_match(strategy):
            """封装了'OCR->匹配->评分'的完整流程"""
            strategy_start_time = time.time()
            text = ocr_recognize_region(image, strategy=strategy)
            if text:
                match, candidates, score = hex_query.fuzzy_match_whitelist(text, hex_query.all_names)
                duration = time.time() - strategy_start_time
                return {'match': match, 'candidates': candidates, 'score': score, 'strategy': strategy, 'duration': duration}
            return None

        future_to_strategy = {
            executor.submit(ocr_and_match, strategy): strategy 
            for strategy in strategy_list
        }

        for future in as_completed(future_to_strategy):
            try:
                result = future.result()
                if result:
                    success_count += 1
                    
                    # 记录每个策略的结果
                    logging_system.log_ocr_processing(
                        'recognize', 
                        result={'text': result['match'], 'strategy': result['strategy']}, 
                        duration=result['duration'], 
                        confidence=f"{result['score']:.1f}/{confidence_threshold}"
                    )
                    
                    if result['score'] >= confidence_threshold:
                        ocr_logger.info(f"[Enhanced OCR] 深度识别成功! 策略: '{result['strategy']}', 结果: '{result['match']}', 分数: {result['score']:.1f}/{confidence_threshold}, 耗时: {result['duration']:.2f}s")
                        
                        # 取消其他未完成的任务
                        for f in future_to_strategy:
                            if not f.done():
                                f.cancel()
                        
                        return {'status': 'SUCCESS', 'data': result}
                    
                    elif result['score'] > best_failure['score']:
                        best_failure = result

            except Exception as e:
                strategy = future_to_strategy[future]
                logging_system.log_ocr_error('enhanced_deep_recognition', e, {'strategy': strategy})

    # 记录批量处理性能
    batch_duration = time.time() - batch_start_time
    logging_system.log_ocr_performance('batch_processing', {
        'total_time': batch_duration,
        'image_count': len(strategy_list),
        'success_count': success_count
    })

    if best_failure['score'] > -1:
        ocr_logger.info(f"[Enhanced OCR] 所有策略均失败. 最佳尝试: 策略 '{best_failure['strategy']}', 结果: '{best_failure['match']}', 最高分: {best_failure['score']:.1f}/{confidence_threshold}")
        return {'status': 'LOW_CONFIDENCE', 'data': best_failure}
    else:
        ocr_logger.info("[Enhanced OCR] 所有策略均未找到可识别文本。")
        return {'status': 'NO_TEXT', 'data': None}

def ocr_recognize(images, whitelist=None, psm=7, confidence_threshold=70, parallel=True):
    """
    [重构] 通用的OCR识别函数，增加了并行处理的开关。
    
    Args:
        images (list): 包含多个PIL图像对象的列表。
        whitelist (list, optional): 允许的文本列表。
        psm (int, optional): Tesseract的页面分割模式。
        confidence_threshold (int, optional): 成功识别的分数阈值。
        parallel (bool, optional): 是否并行处理。

    Returns:
        list: 包含每个区域识别结果的列表，保留原始顺序，失败的区域为None。
    """
    results = []
    scores = []
    # 使用ThreadPoolExecutor管理线程池，最大工作线程数为3
    with ThreadPoolExecutor(max_workers=3) as executor:
        # 提交所有OCR任务并建立future到索引的映射
        future_to_index = {executor.submit(ocr_recognize_region, img): i 
                          for i, img in enumerate(images)}
        
        # 按任务完成的顺序获取结果
        for future in as_completed(future_to_index):
            region_index = future_to_index[future]
            try:
                result = future.result()
                # 保证结果列表的长度足够，并按正确的索引位置插入结果
                while len(results) <= region_index:
                    results.append(None)
                results[region_index] = result
            except Exception as e:
                ocr_logger.error(f"处理区域 {region_index + 1} 时发生错误: {e}")
    
    # 返回包含所有区域结果的列表（不过滤None值）
    return results, scores

def hex_ocr_recognize(screen_width, screen_height):
    """
    专门用于海克斯识别的流程函数。
    它会根据配置的相对坐标截取三个区域的图像，并进行并行OCR。

    Args:
        screen_width (int): 屏幕宽度。
        screen_height (int): 屏幕高度。

    Returns:
        list: 只包含成功识别结果的文本列表。
    """
    images = []
    
    # 一次性获取所有区域的截图
    for rel_region in config.SCREENSHOT_REGIONS_RELATIVE:
        # 将相对坐标转换为屏幕绝对坐标
        x1, y1, x2, y2 = [int(coord * (screen_width if i % 2 == 0 else screen_height)) 
                         for i, coord in enumerate(rel_region)]
        img = capture_screen((x1, y1, x2, y2))
        if img:
            images.append(img)
    
    results = []
    # 使用线程池并行处理截图
    with ThreadPoolExecutor(max_workers=3) as executor:
        future_to_index = {executor.submit(ocr_recognize_region, img): i 
                          for i, img in enumerate(images)}
        
        for future in as_completed(future_to_index):
            region_index = future_to_index[future]
            try:
                result = future.result()
                if result:
                    # 保证结果的顺序
                    while len(results) <= region_index:
                        results.append(None)
                    results[region_index] = result
            except Exception as e:
                ocr_logger.error(f"处理区域 {region_index + 1} 时发生错误: {e}")
    
    # 过滤掉None值，只返回成功识别的结果
    return [result for result in results if result is not None]

def equip_ocr_recognize(screen_width, screen_height):
    """
    专门用于装备识别的流程函数。
    它会截取装备栏的图像，进行预处理后识别出所有装备名称。

    Args:
        screen_width (int): 屏幕宽度。
        screen_height (int): 屏幕高度。

    Returns:
        list: 清理后的装备名称列表。
    """
    try:
        # 将相对坐标转换为屏幕绝对坐标
        x1, y1, x2, y2 = [int(coord * (screen_width if i % 2 == 0 else screen_height)) 
                         for i, coord in enumerate(config.EQUIP_SCREENSHOT_REGION_RELATIVE)]
        ocr_logger.debug(f"装备OCR截图区域: ({x1}, {y1}, {x2}, {y2}), 相对区域: {config.EQUIP_SCREENSHOT_REGION_RELATIVE}")
        
        img = capture_screen((x1, y1, x2, y2))
        if img:
            # 使用装备专用的图像预处理函数
            # [v3.0 迁移] 使用RapidOCR替换Tesseract
            processed = equip_preprocess_image(img)
            
            # 直接调用RapidOCR引擎
            img_np = np.array(processed)
            result, _ = ocr_engine(img_np)
            
            if result is None:
                raw_text = ""
            else:
                # 拼接所有识别到的文本片段
                raw_text = " ".join([item[1] for item in result])
            
            ocr_logger.debug(f"装备原始OCR结果 (RapidOCR): [{raw_text}]")

            # 复用现有的文本分割和清理逻辑
            text_parts = utils.split_text_by_spaces(raw_text)
            ocr_logger.debug(f"装备分割后的文本: {text_parts}")
            
            cleaned_parts = [utils.clean_text(part) for part in text_parts]
            cleaned_parts = [part for part in cleaned_parts if part]  # 移除空字符串
            
            ocr_logger.debug(f"装备清理后的文本段: {cleaned_parts}")
            return cleaned_parts
        else:
            return []
    except Exception as e:
        ocr_logger.error(f"装备OCR识别错误: {e}", exc_info=True)
        return [] 