# 下载链接稳定化设置指南

本文档旨在指导您如何通过“域名 + ECS + 对象存储”的黄金组合，为您的应用 `弈秒决` 建立一个稳定、灵活且面向未来的更新系统。

## 核心思想

我们将利用您长期持有的域名 `yuxianglei.com` 作为客户端中唯一不变的“路标”，而可能会变化的ECS服务器和对象存储服务，则通过这个“路标”动态获取，从而实现“以不变应万变”。

## 实施步骤

### 第1步：域名解析 (DNS设置)

这是将您的域名和您的ECS服务器关联起来的关键一步。

1.  **登录您的域名提供商控制台** (例如阿里云、腾讯云、GoDaddy等)。
2.  找到 `yuxianglei.com` 这个域名的DNS解析设置。
3.  **添加一条新的A记录**：
    *   **主机记录 (Host)**: 填入 `updater` (这样您的访问地址就是 `updater.yuxianglei.com`)。
    *   **记录类型 (Type)**: 选择 `A`。
    *   **记录值 (Value)**: 填入您当前**阿里云ECS服务器的公网IP地址**。
    *   **TTL**: 可以使用默认值 (例如10分钟)。
4.  保存设置。DNS解析通常需要几分钟到几小时生效。生效后，`updater.yuxianglei.com` 就会指向您的ECS服务器。

**未来操作**: 当您更换ECS服务器后，只需回到这里，将A记录的**记录值**修改为**新服务器的IP地址**即可。

---

### 第2步：在ECS上配置静态Web服务

您的ECS需要能够响应来自 `http://updater.yuxianglei.com/yimiaojueconfig.json` 的请求，并返回一个JSON文件的内容。最简单的方式是使用Nginx。

**假设您的ECS是Linux系统 (例如CentOS, Ubuntu):**

1.  **安装Nginx** (如果尚未安装):
    ```bash
    # 对于Ubuntu/Debian
    sudo apt update
    sudo apt install nginx

    # 对于CentOS
    sudo yum install epel-release
    sudo yum install nginx
    ```

2.  **创建存放配置文件的目录**:
    ```bash
    sudo mkdir -p /var/www/updater
    ```

3.  **创建主配置文件 `yimiaojue_config.json`**:
    ```bash
    sudo nano /var/www/updater/yimiaojue_config.json
    ```
    将以下内容粘贴进去。**请注意**: `YOUR_QINIU_BUCKET_DOMAIN` 是一个占位符，您需要将其替换为七牛云提供的**临时测试域名**或备案通过后的**正式域名** (`download.yuxianglei.com`)。
    ```json
    {
      "release": {
        "version_url": "https://YOUR_QINIU_BUCKET_DOMAIN/version.json",
        "download_base_url": "https://YOUR_QINIU_BUCKET_DOMAIN/"
      },
      "beta": {
        "version_url": "https://YOUR_QINIU_BUCKET_DOMAIN/version-beta.json",
        "download_base_url": "https://YOUR_QINIU_BUCKET_DOMAIN/"
      }
    }
    ```
    保存并退出 (在nano中是 `Ctrl+X`, `Y`, `Enter`)。

4.  **配置Nginx**:
    *   创建一个新的Nginx配置文件:
        ```bash
        sudo nano /etc/nginx/conf.d/updater.conf
        ```
    *   将以下配置粘贴进去：
        ```nginx
        server {
            listen 80;
            server_name updater.yuxianglei.com;

            root /var/www/updater;
            index index.html;

            location / {
                try_files $uri $uri/ =404;
            }
        }
        ```
    *   保存并退出。

5.  **启动并测试Nginx**:
    ```bash
    sudo systemctl start nginx
    sudo systemctl enable nginx # 设置开机自启
    sudo nginx -t # 测试配置是否正确
    sudo systemctl reload nginx # 重载配置使其生效
    ```

6.  **验证**: 在您自己的电脑上访问 `http://updater.yuxianglei.com/yimiaojue_config.json`，如果能看到您刚才配置的JSON内容，说明ECS部分已成功！
    *   **注意**: 在ICP备案完成前，直接访问可能会被拦截。您可以在本地电脑的 `hosts` 文件中添加 `[您的ECS公网IP] updater.yuxianglei.com` 来绕过DNS拦截进行测试。

**未来操作**: 当您更换对象存储服务（例如从七牛云换到阿里云OSS）后，只需登录ECS，修改 `/var/www/updater/yimiao_jue_config.json` 文件中的URL地址即可。

---

### 第3步：设置环境变量 (本地开发和打包环境)

为了安全，您的七牛云密钥不应写在代码里。

1.  打开Windows的“编辑系统环境变量”。
2.  在“系统变量”中，点击“新建”。
3.  **新建第一个变量**:
    *   **变量名**: `QINIU_ACCESS_KEY`
    *   **变量值**: 粘贴您的Access Key (AK)
4.  **新建第二个变量**:
    *   **变量名**: `QINIU_SECRET_KEY`
    *   **变量值**: 粘贴您的Secret Key (SK)
5.  点击确定保存。**您需要重启您的开发工具（如VS Code）和命令行终端，新的环境变量才会生效。**

---

### 第4步：代码修改 (由AI完成)

我将为您完成以下代码的修改：

*   **`updater.py` / `updater_main.py`**:
    *   硬编码的URL将改为 `http://updater.yuxianglei.com/yimiaojue_config.json`。
    *   程序启动时，会先请求这个URL获取主配置。
    *   然后根据本地 `update_config.json` 的渠道设置，从主配置中选取正确的 `version_url` 进行后续操作。
*   **`upload_tool.py`**:
    *   将使用七牛云Python SDK进行上传。
    *   会从环境变量中读取 `QINIU_ACCESS_KEY` 和 `QINIU_SECRET_KEY`。
    *   会需要您手动填入或从主配置中读取七牛云的域名和存储空间名。

完成以上步骤后，您的更新系统将变得前所未有的稳定和灵活。

现在只差域名icp备案完成，在七牛里绑定，最后修改三个up相关的代码了