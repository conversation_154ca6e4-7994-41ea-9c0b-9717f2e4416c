# 视图模块/英雄详情视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, QFrame,
                               QPushButton, QTabWidget, QSizePolicy, QSpacerItem)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QIcon, QPixmap
from PySide6.QtWidgets import QStyle # <--- 从 QtWidgets 导入 QStyle

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_HIGHLIGHT,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ITEM_ROW_BG_HOVER,
                       ICON_SIZE_XLARGE, ICON_SIZE_LARGE, ICON_SIZE_MEDIUM, SECTION_BG_COLOR,
                       CONTROL_BAR_BG, TAB_STYLE, BUTTON_STYLE_BASE,
                       FONT_SIZE_XLARGE, FONT_SIZE_LARGE, FONT_SIZE_MEDIUM, FONT_SIZE_NORMAL, FONT_SIZE_SMALL,
                       LINE_EDIT_STYLE, BUTTON_BG) # 引入 LINE_EDIT_STYLE 和 BUTTON_BG
from 自定义组件 import IconLabel, ClickableLabel, SearchLineEdit # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

# --- 定义装备列表使用的图标大小 ---
EQUIP_LIST_ICON_SIZE = ICON_SIZE_LARGE # 使用较大的图标尺寸

# --- 用于装备推荐列表的内部 Widget ---
class EquipRecommendListWidget(QWidget):
    """显示特定装备数量推荐列表的内部控件"""
    item_selected = Signal(str) # 点击装备图标/名称时发出
    magnify_requested = Signal(list) # 点击放大镜时发出，参数为当前装备名称列表

    def __init__(self, item_count, parent=None):
        super().__init__(parent)
        self.item_count = item_count # 1, 2, or 3
        self.all_equips_data = [] # 存储该数量下的所有装备组合数据
        self.filtered_equips_data = [] # 存储筛选和排序后的数据
        self.current_sort_column = 'play_rate' # 默认排序
        self.current_sort_order = Qt.SortOrder.DescendingOrder

        self.header_widgets = {} # 存储表头

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5) # 列表内边距
        layout.setSpacing(5)

        # --- 搜索框区域 (带图标) ---
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(5) # 图标和输入框间距

        # 创建搜索图标 Label
        search_icon_label = QLabel()
        # 使用 Qt 内置的搜索图标，需要转换为 QPixmap 并设置
        search_icon = self.style().standardIcon(QStyle.StandardPixmap.SP_FileDialogContentsView)
        search_icon_pixmap = search_icon.pixmap(QSize(16, 16)) # 设置图标大小
        search_icon_label.setPixmap(search_icon_pixmap)
        search_icon_label.setFixedSize(18, 18) # 固定标签大小略大于图标
        search_icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 创建搜索框
        self.search_box = SearchLineEdit(placeholder_text=f"搜索{self.item_count}件套装备...")
        # 确保使用常量中的浅色字体和合适的字号
        # 继承基础 LINE_EDIT_STYLE 并覆盖特定属性
        search_box_style = LINE_EDIT_STYLE + f" QLineEdit {{ color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px; }}"
        self.search_box.setStyleSheet(search_box_style)
        self.search_box.searchChanged.connect(self.filter_equips)

        # 将图标和搜索框添加到水平布局
        search_layout.addWidget(search_icon_label)
        search_layout.addWidget(self.search_box, 1) # 让搜索框伸展

        # 将搜索布局添加到主垂直布局
        layout.addLayout(search_layout)

        # --- 新增：固定表头区域 ---
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet(f"background-color: {CONTROL_BAR_BG}; border: 1px solid {BORDER_COLOR}; border-radius: 4px;")
        self.header_frame.setFixedHeight(EQUIP_LIST_ICON_SIZE + 18)  # 固定表头高度，适应图标大小
        self.header_layout = QVBoxLayout(self.header_frame)
        self.header_layout.setContentsMargins(0, 0, 0, 0)
        self.header_layout.setSpacing(0)
        layout.addWidget(self.header_frame)
        self.header_frame.hide()  # 初始隐藏，等数据加载完成后显示

        # --- 列表滚动区域 ---
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE) # 基本滚动条样式
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(1) # 行间距

        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area, 1)

        # 初始加载提示 (由父视图控制)
        # self.loading_label = QLabel("加载中...", alignment=Qt.AlignmentFlag.AlignCenter)
        # self.content_layout.addWidget(self.loading_label)

    def set_data(self, data_list):
        """设置并显示装备数据"""
        self.all_equips_data = data_list if data_list else []
        self.filter_equips("") # 触发初始排序和显示

    def filter_equips(self, search_term):
        """筛选装备列表"""
        search_term = search_term.strip().lower()
        if not search_term:
            self.filtered_equips_data = list(self.all_equips_data)
        else:
            # 对搜索词进行分词处理，支持多个装备名称的搜索
            search_terms = search_term.split()
            
            # 使用更精确的筛选方法
            self.filtered_equips_data = []
            for equip in self.all_equips_data:
                item_names = equip.get('item_names', '').lower()
                # 要求所有搜索词都在装备名称中出现
                if all(term in item_names for term in search_terms):
                    self.filtered_equips_data.append(equip)
                    
            print(f"搜索 '{search_term}' 找到 {len(self.filtered_equips_data)} 个匹配装备")
        
        self.sort_equips()

    def sort_equips(self):
        """排序装备列表"""
        try:
            self.filtered_equips_data.sort(
                key=lambda x: x.get(self.current_sort_column) if x.get(self.current_sort_column) is not None else (float('-inf') if self.current_sort_order == Qt.SortOrder.DescendingOrder else float('inf')),
                reverse=(self.current_sort_order == Qt.SortOrder.DescendingOrder)
            )
        except Exception as e:
            print(f"装备推荐排序错误: {e}")
        self.update_ui()

    @Slot(str)
    def handle_sort_request(self, column_key):
        """处理表头点击"""
        print(f"收到装备推荐列表排序请求: {column_key}") # Debug
        if self.current_sort_column == column_key:
            self.current_sort_order = Qt.SortOrder.AscendingOrder if self.current_sort_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
        else:
            self.current_sort_column = column_key
            # --- 修改开始：设置各列默认排序 ---
            # "好的数据"排在前面
            if column_key == 'avg_place':
                # 平均排名：越小越好 -> 升序
                self.current_sort_order = Qt.SortOrder.AscendingOrder
            else:
                # 出场率、前四率、登顶率：越大越好 -> 降序
                self.current_sort_order = Qt.SortOrder.DescendingOrder
            # --- 修改结束 ---
        self.update_header_styles()
        self.sort_equips()

    def update_header_styles(self):
        """更新表头样式"""
        sort_indicator = " ▼" if self.current_sort_order == Qt.SortOrder.DescendingOrder else " ▲"
        # 增大表头字体
        base_style = f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: bold;"
        selected_style = f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: bold;"

        for key, label in self.header_widgets.items():
            text = label.property("original_text")
            if key == self.current_sort_column:
                label.setStyleSheet(selected_style)
                label.setText(text + sort_indicator)
            else:
                label.setStyleSheet(base_style)
                label.setText(text)

    def update_ui(self):
        """更新列表显示"""
        # 清空滚动区域内容（不再包含表头）
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()
        self.header_widgets = {}

        # --- 更新固定表头 ---
        if self.all_equips_data:  # 只有在有数据时才显示表头
            self.header_frame.show()
            self.update_header()
        else:
            self.header_frame.hide()

        # --- 添加数据行 ---
        if not self.filtered_equips_data:
            status = "无数据" if not self.all_equips_data else "未找到匹配的装备组合"
            label = QLabel(status, alignment=Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; padding: 15px;")
            self.content_layout.addWidget(label)
        else:
            for equip_data in self.filtered_equips_data:
                row = self.create_equip_row_widget(equip_data)
                if row: self.content_layout.addWidget(row)

        self.content_layout.addSpacerItem(QSpacerItem(1, 1, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

    def update_header(self):
        """更新固定表头"""
        # 清空现有表头内容
        while self.header_layout.count():
            item = self.header_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 创建新的表头
        header_widget = self.create_equip_row_widget(is_header=True)
        if header_widget:
            self.header_layout.addWidget(header_widget)
            # 更新表头样式
            self.update_header_styles()

    def create_equip_row_widget(self, equip_data=None, is_header=False):
        """创建装备推荐列表中的一行"""
        row_widget = QWidget()
        # 增加行高以适应更大的图标和字体
        row_widget.setMinimumHeight(EQUIP_LIST_ICON_SIZE + 14) # 图标高度 + padding
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(8, 5, 8, 5) # 增加垂直边距
        row_layout.setSpacing(8) # 稍微增加列间距

        # --- 列定义 ---
        col_keys = ['icons', 'play_rate', 'avg_place', 'top4_rate', 'top1_rate', 'actions']
        # 调整宽度以适应更大的字体
        col_widths = {
            'icons': -1, # 自动伸展 (包含图标和名称)
            'play_rate': 70,
            'avg_place': 70,
            'top4_rate': 70,
            'top1_rate': 70,
            'actions': 40 # 按钮区域宽度稍大
        }
        col_alignments = {
            'icons': Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter,
            'play_rate': Qt.AlignmentFlag.AlignCenter,
            'avg_place': Qt.AlignmentFlag.AlignCenter,
            'top4_rate': Qt.AlignmentFlag.AlignCenter,
            'top1_rate': Qt.AlignmentFlag.AlignCenter,
            'actions': Qt.AlignmentFlag.AlignCenter
        }
        col_titles = {
            'icons': '装备',
            'play_rate': '出场率',
            'avg_place': '平均排名', # 使用平均排名
            'top4_rate': '前四率',
            'top1_rate': '登顶率',
            'actions': '' # 操作列无标题
        }

        # --- 创建列控件 ---
        col_widgets = {}
        for key in col_keys:
            if is_header and key not in ['icons', 'actions']: # 图标和操作列不可排序
                label = ClickableLabel(data=key)
                label.clicked.connect(self.handle_sort_request)
                label.setProperty("original_text", col_titles[key])
                self.header_widgets[key] = label
            elif key == 'icons': # 图标列需要特殊布局
                 label = QWidget() # 使用 QWidget 作为容器
                 icon_layout = QHBoxLayout(label)
                 icon_layout.setContentsMargins(0,0,0,0)
                 icon_layout.setSpacing(4)
                 icon_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
                 label.setProperty("layout", icon_layout) # 存储布局引用
                 if is_header: # 表头的文本
                     header_text_label = QLabel(col_titles['icons'])
                     # 使用与其它表头一致的字体大小
                     header_text_label.setStyleSheet(f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: bold;")
                     icon_layout.addWidget(header_text_label)

            elif key == 'actions': # 操作列
                 label = QWidget() # 容器
                 action_layout = QHBoxLayout(label)
                 action_layout.setContentsMargins(0,0,0,0)
                 action_layout.setSpacing(0)
                 action_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                 label.setProperty("layout", action_layout)
            else: # 普通文本列
                label = QLabel()

            col_widgets[key] = label
            # 只有QLabel和子类才有setAlignment方法，QWidget没有
            if hasattr(label, 'setAlignment'):
                label.setAlignment(col_alignments[key])
            if col_widths[key] > 0:
                label.setFixedWidth(col_widths[key])

            row_layout.addWidget(label, 1 if col_widths[key] == -1 else 0) # 图标列伸展

        # --- 填充内容 ---
        if is_header:
            pass # 不再需要在这里设置，由 update_header_styles 处理
        elif equip_data:
            row_widget.enterEvent = lambda event, w=row_widget: w.setStyleSheet(f"background-color: {ITEM_ROW_BG_HOVER}; border-radius: 4px;")
            row_widget.leaveEvent = lambda event, w=row_widget: w.setStyleSheet("background-color: transparent;")

            item_names_str = equip_data.get('item_names', '')
            item_names_list = item_names_str.split('|') if item_names_str else []

            # 填充图标列
            icon_container_widget = col_widgets['icons']
            icon_layout = icon_container_widget.property("layout")
            # 清空旧图标 (如果需要)
            while icon_layout.count():
                child = icon_layout.takeAt(0)
                if child.widget(): child.widget().deleteLater()

            for i, item_name in enumerate(item_names_list):
                if not item_name: continue
                # 数据库查询装备图标路径 (这里简化，假设 equip_data 包含 icon_paths)
                # 实际应用中可能需要异步查询或预加载
                icon_path = equip_data.get('icon_paths', {}).get(item_name) # 假设有 icon_paths 字典

                # 使用更大的图标尺寸常量
                icon_label = IconLabel(data=item_name, icon_size=EQUIP_LIST_ICON_SIZE, icon_type='item')
                icon_label.set_icon(icon_path, item_name)
                icon_label.setToolTip(item_name)
                icon_label.clicked.connect(lambda data: self.item_selected.emit(data)) # 添加lambda适配器
                icon_layout.addWidget(icon_label)

            if self.item_count == 1 and item_names_list: # 单件套显示名称
                name_label = ClickableLabel(data=item_names_list[0])
                name_label.setText(item_names_list[0])
                # 增大名称字体
                name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px; margin-left: 5px;")
                name_label.clicked.connect(lambda data: self.item_selected.emit(data)) # 添加lambda适配器
                icon_layout.addWidget(name_label)
            icon_layout.addStretch()


            # 填充统计数据
            # 增大统计数据字体
            value_style = f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px;"
            # 装备出场率
            play_rate = equip_data.get('play_rate')
            if play_rate is not None:
                col_widgets['play_rate'].setText(f"{play_rate:.1f}%")
            else:
                col_widgets['play_rate'].setText("--")
            col_widgets['play_rate'].setStyleSheet(value_style)

            avg_place = equip_data.get('avg_place')
            col_widgets['avg_place'].setText(f"{avg_place:.2f}" if avg_place is not None else "--")
            col_widgets['avg_place'].setStyleSheet(value_style)

            top4_rate = equip_data.get('top4_rate')
            if top4_rate is not None:
                # 前四率需要放大10倍
                top4_rate = top4_rate * 10
                # 判断数据范围
                if top4_rate > 1:
                    col_widgets['top4_rate'].setText(f"{top4_rate:.1f}%")
                else:
                    col_widgets['top4_rate'].setText(f"{top4_rate:.1%}")
            else:
                col_widgets['top4_rate'].setText("--")
            col_widgets['top4_rate'].setStyleSheet(value_style)

            top1_rate = equip_data.get('top1_rate')
            col_widgets['top1_rate'].setText(f"{top1_rate:.1f}%" if top1_rate is not None else "--")
            col_widgets['top1_rate'].setStyleSheet(value_style)

            # 填充操作列 (放大镜按钮)
            if self.item_count < 3: # 只有1件和2件套需要放大镜
                action_container_widget = col_widgets['actions']
                action_layout = action_container_widget.property("layout")
                magnify_button = QPushButton()
                # 尝试获取稍微清晰一点的标准图标
                mag_icon = self.style().standardIcon(getattr(QStyle, 'SP_CommandLink', QStyle.StandardPixmap.SP_ArrowForward))
                magnify_button.setIcon(mag_icon)
                # 增大图标和按钮尺寸
                magnify_button.setIconSize(QSize(16, 16))
                magnify_button.setFixedSize(24, 24)
                magnify_button.setCursor(Qt.CursorShape.PointingHandCursor)
                magnify_button.setStyleSheet(BUTTON_STYLE_BASE + "QPushButton { padding: 2px; }") # 减小 padding
                magnify_button.setToolTip(f"查找包含这些装备的{self.item_count+1}件套")
                # 使用 lambda 捕获当前行的装备列表
                magnify_button.clicked.connect(lambda checked=False, items=list(item_names_list): self.magnify_requested.emit(items))
                action_layout.addWidget(magnify_button)

        return row_widget


# --- 主视图 ---
class 英雄详情视图(QWidget):
    """显示英雄详细信息的视图"""
    item_selected = Signal(str) # 点击装备时发出信号
    # hero_selected = Signal(str) # 暂不需要，因为是从英雄列表进入的

    def __init__(self, parent=None):
        super().__init__(parent)
        self.hero_name = ""
        self.hero_base_data = {} # 英雄基础信息
        self.hero_equip_stats = {1: [], 2: [], 3: []} # 按数量存储装备统计
        self.item_icon_cache = {} # 缓存装备图标路径

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(10)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 顶部英雄信息区域 ---
        self.hero_info_frame = QFrame()
        self.hero_info_frame.setStyleSheet(f"background-color: {SECTION_BG_COLOR}; border-radius: 6px; padding: 10px;")
        hero_info_layout = QHBoxLayout(self.hero_info_frame)
        hero_info_layout.setContentsMargins(5, 5, 5, 5)
        hero_info_layout.setSpacing(15)

        self.hero_icon_label = IconLabel(icon_size=ICON_SIZE_XLARGE, icon_type='hero', placeholder_text='?')
        hero_info_layout.addWidget(self.hero_icon_label)

        # --- 右侧信息区 (垂直布局) ---
        hero_detail_layout = QVBoxLayout()
        hero_detail_layout.setSpacing(8) # 调整垂直间距

        # --- 名称和费用行 (水平布局) ---
        name_cost_layout = QHBoxLayout()
        name_cost_layout.setContentsMargins(0, 0, 0, 0)
        name_cost_layout.setSpacing(8) # 名称和费用间距
        self.hero_name_label = QLabel("英雄名称")
        # 使用常量设置更大字体
        self.hero_name_label.setStyleSheet(f"font-size: {FONT_SIZE_XLARGE}px; font-weight: bold; color: {TEXT_COLOR_LIGHT};")
        self.hero_cost_label = QLabel("(?费)") # 稍微改变格式
        # 使用常量设置稍小字体，颜色区分
        self.hero_cost_label.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_NORMAL}px;")

        name_cost_layout.addWidget(self.hero_name_label)
        name_cost_layout.addWidget(self.hero_cost_label)
        name_cost_layout.addStretch(1) # 让名称和费用靠左

        # --- 基础统计数据行 (水平布局) ---
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(0, 0, 0, 0) # 移除边距，由父级控制
        stats_layout.setSpacing(9)
        self.play_rate_label = QLabel("出场率: --")
        self.avg_place_label = QLabel("平均排名: --")
        self.top4_rate_label = QLabel("前四率: --")
        self.top1_rate_label = QLabel("登顶率: --")
        # 使用常量增大字体，保持浅色
        stat_label_style = f"color:{TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px;"
        for label in [self.play_rate_label, self.avg_place_label, self.top4_rate_label, self.top1_rate_label]:
            label.setStyleSheet(stat_label_style)
        stats_layout.addWidget(self.play_rate_label)
        stats_layout.addWidget(self.avg_place_label)
        stats_layout.addWidget(self.top4_rate_label)
        stats_layout.addWidget(self.top1_rate_label)
        stats_layout.addStretch(1) # 让统计数据靠左

        # --- 将名称费用行和统计数据行添加到垂直布局 ---
        hero_detail_layout.addLayout(name_cost_layout)
        hero_detail_layout.addLayout(stats_layout)

        # --- 将垂直布局添加到主水平布局 ---
        hero_info_layout.addLayout(hero_detail_layout, 1) # 让右侧区域伸展
        # 移除全局的 addStretch，因为内部布局已经控制了对齐
        # hero_info_layout.addStretch()
        self.main_layout.addWidget(self.hero_info_frame)

        # --- 装备推荐 Tabs ---
        self.equip_tabs = QTabWidget()
        self.equip_tabs.setStyleSheet(TAB_STYLE)

        # 创建 Tab 内容 Widget
        self.tab_widgets = {
            1: EquipRecommendListWidget(item_count=1),
            2: EquipRecommendListWidget(item_count=2),
            3: EquipRecommendListWidget(item_count=3)
        }

        # 添加 Tabs
        self.equip_tabs.addTab(self.tab_widgets[1], "装备")
        self.equip_tabs.addTab(self.tab_widgets[2], "双装备组合")
        self.equip_tabs.addTab(self.tab_widgets[3], "三装备组合")

        # 连接信号
        for count, widget in self.tab_widgets.items():
            widget.item_selected.connect(self.item_selected) # 转发信号
            if count < 3: # 只有1件和2件套需要处理放大镜
                widget.magnify_requested.connect(self.handle_magnify_request)

        self.main_layout.addWidget(self.equip_tabs, 1) # Tab 区域占据剩余空间

    def load_hero_data(self, hero_name):
        """加载指定英雄的数据"""
        if not hero_name or self.hero_name == hero_name:
            return
        self.hero_name = hero_name
        print(f"开始加载英雄详情: {self.hero_name}")

        # 重置状态
        self.hero_base_data = {}
        self.hero_equip_stats = {1: [], 2: [], 3: []}
        self.item_icon_cache = {} # 清空装备图标缓存
        self.update_ui() # 清空旧数据并显示加载状态

        # 异步加载英雄基础信息
        sql_base = "SELECT cn_name, cost, icon_path, play_rate, avg_place, top4_rate, top1_rate FROM heroes WHERE cn_name = ?"
        query_key_base = f"hero_base_{hero_name}"
        execute_query_async(
            sql_base, (hero_name,),
            on_success=self.on_base_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key_base
        )

        # 异步加载英雄装备统计信息
        sql_equips = "SELECT item_count, item_names, play_rate, avg_place, top4_rate, top1_rate FROM hero_item_stats WHERE hero_cn_name = ?"
        query_key_equips = f"hero_equips_{hero_name}"
        execute_query_async(
            sql_equips, (hero_name,),
            on_success=self.on_equip_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key_equips
        )

        # 异步预加载所有装备图标路径 (优化)
        sql_item_icons = "SELECT name, icon_path FROM items"
        query_key_item_icons = "all_item_icons"
        execute_query_async(
            sql_item_icons,
            on_success=self.on_item_icons_loaded,
            on_error=lambda e: print(f"加载装备图标路径失败: {e}"), # 出错不影响主要流程
            query_key=query_key_item_icons
        )


    def on_base_data_loaded(self, results):
        """英雄基础信息加载成功"""
        if results:
            self.hero_base_data = results[0]
            self.update_hero_info_ui()
        else:
            print(f"警告: 未找到英雄 {self.hero_name} 的基础数据。")
            self.hero_name_label.setText(f"{self.hero_name} (未找到)")

    def on_equip_data_loaded(self, results):
        """英雄装备统计加载成功"""
        self.hero_equip_stats = {1: [], 2: [], 3: []} # 先清空
        if results:
            for row in results:
                count = row.get('item_count')
                if count in self.hero_equip_stats:
                    # 为装备组合添加图标路径信息 (需要 item_icon_cache 已填充)
                    item_names_list = row.get('item_names', '').split('|')
                    icon_paths = {name: self.item_icon_cache.get(name) for name in item_names_list if name in self.item_icon_cache}
                    row_dict = dict(row) # 将 sqlite3.Row 转为字典
                    row_dict['icon_paths'] = icon_paths # 添加图标路径字典
                    self.hero_equip_stats[count].append(row_dict)

        # 更新所有 Tab 的数据
        for count, widget in self.tab_widgets.items():
            widget.set_data(self.hero_equip_stats.get(count, []))

    def on_item_icons_loaded(self, results):
        """所有装备图标路径加载成功"""
        if results:
            self.item_icon_cache = {row['name']: row['icon_path'] for row in results}
            print(f"已缓存 {len(self.item_icon_cache)} 个装备图标路径。")
            # 如果装备数据已经加载，需要重新处理以添加图标路径
            if any(self.hero_equip_stats.values()):
                self.on_equip_data_loaded([item for sublist in self.hero_equip_stats.values() for item in sublist])


    def on_data_load_error(self, error_message):
        """数据加载失败"""
        print(f"加载英雄 {self.hero_name} 数据失败: {error_message}")
        # 可以在界面上显示错误提示，例如在 Tab 页内
        if not self.hero_base_data:
             self.hero_name_label.setText(f"{self.hero_name} (加载错误)")
        for widget in self.tab_widgets.values():
             widget.set_data([]) # 清空数据并显示无数据提示

    def update_ui(self):
        """更新整个视图 (通常在加载新英雄时调用)"""
        self.update_hero_info_ui()
        # 清空 Tab 数据
        for widget in self.tab_widgets.values():
            widget.set_data([])

    def update_hero_info_ui(self):
        """更新顶部英雄信息"""
        if not self.hero_base_data:
            # 设置默认或加载中状态
            self.hero_icon_label.set_icon(None, '?')
            self.hero_name_label.setText("加载中...")
            self.hero_cost_label.setText("(?费)") # 匹配新格式
            self.play_rate_label.setText("出场率: --")
            self.avg_place_label.setText("平均排名: --")
            self.top4_rate_label.setText("前四率: --")
            self.top1_rate_label.setText("登顶率: --")
            return

        self.hero_name_label.setText(self.hero_base_data.get('cn_name', 'N/A'))
        cost = self.hero_base_data.get('cost')
        self.hero_cost_label.setText(f"({cost}费)" if cost is not None else "(?费)") # 匹配新格式
        icon_path = self.hero_base_data.get('icon_path')
        self.hero_icon_label.set_icon(icon_path, self.hero_base_data.get('cn_name'))

        pr = self.hero_base_data.get('play_rate')
        if pr is not None:
            self.play_rate_label.setText(f"出场率: {pr * 100:.1f}%")
        else:
            self.play_rate_label.setText("出场率: --")
            
        # 其他数据保持不变
        ap = self.hero_base_data.get('avg_place')
        if ap is not None:
            self.avg_place_label.setText(f"平均排名: {ap:.2f}")
        else:
            self.avg_place_label.setText("平均排名: --")
            
        tr = self.hero_base_data.get('top4_rate')
        if tr is not None:
            self.top4_rate_label.setText(f"前四率: {tr * 100:.1f}%")
        else:
            self.top4_rate_label.setText("前四率: --")
            
        wr = self.hero_base_data.get('top1_rate')
        if wr is not None:
            self.top1_rate_label.setText(f"登顶率: {wr * 100:.1f}%")
        else:
            self.top1_rate_label.setText("登顶率: --")

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置英雄详情视图到初始状态")
        
        # 重置装备Tab选择到第一个（"装备"）
        if hasattr(self, 'equip_tabs') and self.equip_tabs:
            self.equip_tabs.setCurrentIndex(0)  # 切换到第一个Tab
        
        # 重置各个Tab Widget的搜索框
        if hasattr(self, 'tab_widgets') and self.tab_widgets:
            for tab_widget in self.tab_widgets.values():
                if hasattr(tab_widget, 'search_box') and tab_widget.search_box:
                    tab_widget.search_box.clear()
                
                # 重置各Tab的排序状态为默认
                if hasattr(tab_widget, 'current_sort_column'):
                    tab_widget.current_sort_column = 'play_rate'
                if hasattr(tab_widget, 'current_sort_order'):
                    tab_widget.current_sort_order = Qt.SortOrder.DescendingOrder
                
                # 重置滚动位置
                if hasattr(tab_widget, 'scroll_area') and tab_widget.scroll_area and tab_widget.scroll_area.isVisible():
                    tab_widget.scroll_area.verticalScrollBar().setValue(0)
                
                # 如果Tab有数据，重新应用筛选和排序
                if hasattr(tab_widget, 'all_equips_data') and tab_widget.all_equips_data:
                    tab_widget.filter_equips("")  # 空字符串会显示所有装备并应用默认排序

    @Slot(list)
    def handle_magnify_request(self, current_items):
        """处理放大镜点击事件"""
        current_count = len(current_items)
        target_count = current_count + 1
        if target_count > 3: return # 最多到三件套

        print(f"放大镜请求: 从 {current_count}件套 ({current_items}) 查找 {target_count}件套")

        # 切换到目标 Tab
        self.equip_tabs.setCurrentIndex(target_count - 1) # Tab索引从0开始

        # 在目标 Tab 的搜索框中设置筛选条件
        target_widget = self.tab_widgets.get(target_count)
        if target_widget:
            # 构建搜索词，简单地用空格连接装备名称
            # 注意：如果装备名称包含空格，这种方式可能不完美
            search_term = " ".join(current_items)
            target_widget.search_box.setText(search_term) # 这会自动触发筛选