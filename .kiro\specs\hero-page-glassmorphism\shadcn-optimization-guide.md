# Shadcn/Vue 优化指南

## Shadcn/Vue 工作原理

### 什么是 Shadcn/Vue

Shadcn/Vue 是一个基于 **Radix Vue** 和 **Tailwind CSS** 的组件库，它不是传统的 npm 包，而是一个**可复制粘贴的组件集合**。

### 核心特点

1. **Copy & Paste 方式**：组件代码直接复制到你的项目中，完全可控
2. **基于 Radix Vue**：提供无障碍访问和键盘导航支持
3. **Tailwind CSS 驱动**：使用 Tailwind 实用类进行样式设计
4. **完全可定制**：因为代码在你的项目中，可以随意修改

## 如何优化我们的 UI 构建

### 1. 基础组件复用

```vue
<!-- 使用 shadcn/vue 的 Button 组件作为基础 -->
<Button variant="ghost" size="sm" class="glassmorphism-button">
  英雄
</Button>

<!-- 使用 shadcn/vue 的 Input 组件作为基础 -->
<Input 
  placeholder="搜索英雄名称..." 
  class="glassmorphism-input"
/>

<!-- 使用 shadcn/vue 的 Card 组件作为基础 -->
<Card class="glassmorphism-card">
  <CardContent>
    <!-- 英雄卡片内容 -->
  </CardContent>
</Card>
```

### 2. Glassmorphism 样式扩展

在 Tailwind 配置中添加自定义样式：

```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      backdropBlur: {
        'xs': '2px',
      },
      backgroundColor: {
        'glass': 'rgba(255, 255, 255, 0.1)',
        'glass-dark': 'rgba(0, 0, 0, 0.1)',
      },
      borderColor: {
        'glass': 'rgba(255, 255, 255, 0.2)',
      }
    }
  }
}
```

### 3. 自定义 Glassmorphism 组件类

```css
/* globals.css */
.glassmorphism-button {
  @apply bg-glass backdrop-blur-md border border-glass rounded-lg;
  @apply hover:bg-glass-dark transition-all duration-200;
  @apply shadow-lg hover:shadow-xl;
}

.glassmorphism-input {
  @apply bg-glass backdrop-blur-md border border-glass rounded-lg;
  @apply focus:ring-2 focus:ring-purple-400 focus:border-transparent;
  @apply placeholder-white/60 text-white;
}

.glassmorphism-card {
  @apply bg-glass backdrop-blur-md border border-glass rounded-xl;
  @apply shadow-2xl hover:shadow-3xl transition-all duration-300;
}
```

### 4. 渐进式构建优势

#### 第一层：基础框架
```vue
<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-600 via-pink-500 to-red-400">
    <!-- 使用 shadcn Card 作为主窗口 -->
    <Card class="glassmorphism-card max-w-4xl mx-auto mt-8">
      <!-- 控制栏 -->
      <div class="flex items-center justify-between p-4 border-b border-glass">
        <Button variant="ghost" size="sm">←</Button>
        <div class="flex-1 text-center">
          <Button variant="ghost" size="sm">⌄</Button>
        </div>
        <Button variant="ghost" size="sm" class="text-red-400 hover:text-red-300">×</Button>
      </div>
    </Card>
  </div>
</template>
```

#### 第二层：导航栏
```vue
<!-- 在 Card 内部添加导航栏 -->
<div class="flex border-b border-glass">
  <Button 
    v-for="tab in ['阵容', '英雄', '装备', '海克斯']" 
    :key="tab"
    variant="ghost" 
    class="flex-1 glassmorphism-button"
    :class="{ 'bg-glass-dark': tab === '英雄' }"
  >
    {{ tab }}
  </Button>
</div>
```

#### 第三层：内容区域
```vue
<!-- 在导航栏下方添加内容区域 -->
<div class="p-6 space-y-4">
  <!-- 搜索框 -->
  <Input 
    placeholder="搜索英雄名称..." 
    class="glassmorphism-input"
  />
  
  <!-- 费用筛选 -->
  <div class="flex gap-2">
    <Button 
      v-for="cost in ['全部', '1费', '2费', '3费', '4费', '5费']"
      :key="cost"
      variant="outline"
      size="sm"
      class="glassmorphism-button"
    >
      {{ cost }}
    </Button>
  </div>
  
  <!-- 英雄列表容器 -->
  <div class="grid grid-cols-5 gap-4">
    <!-- 英雄卡片占位符 -->
    <Card 
      v-for="i in 10" 
      :key="i"
      class="glassmorphism-card aspect-square"
    >
      <CardContent class="p-4 text-center">
        <div class="w-16 h-16 bg-glass rounded-lg mx-auto mb-2"></div>
        <p class="text-sm text-white">英雄名称</p>
      </CardContent>
    </Card>
  </div>
</div>
```

### 5. 组件化优势

#### 可复用的英雄卡片组件
```vue
<!-- HeroCard.vue -->
<template>
  <Card class="glassmorphism-card hover:scale-105 transition-transform cursor-pointer">
    <CardContent class="p-4 text-center">
      <div class="relative">
        <img 
          :src="hero.iconPath" 
          :alt="hero.name"
          class="w-16 h-16 rounded-lg mx-auto mb-2"
        />
        <Badge 
          :class="getCostColor(hero.cost)"
          class="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs"
        >
          {{ hero.cost }}
        </Badge>
      </div>
      <p class="text-sm text-white font-medium">{{ hero.name }}</p>
      <p class="text-xs text-white/60">出场率: {{ hero.playRate }}</p>
      <p class="text-xs text-white/60">均名: {{ hero.avgPlace }}</p>
    </CardContent>
  </Card>
</template>
```

### 6. 状态管理集成

```typescript
// stores/heroes.ts
import { defineStore } from 'pinia'

export const useHeroesStore = defineStore('heroes', () => {
  const heroes = ref([])
  const loading = ref(false)
  const searchQuery = ref('')
  const selectedCost = ref('全部')

  const filteredHeroes = computed(() => {
    return heroes.value.filter(hero => {
      const matchesSearch = hero.name.includes(searchQuery.value)
      const matchesCost = selectedCost.value === '全部' || hero.cost === selectedCost.value
      return matchesSearch && matchesCost
    })
  })

  const loadHeroes = async () => {
    loading.value = true
    try {
      // 调用 Tauri 命令加载数据
      heroes.value = await invoke('get_heroes')
    } finally {
      loading.value = false
    }
  }

  return {
    heroes,
    loading,
    searchQuery,
    selectedCost,
    filteredHeroes,
    loadHeroes
  }
})
```

### 7. 性能优化

#### 虚拟滚动（如果英雄数量很多）
```vue
<script setup>
import { RecycleScroller } from 'vue-virtual-scroller'

const heroesStore = useHeroesStore()
</script>

<template>
  <RecycleScroller
    class="scroller"
    :items="heroesStore.filteredHeroes"
    :item-size="180"
    key-field="id"
    v-slot="{ item }"
  >
    <HeroCard :hero="item" />
  </RecycleScroller>
</template>
```

#### 图片懒加载
```vue
<img 
  v-lazy="hero.iconPath"
  :alt="hero.name"
  class="w-16 h-16 rounded-lg mx-auto mb-2"
  loading="lazy"
/>
```

## 总结

使用 shadcn/vue 的优势：

1. **快速原型**：基础组件开箱即用，专注于业务逻辑
2. **一致性**：所有组件遵循统一的设计系统
3. **可定制**：代码在项目中，可以随意修改样式
4. **无障碍**：基于 Radix Vue，天然支持键盘导航和屏幕阅读器
5. **性能**：基于 Vue 3 Composition API，性能优秀
6. **类型安全**：完整的 TypeScript 支持

这种渐进式构建方式让我们可以：
- 每一步都有可见的进展
- 容易调试和修改
- 代码结构清晰
- 便于团队协作