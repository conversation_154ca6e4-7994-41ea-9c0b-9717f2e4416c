# -*- coding: utf-8 -*-
"""
中央触发器管理器 (Central Trigger Manager)

本模块负责后台的统一监控，根据预设的规则（如哈希匹配）
来决定何时触发海克斯和装备的OCR识别流程。
"""
import threading
import time
import json
import logging
from PIL import Image, ImageFilter, ImageGrab
import imagehash
import numpy as np

import ocr_handler
import config
import utils

class TriggerManager:
    def __init__(self, app_ref, hex_manager_ref, equip_manager_ref):
        self.app = app_ref
        self.hex_manager = hex_manager_ref
        self.equip_manager = equip_manager_ref

        self.is_monitoring = False
        self.monitor_thread = None
        self.hash_bases = self._load_hash_bases()
        self.last_triggered = {}

    def _load_hash_bases(self):
        """从JSON文件加载哈希基准值。"""
        try:
            with open(config.HASH_BASES_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # 将字符串哈希转换回 imagehash 对象
            for key, bases in data.items():
                for base_name, hashes in bases.items():
                    for h_key, h_val in hashes.items():
                        data[key][base_name][h_key] = imagehash.hex_to_hash(h_val)
            return data
        except FileNotFoundError:
            logging.error(f"未找到哈希基准文件: {config.HASH_BASES_PATH}")
            return {}
        except Exception as e:
            logging.error(f"加载或解析哈希基准文件时出错: {e}")
            return {}

    def start_monitoring(self):
        """启动后台监控线程。"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            logging.info("中央触发器监控已启动。")

    def stop_monitoring(self):
        """停止后台监控。"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=0.5)
        logging.info("中央触发器监控已停止。")

    def _calculate_hashes(self, img):
        """计算图像的所有四种哈希值。"""
        return {
            "p": imagehash.phash(img),
            "d": imagehash.dhash(img),
            "a": imagehash.average_hash(img),
            "w": imagehash.whash(img)
        }

    def _check_hashes(self, current_hashes, base_hashes, required_matches):
        """检查当前哈希与基准哈希的匹配度。"""
        if not current_hashes or not base_hashes:
            return False
        
        diffs = {h_key: base_hashes[h_key] - current_hashes[h_key] for h_key in base_hashes}
        count = sum(1 for d in diffs.values() if d <= config.HASH_DIFFERENCE_THRESHOLD)
        return count >= required_matches

    def _get_image_for_hash(self, key, base_img):
        """根据配置对图像进行预处理（例如边框哈希）。"""
        info = config.MONITOR_REGIONS.get(key, {})
        if info.get("mode") == "frame_hash":
            outer_w, outer_h = base_img.size
            outer_coords = info["coords_outer"]
            inner_coords = info["coords_inner"]
            
            inner_rel_x1 = (inner_coords[0] - outer_coords[0]) / (outer_coords[2] - outer_coords[0])
            inner_rel_y1 = (inner_coords[1] - outer_coords[1]) / (outer_coords[3] - outer_coords[1])
            inner_rel_x2 = (inner_coords[2] - outer_coords[0]) / (outer_coords[2] - outer_coords[0])
            inner_rel_y2 = (inner_coords[3] - outer_coords[1]) / (outer_coords[3] - outer_coords[1])
            
            inner_abs_x1 = int(outer_w * inner_rel_x1)
            inner_abs_y1 = int(outer_h * inner_rel_y1)
            inner_abs_x2 = int(outer_w * inner_rel_x2)
            inner_abs_y2 = int(outer_h * inner_rel_y2)
            
            mask = Image.new('L', (outer_w, outer_h), 0)
            mask.paste(255, (0, 0, outer_w, outer_h))
            mask.paste(0, (inner_abs_x1, inner_abs_y1, inner_abs_x2, inner_abs_y2))
            
            final_img = Image.new(base_img.mode, (outer_w, outer_h), 'black')
            final_img.paste(base_img, (0,0), mask)
            return final_img
        return base_img

    def _is_color_close(self, color1, color2, threshold=50):
        """
        检查两种RGB颜色是否接近。
        目标颜色 (182, 145, 61) 与实际截图中的相似色点距离通常在35以内。
        阈值设为50可以在容忍较大颜色波动的同时，避免过于宽松导致的误触发。
        """
        c1 = np.array(color1)
        c2 = np.array(color2)
        distance = np.linalg.norm(c1 - c2)
        return distance < threshold

    def _monitoring_loop(self):
        """监控主循环。"""
        while self.is_monitoring:
            try:
                game_rect = utils.get_game_window_rect(use_client_area=True)
                if not game_rect or game_rect[2] == 0:
                    time.sleep(1)
                    continue

                # --- 海克斯触发逻辑 ---
                # 检查蓝色按钮
                blue_button_info = config.MONITOR_REGIONS.get("blue_button")
                blue_button_base = self.hash_bases.get("blue_button", {}).get("base_1")
                
                blue_triggered = False
                if blue_button_info and blue_button_base:
                    bbox = utils.convert_relative_to_absolute(blue_button_info["coords"], game_rect)
                    img = ocr_handler.capture_screen(bbox)
                    current_hashes = self._calculate_hashes(img)
                    if self._check_hashes(current_hashes, blue_button_base, config.HEX_HASH_REQUIRED_MATCH):
                        blue_triggered = True

                # 检查刷新按钮组
                refresh_button_info = config.MONITOR_REGIONS.get("refresh_button")
                refresh_button_base = self.hash_bases.get("refresh_button", {}).get("base_1")
                
                refresh_triggered = False
                if refresh_button_info and refresh_button_base:
                    match_count = 0
                    for coords in refresh_button_info["coords"]:
                        bbox = utils.convert_relative_to_absolute(coords, game_rect)
                        img = ocr_handler.capture_screen(bbox)
                        current_hashes = self._calculate_hashes(img)
                        if self._check_hashes(current_hashes, refresh_button_base, config.HEX_HASH_REQUIRED_MATCH):
                            match_count += 1
                    
                    # "2 of 3" rule - 这里的2是固定的业务逻辑，不应从config读取
                    if match_count >= 2:
                        refresh_triggered = True

                # 最终海克斯触发判断
                if blue_triggered or refresh_triggered:
                    if not self.hex_manager.is_running():
                        if time.time() - self.last_triggered.get("hex", 0) > 5: # 冷却时间
                            logging.info(f"海克斯触发: blue_button({blue_triggered}), refresh_button({refresh_triggered})")
                            self.hex_manager.start_hex_ocr(manual=False)
                            self.last_triggered["hex"] = time.time()

                # --- 装备触发逻辑 ---
                equip_frame_info = config.MONITOR_REGIONS.get("equipment_frame")
                equip_bases = self.hash_bases.get("equipment_frame", {})
                base_empty = equip_bases.get("base_empty")
                base_stable = equip_bases.get("base_stable")

                if equip_frame_info and base_empty and base_stable:
                    # 步骤1: 首先在精确的小区域内进行颜色检查
                    color_check_bbox = utils.convert_relative_to_absolute(config.EQUIP_COLOR_CHECK_REGION_RELATIVE, game_rect)
                    color_img = ocr_handler.capture_screen(color_check_bbox)
                    center_color = color_img.getpixel((color_img.width // 2, color_img.height // 2))
                    target_color = (182, 145, 61)
                    color_match = self._is_color_close(center_color, target_color)

                    # 只有颜色匹配成功，才继续进行哈希检查
                    if color_match:
                        # 步骤2: 截取大区域进行哈希计算
                        hash_bbox = utils.convert_relative_to_absolute(equip_frame_info["coords_outer"], game_rect)
                        hash_base_img = ocr_handler.capture_screen(hash_bbox)
                        hash_img = self._get_image_for_hash("equipment_frame", hash_base_img)
                        current_hashes = self._calculate_hashes(hash_img)

                        # [修正] 使用config中的配置，而不是硬编码
                        is_empty = self._check_hashes(current_hashes, base_empty, config.EQUIP_HASH_REQUIRED_MATCH)
                        is_stable = self._check_hashes(current_hashes, base_stable, config.EQUIP_HASH_REQUIRED_MATCH)

                        if is_empty or is_stable:
                            if not self.equip_manager.is_running():
                                if time.time() - self.last_triggered.get("equip", 0) > 5: # 冷却时间
                                    logging.info(f"装备触发: 颜色与哈希均匹配 (is_empty: {is_empty}, is_stable: {is_stable})")
                                    self.equip_manager.start_equip_ocr(manual=False)
                                    self.last_triggered["equip"] = time.time()
                
                time.sleep(0.2) # 循环间隔

            except OSError as e:
                # 这是一个预期的、常见的错误，当屏幕被安全桌面（如UAC、任务管理器）锁定时发生
                if "screen grab failed" in str(e):
                    logging.info("屏幕暂时无法截图（可能由于安全桌面），跳过此次检测。")
                    time.sleep(1) # 等待1秒后重试
                else:
                    # 对于其他类型的OSError，仍然作为严重错误记录
                    logging.error(f"中央触发器监控循环发生未处理的操作系统错误: {e}", exc_info=True)
                    time.sleep(1)
            except Exception as e:
                logging.error(f"中央触发器监控循环出错: {e}", exc_info=True)
                time.sleep(1)