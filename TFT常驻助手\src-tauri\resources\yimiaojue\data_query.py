# -*- coding: utf-8 -*-
"""
数据查询模块

本文件封装了所有与数据查询、匹配和处理相关的逻辑。
包括海克斯和装备的数据库连接、数据预加载以及模糊匹配算法。
"""

import sqlite3
import re
import sys
import os
from tkinter import messagebox
import pypinyin
from pypinyin import lazy_pinyin
from Levenshtein import ratio

import config
import utils

# --- 海克斯数据查询与匹配 ---
class EnhancedHextechQuery:
    def __init__(self):
        """初始化海克斯查询器，预加载数据。"""
        if not os.path.exists(config.HEX_DB_PATH):
            messagebox.showerror("Error", "Hextech Database file not found!")
            sys.exit(1)
        self._preload_data()

    def _preload_data(self):
        """从数据库预加载所有海克斯数据到内存中，并创建拼音索引。"""
        # 获取日志系统实例
        from logger_setup import get_logging_system
        logging_system = get_logging_system()
        
        try:
            conn = sqlite3.connect(config.HEX_DB_PATH)
            logging_system.log_database_connection(config.HEX_DB_PATH, 'success')
            
            cursor = conn.cursor()
            cursor.execute("SELECT name, tier FROM hexes")
            # 原始数据
            rows = cursor.fetchall()
            self.augment_map = {row[0]: row[1] for row in rows}
            conn.close()
            self.all_names = list(self.augment_map.keys())
            
            logging_system.log_database_connection(
                config.HEX_DB_PATH, 
                'success', 
                f"加载了{len(self.all_names)}个海克斯强化项"
            )
            
        except Exception as e:
            logging_system.log_database_connection(config.HEX_DB_PATH, 'failed', str(e))
            raise

        # --- 新增：为模糊搜索创建拼音映射 ---
        self.hex_pinyin_map = {}
        for name in self.all_names:
            # full: 全拼, initial: 首字母
            self.hex_pinyin_map[name] = {
                'name': name,
                'full': ''.join(lazy_pinyin(name)).lower(),
                'initial': ''.join(item[0] for item in lazy_pinyin(name, style=pypinyin.Style.FIRST_LETTER)).lower()
            }

    def _preprocess_name(self, name):
        """预处理名称，移除等级罗马数字并转为小写。"""
        name = re.sub(r'[ⅠⅡⅢⅣⅤI]+$', '', name).strip()
        return name.lower()

    def _preprocess(self, text):
        """预处理文本，移除无关字符并生成拼音。"""
        cleaned = re.sub(r'[^\u4e00-\u9fa5a-zA-Z]', '', text).lower()
        return (cleaned, ''.join(lazy_pinyin(cleaned)))

    def fuzzy_search_hex(self, query):
        """
        专为用户手动输入设计的实时模糊搜索方法。
        支持汉字、全拼、首字母缩写混合搜索。
        """
        query = query.strip().lower()
        if not query:
            return []

        candidates = []
        is_chinese_query = bool(re.search(r'[\u4e00-\u9fa5]', query))

        # 如果是中文查询，也计算其拼音以备用
        query_pinyin = ''.join(lazy_pinyin(query)) if is_chinese_query else None

        for name, pinyin_data in self.hex_pinyin_map.items():
            score = 0
            # 规则1: 拼音/首字母匹配 (适用于英文/拼音输入)
            if not is_chinese_query:
                if query in pinyin_data['initial']:
                    score += 90  # 首字母匹配权重最高
                    # bonus for exact initial match
                    if query == pinyin_data['initial']:
                        score += 10
                elif query in pinyin_data['full']:
                    score += 70 # 全拼包含匹配
            
            # 规则2: 汉字匹配
            if is_chinese_query:
                if query in name:
                    score += 100 # 汉字直接包含，权重最高
                # 使用中文查询的拼音进行匹配 (处理同音异形字)
                elif query_pinyin and query_pinyin in pinyin_data['full']:
                    score += 80

            # 规则3: Levenshtein距离作为补充
            if score == 0: # 仅在上面规则完全不匹配时启用，避免性能开销
                # 如果是拼音输入，则与拼音比较
                if not is_chinese_query:
                    score += ratio(query, pinyin_data['full']) * 60
                # 如果是中文输入，则与中文比较
                else:
                    score += ratio(query, name) * 60

            if score > 50: # 设定一个阈值，避免不相关的结果
                candidates.append({'name': name, 'score': score})
        
        # 按分数从高到低排序
        candidates.sort(key=lambda x: x['score'], reverse=True)
        
        # 返回排序后的海克斯名称列表
        return [c['name'] for c in candidates][:10] # 最多返回10个结果

    def _extract_base_name_for_special(self, name):
        """提取特殊海克斯（如'之徽'）的基础名称。"""
        if "之徽" in name or "之冕" in name or "之环" in name:
            zh_index = name.find("之")
            if zh_index > 0:
                return name[:zh_index]
        return name

    def fuzzy_match_whitelist(self, text, whitelist):
        """
        核心的模糊匹配算法。
        综合使用编辑距离、拼音相似度、最长公共子串和位置奖励来进行智能匹配。
        """
        text_clean, text_pinyin_str = self._preprocess(text)
        text_no_roman = self._preprocess_name(text_clean)

        best_match_base = None
        best_score = 0
        best_match_candidates = []

        for name in whitelist:
            name_no_roman = self._preprocess_name(name)
            edit_similarity = ratio(text_no_roman, name_no_roman)
            pinyin_similarity = ratio(text_pinyin_str, ''.join(lazy_pinyin(name_no_roman)))
            combined_similarity = 0.7 * edit_similarity + 0.3 * pinyin_similarity
            
            longest_common_substring = self._find_longest_common_substring(text_no_roman, name_no_roman)
            lcs_ratio = len(longest_common_substring) / max(len(text_no_roman), len(name_no_roman)) if longest_common_substring else 0
            
            position_bonus = 0
            if longest_common_substring:
                text_pos = text_no_roman.find(longest_common_substring) / len(text_no_roman) if len(text_no_roman) > 0 else 0
                name_pos = name_no_roman.find(longest_common_substring) / len(name_no_roman) if len(name_no_roman) > 0 else 0
                position_bonus = 1 - abs(text_pos - name_pos)
            
            final_similarity = 0.6 * combined_similarity + 0.3 * lcs_ratio + 0.1 * position_bonus
            
            if abs(final_similarity - best_score) < 0.01:
                best_match_candidates.append((name_no_roman, final_similarity, lcs_ratio, position_bonus))
            elif final_similarity > best_score:
                best_score = final_similarity
                best_match_base = name_no_roman
                best_match_candidates = [(name_no_roman, final_similarity, lcs_ratio, position_bonus)]

        match_score = best_score * 100
        
        if best_score <= 0.6:
            return None, None, match_score

        if len(best_match_candidates) > 1:
            best_match_candidates.sort(key=lambda x: (x[2], x[3]), reverse=True)
            best_match_base = best_match_candidates[0][0]

        candidates = [name for name in whitelist if self._preprocess_name(name) == best_match_base]
        cleaned_text = utils.clean_ocr_text(text)
        for candidate in candidates:
            if cleaned_text == candidate:
                return candidate, candidates, match_score

        best_match = None
        best_score = 0
        for candidate in candidates:
            similarity = ratio(cleaned_text, candidate)
            if similarity > best_score:
                best_score = similarity
                best_match = candidate
        return best_match, candidates, match_score
        
    def _find_longest_common_substring(self, str1, str2):
        """查找两个字符串的最长公共子串。"""
        if not str1 or not str2:
            return ""
        m, n = len(str1), len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        max_length, end_pos = 0, 0
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                    if dp[i][j] > max_length:
                        max_length = dp[i][j]
                        end_pos = i
        return str1[end_pos - max_length:end_pos]

    def handle_special_variants(self, name):
        """处理特殊变体（如'之徽'），找到所有相关海克斯。"""
        if not name or (not "之徽" in name and not "之冕" in name and not "之环" in name):
            return None
        base_name = self._extract_base_name_for_special(name)
        if base_name == name:
            return None
        special_variants = {full_name: rank for full_name, rank in self.augment_map.items() if base_name in full_name}
        return special_variants if len(special_variants) > 1 else None

    def get_variants_by_base_name(self, base_name):
        """根据基础名称找到所有变体（如 I, II, III）。"""
        return {name: rank for name, rank in self.augment_map.items() if self._preprocess_name(name) == base_name}

    def smart_query(self, original_name):
        """精确查询海克斯数据。"""
        if original_name in self.augment_map:
            return {'status': 'exact', 'data': {'name': original_name, 'rank': self.augment_map[original_name]}}
        else:
            return {'status': 'not_found'}

# --- 装备数据查询与匹配 ---
class EquipmentQuery:
    def __init__(self):
        """初始化装备查询器，预加载数据。"""
        if not os.path.exists(config.EQUIP_DB_PATH):
            messagebox.showerror("Error", "Equipment Database file not found!")
            sys.exit(1)
        self._preload_data()
        self.debug_mode = True

    def _preload_data(self):
        """从数据库预加载所有装备数据到内存中。"""
        # 获取日志系统实例
        from logger_setup import get_logging_system
        logging_system = get_logging_system()
        
        try:
            conn = sqlite3.connect(config.EQUIP_DB_PATH)
            logging_system.log_database_connection(config.EQUIP_DB_PATH, 'success')
            
            cursor = conn.cursor()
            cursor.execute("SELECT name, avg_placement, top1_rate, play_rate, tier FROM items")
            rows = cursor.fetchall()
            self.equipment_map = {row[0]: (row[1], row[2], row[3], row[4]) for row in rows}
            conn.close()
            self.all_names = list(self.equipment_map.keys())
            
            logging_system.log_database_connection(
                config.EQUIP_DB_PATH, 
                'success', 
                f"加载了{len(self.all_names)}个装备项"
            )
            
        except Exception as e:
            logging_system.log_database_connection(config.EQUIP_DB_PATH, 'failed', str(e))
            raise

    def _preprocess_name(self, name):
        """预处理名称，转为小写。"""
        return name.lower()

    def match_equipment(self, text_parts, whitelist):
        """
        [重构] 对分割后的每段文本进行装备匹配。
        为保证UI稳定性，此方法将始终返回与text_parts等长的列表。
        匹配失败的位置将用 None 填充。
        """
        results = [None] * len(text_parts) # 初始化一个与输入等长的结果列表，用None填充

        for i, part in enumerate(text_parts):
            # 1. 清理文本
            cleaned_text = utils.clean_ocr_text(part)
            if not cleaned_text:
                continue # 如果文本清理后为空，则跳过，保留None

            # 2. 寻找最佳匹配
            best_match = None
            highest_score = 0
            for item_name in whitelist:
                # 使用简单的编辑距离作为相似度度量
                score = ratio(cleaned_text, item_name)
                if score > highest_score:
                    highest_score = score
                    best_match = item_name

            # 3. 应用置信度阈值
            # 阈值设为0.4，因为装备名称通常较短且OCR易出错
            if highest_score > 0.4:
                results[i] = best_match # 在相应位置填入匹配结果

        if self.debug_mode:
            print(f"装备匹配完成，输入: {text_parts}, 输出: {results}")
            
        return results

    def _find_best_match(self, text, whitelist):
        # 此方法在新的逻辑中不再需要，可以移除或保留以备后用
        pass

    def smart_query(self, name):
        """精确查询装备数据。"""
        if name in self.equipment_map:
            data = self.equipment_map[name]
            return {'name': name, 'data': data[:3], 'rating': data[3]}
        else:
            return {'name': "未找到", 'data': (0, 0, 0), 'rating': None} 