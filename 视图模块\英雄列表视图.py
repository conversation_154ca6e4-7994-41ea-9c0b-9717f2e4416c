# 视图模块/英雄列表视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
                               QSpacerItem, QSizePolicy, QFrame, QPushButton, QCheckBox,
                               QGridLayout) # 导入 QGridLayout
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QCursor, QFont, QPainter, QColor, QPen # 导入 QPainter, QColor, QPen

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_HIGHLIGHT, TEXT_COLOR_DARK,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ITEM_ROW_BG_HOVER,
                       ICON_SIZE_LARGE, SECTION_BG_COLOR, BUTTON_BG, COST_COLORS,
                       # 只导入需要的字体大小常量
                       FONT_SIZE_SMALL, FONT_SIZE_NORMAL, FONT_SIZE_MEDIUM, FONT_SIZE_LARGE,
                       FONT_WEIGHT_BOLD, FONT_WEIGHT_SEMIBOLD) # 暂时保留需要用的权重，后续调整
from 自定义组件 import IconLabel, ClickableLabel, SearchLineEdit # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数


# --- 新增：垂直费用标签 ---
class VerticalCostLabel(QWidget):
    """自定义垂直费用标签"""
    def __init__(self, cost_text, cost_color, parent=None):
        super().__init__(parent)
        self.cost_text = cost_text
        self.cost_color = QColor(cost_color)
        self.text_color = QColor(TEXT_COLOR_DARK) # 深色文字
        self.setFixedWidth(45) # 保持与海克斯评级标签宽度一致
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding) # 宽度固定，高度扩展

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        # 绘制带边框的圆角矩形作为背景
        painter.setPen(QPen(QColor(BORDER_COLOR), 1)) # 添加边框
        painter.setBrush(self.cost_color)
        # 调整 rect 缩小 1px，避免边框被裁减
        rect = self.rect().adjusted(1, 1, -1, -1)
        painter.drawRoundedRect(rect, 5, 5)

        # 绘制文字
        painter.setPen(self.text_color)
        font = QFont()
        font.setPointSize(FONT_SIZE_LARGE) # 使用常量
        font.setWeight(QFont.Weight.Bold) # 直接使用 QFont.Weight 枚举
        painter.setFont(font)
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, self.cost_text)

    def minimumSizeHint(self):
        return QSize(45, 50)


class 英雄列表视图(QWidget):
    """显示按费用分组的英雄列表的视图"""
    hero_selected = Signal(str) # 点击英雄时发出信号，参数为英雄中文名

    def __init__(self, parent=None):
        super().__init__(parent)
        self.all_hero_data = []
        self.filtered_hero_data = []
        self.cost_order = sorted([1, 2, 3, 4, 5, 0])
        self.cost_checkboxes = {}
        self._updating_cost_checkboxes = False
        self._is_loading = False # 新增：加载状态标志

        self.init_ui()
        # self.load_data() # 数据加载由主窗口触发

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(8)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 搜索框 ---
        self.search_box = SearchLineEdit(placeholder_text="搜索英雄名称...")
        self.search_box.searchChanged.connect(self.apply_filters)
        self.main_layout.addWidget(self.search_box)

        # --- 费用筛选区域 ---
        self.cost_filter_layout = QHBoxLayout()
        self.cost_filter_layout.setContentsMargins(5, 0, 5, 5)
        self.cost_filter_layout.setSpacing(10)
        cost_label = QLabel("费用:")
        cost_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_SMALL}px;")
        self.cost_filter_layout.addWidget(cost_label)
        self.create_cost_filter_checkboxes()
        self.cost_filter_layout.addStretch(1)
        self.main_layout.addLayout(self.cost_filter_layout)

        # --- 加载提示 QLabel ---
        self.loading_label = QLabel("正在加载英雄数据...", alignment=Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_LARGE}px; padding: 30px;")
        self.main_layout.addWidget(self.loading_label)
        self.loading_label.hide() # 初始隐藏

        # --- 英雄列表滚动区域 (初始隐藏) ---
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.hide() # 初始隐藏

        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(0)

        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area, 1)

    def create_cost_filter_checkboxes(self):
        """创建费用筛选的复选框"""
        # 清理旧的 (如果需要)
        for checkbox in self.cost_checkboxes.values():
            self.cost_filter_layout.removeWidget(checkbox)
            checkbox.deleteLater()
        self.cost_checkboxes.clear()

        # 清除 stretch (如果存在)
        if self.cost_filter_layout.count() > 1: # 假设 QLabel 是第一个
            item = self.cost_filter_layout.takeAt(self.cost_filter_layout.count() - 1)
            if item and isinstance(item, QSpacerItem):
                pass # 成功移除 stretch
            else: # 可能移除了别的，放回去
                 if item: self.cost_filter_layout.addItem(item)


        checkbox_style = f"""
            QCheckBox {{ color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_SMALL}px; spacing: 5px; }}
            QCheckBox::indicator {{ width: 12px; height: 12px; border: 1px solid {BORDER_COLOR}; border-radius: 3px; background-color: {BUTTON_BG}; }}
            QCheckBox::indicator:checked {{ background-color: {TEXT_COLOR_HIGHLIGHT}; border: 1px solid {TEXT_COLOR_HIGHLIGHT}; }}
            QCheckBox::indicator:hover {{ border: 1px solid {TEXT_COLOR_LIGHT}; }}
            QCheckBox:hover {{ color: {TEXT_COLOR_LIGHT}; }}
        """

        # 添加 "全部"
        all_cb = QCheckBox("全部")
        all_cb.setStyleSheet(checkbox_style)
        all_cb.setChecked(True)
        all_cb.stateChanged.connect(self.handle_all_costs_change)
        self.cost_filter_layout.addWidget(all_cb)
        self.cost_checkboxes["全部"] = all_cb

        # 添加 1-5 费
        for cost in range(1, 6):
            cb = QCheckBox(f"{cost}费")
            cb.setStyleSheet(checkbox_style)
            cb.setProperty("cost_value", cost)
            cb.stateChanged.connect(self.handle_cost_change)
            self.cost_filter_layout.addWidget(cb)
            self.cost_checkboxes[cost] = cb

        self.cost_filter_layout.addStretch(1) # 加回 stretch

    def handle_all_costs_change(self, state):
        """处理 '全部' 费用复选框状态变化"""
        if self._updating_cost_checkboxes: return
        self._updating_cost_checkboxes = True
        is_checked = (state == Qt.CheckState.Checked.value)
        if is_checked:
            for cost, checkbox in self.cost_checkboxes.items():
                if cost != "全部":
                    checkbox.setChecked(False)
        elif not any(cb.isChecked() for cost, cb in self.cost_checkboxes.items() if cost != "全部"):
            self.cost_checkboxes["全部"].setChecked(True)
        self._updating_cost_checkboxes = False
        self.apply_filters()

    def handle_cost_change(self, state):
        """处理单个费用复选框状态变化"""
        if self._updating_cost_checkboxes: return
        self._updating_cost_checkboxes = True
        is_checked = (state == Qt.CheckState.Checked.value)
        if is_checked:
            if self.cost_checkboxes.get("全部"):
                self.cost_checkboxes["全部"].setChecked(False)
        elif not any(cb.isChecked() for cost, cb in self.cost_checkboxes.items() if cost != "全部"):
            if self.cost_checkboxes.get("全部"):
                self.cost_checkboxes["全部"].setChecked(True)
        self._updating_cost_checkboxes = False
        self.apply_filters()

    def load_data(self):
        """从数据库异步加载英雄数据"""
        if self._is_loading or (hasattr(self, 'all_hero_data') and self.all_hero_data):
            if self._is_loading:
                print("英雄数据正在加载中，请稍候...")
            else:
                print("英雄数据已加载，跳过重复加载。")
                # 确保UI状态正确
                if not self.loading_label.isHidden() or self.scroll_area.isHidden():
                    self.loading_label.hide()
                    self.scroll_area.show()
                    self.apply_filters() # 重新应用筛选以确保UI更新
            return

        print("开始异步加载英雄数据...")
        self._is_loading = True
        self.loading_label.setText("正在加载英雄数据...") # 确保文本正确
        self.loading_label.show()
        self.scroll_area.hide()
        # 清理旧的内容
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget and widget != self.loading_label: # 不要删除 loading_label (虽然它现在在 main_layout)
                 widget.deleteLater()
            elif widget == self.loading_label:
                pass # loading_label 在main_layout中，这里 content_layout 不应该再有它，但以防万一

        sql = "SELECT cn_name, cost, icon_path, avg_place, play_rate FROM heroes ORDER BY cost, cn_name"
        query_key = "all_heroes_list_by_cost" # 定义缓存键

        execute_query_async(
            sql,
            on_success=self.on_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key
        )

    def on_data_loaded(self, results):
        """数据加载成功后的回调函数"""
        print(f"英雄数据加载成功，共 {len(results)} 条。")
        self.all_hero_data = results if results else []
        loaded_costs = set(h.get('cost', 0) for h in self.all_hero_data)
        self.cost_order = sorted(list(loaded_costs))

        self._is_loading = False # 数据加载完成
        self.loading_label.hide() # 隐藏加载提示
        self.scroll_area.show()   # 显示内容区域
        self.apply_filters()      # 触发初始显示

    def on_data_load_error(self, error_message):
        """数据加载失败后的回调函数"""
        print(f"英雄数据加载失败: {error_message}")
        self._is_loading = False # 加载结束（虽然失败了）
        self.display_error(f"加载英雄数据失败:\n{error_message}")
        self.loading_label.setText(f"加载英雄数据失败: {error_message[:100]}...") # 在主loading_label显示错误
        self.loading_label.show()
        self.scroll_area.hide()

    def display_error(self, message):
        """在界面上显示错误信息"""
        # 清理 content_widget 内的内容
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()

        # 如果 self.loading_label 存在于 main_layout, 用它显示错误
        # 否则，在 content_layout 中创建新的错误标签
        if hasattr(self, 'loading_label') and self.loading_label.parent() == self.main_layout:
            self.loading_label.setText(message)
            self.loading_label.setWordWrap(True) # 允许换行
            self.loading_label.show()
            self.scroll_area.hide()
        else: # Fallback: 如果 loading_label 不合适或不存在，则在 content_layout 中显示
            error_label = QLabel(message, alignment=Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_MEDIUM}px; padding: 20px;")
            error_label.setWordWrap(True)
            self.content_layout.addWidget(error_label)
            # 确保 content_widget (如果它承载错误标签) 是可见的，而 scroll_area 隐藏
            self.scroll_area.show() # content_widget 在 scroll_area 内部
            self.content_widget.show() # 确保 content_widget 可见
            # self.loading_label = None # 避免混淆

    def apply_filters(self):
        """应用当前的筛选条件"""
        search_term = self.search_box.text().strip().lower()
        selected_costs = set()

        # 确定选中的费用
        if "全部" in self.cost_checkboxes and self.cost_checkboxes["全部"].isChecked():
            selected_costs = set(self.cost_order) # 包含所有费用
        else:
            for cost, checkbox in self.cost_checkboxes.items():
                if cost != "全部" and checkbox.isChecked():
                    selected_costs.add(cost)

        self.filtered_hero_data = []
        for hero in self.all_hero_data:
            name_match = (not search_term) or (search_term in hero.get('cn_name', '').lower())
            cost_match = hero.get('cost', 0) in selected_costs
            if name_match and cost_match:
                self.filtered_hero_data.append(hero)
        self.sort_and_update_ui()

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置英雄列表视图到初始状态")
        
        # 重置搜索框
        if hasattr(self, 'search_box') and self.search_box:
            self.search_box.clear()
        
        # 重置费用筛选为"全部"选中状态
        if hasattr(self, 'cost_checkboxes') and self.cost_checkboxes:
            self._updating_cost_checkboxes = True  # 防止信号循环
            try:
                # 选中"全部"，取消其他选项
                if "全部" in self.cost_checkboxes:
                    self.cost_checkboxes["全部"].setChecked(True)
                
                for cost, checkbox in self.cost_checkboxes.items():
                    if cost != "全部":
                        checkbox.setChecked(False)
            finally:
                self._updating_cost_checkboxes = False
        
        # 重置排序状态为默认
        self.current_sort_column = 'play_rate'
        self.current_sort_order = Qt.SortOrder.DescendingOrder
        
        # 重置滚动位置（如果滚动区域可见）
        if hasattr(self, 'scroll_area') and self.scroll_area and self.scroll_area.isVisible():
            self.scroll_area.verticalScrollBar().setValue(0)
        
        # 重新应用筛选和排序（如果数据已加载）
        if hasattr(self, 'all_hero_data') and self.all_hero_data and not self._is_loading:
            self.apply_filters()

    def sort_and_update_ui(self):
        """根据 self.filtered_hero_data 更新UI元素"""
        if self._is_loading: # 如果正在加载，则不更新UI
            print("sort_and_update_ui 调用，但数据仍在加载中，跳过UI更新。")
            return

        # 确保在更新UI前，加载提示已隐藏，内容区已显示 (除非没有数据)
        if self.all_hero_data or not self.filtered_hero_data:
             self.loading_label.hide()
             self.scroll_area.show()
        else: # 如果 all_hero_data 为空 (可能加载失败或无数据), 保持 loading_label (可能显示错误)
            self.loading_label.show()
            self.scroll_area.hide()
            return # 不继续更新UI

        # 清理旧的内容，除了 loading_label (它现在在 main_layout)
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()
            elif item and item.spacerItem(): self.content_layout.removeItem(item)


        # 按费用分组筛选后的英雄
        heroes_grouped_by_cost = {}
        for hero in self.filtered_hero_data:
            cost = hero.get('cost', 0)
            if cost not in heroes_grouped_by_cost:
                heroes_grouped_by_cost[cost] = []
            heroes_grouped_by_cost[cost].append(hero)

        has_results = False
        # 按费用顺序添加区块
        for cost in self.cost_order:
            if cost in heroes_grouped_by_cost and heroes_grouped_by_cost[cost]:
                # 按名称排序同一费用的英雄
                heroes_grouped_by_cost[cost].sort(key=lambda h: h.get('cn_name', ''))
                self.add_cost_section(cost, heroes_grouped_by_cost[cost])
                has_results = True

        if not has_results:
            no_results_label = QLabel("未找到匹配的英雄", alignment=Qt.AlignmentFlag.AlignCenter)
            no_results_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_MEDIUM}px; padding: 20px;")
            self.content_layout.addWidget(no_results_label)

        # 添加弹性空间
        self.content_layout.addSpacerItem(QSpacerItem(20, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))

    def add_cost_section(self, cost, hero_list):
        """向布局中添加一个费用区块 (新布局，类似海克斯)"""
        # 外部 Frame
        cost_section_frame = QFrame()
        cost_section_frame.setStyleSheet(f"""
            QFrame {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                background-color: {WINDOW_BG_COLOR}; /* 或 SECTION_BG_COLOR */
                margin-bottom: 8px;
            }}
        """)
        section_layout = QHBoxLayout(cost_section_frame)
        section_layout.setContentsMargins(0, 0, 0, 0)
        section_layout.setSpacing(0)

        # 左侧：垂直费用标签
        cost_color = COST_COLORS.get(cost, COST_COLORS[0]) # 获取颜色，默认0费
        cost_text = str(cost) if cost > 0 else "" # 0费不显示文字
        cost_label_widget = VerticalCostLabel(cost_text, cost_color)
        section_layout.addWidget(cost_label_widget, 0) # 不伸展

        # 右侧：英雄网格容器
        grid_container = QWidget()
        grid_container_layout = QVBoxLayout(grid_container)
        grid_container_layout.setContentsMargins(10, 10, 10, 10) # 内边距
        grid_container_layout.setSpacing(0)

        grid_widget = QWidget()
        grid_layout = QGridLayout(grid_widget)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setSpacing(10) # 网格间距

        # --- 修改：每行放 5 个英雄 ---
        items_per_row = 5
        for index, hero_detail in enumerate(hero_list):
            row = index // items_per_row
            col = index % items_per_row
            hero_item = self.create_hero_grid_item(hero_detail)
            if hero_item:
                # 修改：移除 alignment，让 item_container 填充单元格
                grid_layout.addWidget(hero_item, row, col)

        grid_layout.setRowStretch(len(hero_list) // items_per_row + 1, 1) # 底部伸展
        grid_layout.setColumnStretch(items_per_row, 1) # 右侧伸展

        grid_container_layout.addWidget(grid_widget)
        section_layout.addWidget(grid_container, 1)
        self.content_layout.addWidget(cost_section_frame)

    def create_hero_grid_item(self, hero_detail):
        """创建英雄网格中的单个显示单元"""
        item_container = QWidget()
        # --- 修改：移除固定高度，保持固定宽度，让高度自适应 ---
        container_width = 85
        # container_height = 135 # 移除固定高度
        item_container.setFixedWidth(container_width) # 只设置固定宽度
        # item_container.setStyleSheet("background-color: transparent; border: none;") # 确保无背景无边框

        item_layout = QVBoxLayout(item_container)
        # --- 保持之前的修改：移除边距和间距 ---
        item_layout.setContentsMargins(0, 0, 0, 0) # 移除所有边距
        item_layout.setSpacing(0) # 控件间距为0
        item_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignHCenter)

        hero_name = hero_detail.get('cn_name', 'N/A')
        icon_path = hero_detail.get('icon_path')
        avg_place = hero_detail.get('avg_place')
        play_rate = hero_detail.get('play_rate')

        # 1. 图标 (保持 IconLabel 的固定大小)
        icon_size = 60
        icon_label = IconLabel(data=hero_name, icon_size=icon_size, icon_type='hero', placeholder_text='?')
        icon_label.set_icon(icon_path, hero_name)
        icon_label.setToolTip(hero_name)
        icon_label.clicked.connect(lambda name=hero_name: self.hero_selected.emit(name))
        item_layout.addWidget(icon_label, 0, Qt.AlignmentFlag.AlignCenter)

        # --- 保持之前的修改：移除 SpacerItems ---
        # item_layout.addSpacerItem(QSpacerItem(1, 3, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed))

        # 2. 名称
        name_label = QLabel(hero_name)
        name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: {FONT_SIZE_NORMAL}px; font-weight: {FONT_WEIGHT_SEMIBOLD}; background-color: transparent; border: none; padding-top: 2px;") # 微调padding
        name_label.setWordWrap(True)
        name_label.setMaximumWidth(container_width - 8)
        item_layout.addWidget(name_label)

        # --- 保持之前的修改：移除 SpacerItems ---
        # item_layout.addSpacerItem(QSpacerItem(1, 1, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed))

        # 3. 出场率 (名称下方)
        play_rate_text = "--"
        if play_rate is not None:
            play_rate_text = f"出场率: {play_rate:.2f}"
        play_rate_label = QLabel(play_rate_text)
        play_rate_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        play_rate_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_SMALL}px; background-color: transparent; border: none;") # 确保无背景无边框
        item_layout.addWidget(play_rate_label)

        # --- 保持之前的修改：移除 SpacerItems ---
        # item_layout.addSpacerItem(QSpacerItem(1, 1, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed))

        # 4. 平均排名 (出场率下方)
        avg_place_text = "--"
        if avg_place is not None:
            avg_place_text = f"均名: {avg_place:.2f}" # 使用您改过的文字
        avg_place_label = QLabel(avg_place_text)
        avg_place_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        avg_place_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_SMALL}px; background-color: transparent; border: none;") # 确保无背景无边框
        item_layout.addWidget(avg_place_label)

        item_layout.addStretch(1) # 保留底部的 Stretch

        item_container.setCursor(Qt.CursorShape.PointingHandCursor)
        item_container.mousePressEvent = lambda event, name=hero_name: self.hero_selected.emit(name) if event.button() == Qt.MouseButton.LeftButton else None

        return item_container