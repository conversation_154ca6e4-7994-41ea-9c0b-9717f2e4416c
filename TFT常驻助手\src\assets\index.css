@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* TFT主题颜色 - 基于UI风格指导 */
    --background: 30 41% 15%; /* #1E293B */
    --foreground: 210 40% 90%; /* #E2E8F0 */
    --card: 30 41% 15%; /* #1E293B */
    --card-foreground: 210 40% 90%; /* #E2E8F0 */
    --popover: 30 41% 15%; /* #1E293B */
    --popover-foreground: 210 40% 90%; /* #E2E8F0 */
    --primary: 213 94% 68%; /* #8EBBFF */
    --primary-foreground: 222 84% 5%; /* #1A202C */
    --secondary: 215 25% 27%; /* #293548 */
    --secondary-foreground: 210 40% 90%; /* #E2E8F0 */
    --muted: 215 25% 27%; /* #293548 */
    --muted-foreground: 215 20% 65%; /* #A0AEC0 */
    --accent: 215 28% 17%; /* #334155 */
    --accent-foreground: 210 40% 90%; /* #E2E8F0 */
    --destructive: 0 84% 60%; /* #FF6B6B */
    --destructive-foreground: 210 40% 90%; /* #E2E8F0 */
    --border: 215 20% 30%; /* #4A5568 */
    --input: 215 28% 17%; /* #334155 */
    --ring: 213 94% 68%; /* #8EBBFF */
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

/* TFT特定样式 */
@layer components {
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #4A5568 #1E293B;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1E293B;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #4A5568;
    border-radius: 4px;
  }
  
  .icon-placeholder {
    background-color: #4A5568;
    color: #E2E8F0;
    border-radius: 0.25rem;
    border: 1px solid #4A5568;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .tier-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: bold;
  }
  
  .tier-s { background-color: #FF5A5F; color: #1A202C; }
  .tier-a { background-color: #FFA756; color: #1A202C; }
  .tier-b { background-color: #FFD700; color: #1A202C; }
  .tier-c { background-color: #F3FF59; color: #1A202C; }
  .tier-d { background-color: #8AFF40; color: #1A202C; }
  
  .cost-1 { background-color: #D3D3D3; color: #1A202C; }
  .cost-2 { background-color: #32CD32; color: #1A202C; }
  .cost-3 { background-color: #00BFFF; color: #1A202C; }
  .cost-4 { background-color: #BF00FF; color: #E2E8F0; }
  .cost-5 { background-color: #FFD700; color: #1A202C; }
}
