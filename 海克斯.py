# 海克斯评分.py
import os
import time
import json
import requests
import urllib.parse
from urllib.parse import urljoin
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from bs4 import BeautifulSoup
from pathlib import Path

def fetch_and_parse_hextech_data(url="https://www.metatft.com/augments", debug_mode=False, max_retries=3, timeout=30, download_icons=True):
    """从网站直接获取海克斯科技数据，使用Selenium模拟浏览器并解析数据
    
    Args:
        url: 要爬取的网站URL
        debug_mode: 是否开启调试模式（显示浏览器界面）
        max_retries: 最大重试次数
        timeout: 等待页面加载的超时时间（秒）
        download_icons: 是否下载海克斯图标
        
    Returns:
        tuple: (海克斯数据列表, 海克斯映射字典)
    """
    # 配置Chrome和ChromeDriver路径
    chrome_path = r"D:\chrome\chrome-win64\chrome.exe"  # 使用r前缀避免转义问题
    chrome_driver_path = r"D:\chrome\chromedriver-win64\chromedriver.exe"
    
    # 尝试自动查找Chrome和ChromeDriver
    if not os.path.exists(chrome_path) or not os.path.exists(chrome_driver_path):
        print("[警告] 未找到指定的Chrome或ChromeDriver路径，尝试使用系统默认路径")
        chrome_path = None
        chrome_driver_path = None
    
    for attempt in range(max_retries):
        driver = None
        try:
            # 配置Chrome选项
            chrome_options = Options()
            
            # 指定Chrome浏览器的路径(如果存在)
            if chrome_path:
                chrome_options.binary_location = chrome_path
            
            # 在调试模式下不使用无头模式
            if not debug_mode:
                chrome_options.add_argument("--headless=new")  # 新版无头模式
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument('--ignore-certificate-errors')  # 忽略SSL证书错误
            chrome_options.add_argument('--ignore-ssl-errors')  # 忽略SSL错误
            
            # 优化性能的配置
            chrome_options.add_argument("--disable-extensions")  # 禁用扩展
            chrome_options.add_argument("--disable-infobars")  # 禁用信息栏
            chrome_options.add_argument("--disable-notifications")  # 禁用通知
            chrome_options.add_argument("--disable-popup-blocking")  # 禁用弹窗拦截
            
            # 设置页面加载策略为只等待HTML解析完成，不等待样式、图片、帧等资源
            chrome_options.page_load_strategy = 'eager'
            
            # 添加自定义用户代理，使用更新的版本
            chrome_options.add_argument(f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            # 禁止加载某些资源类型（通过实验性选项）
            prefs = {
                "profile.default_content_setting_values.notifications": 2,  # 禁用通知
                "profile.managed_default_content_settings.cookies": 1,  # 允许cookies
                "profile.managed_default_content_settings.javascript": 1,  # 允许JavaScript
                "profile.managed_default_content_settings.plugins": 2,  # 禁用插件
                "profile.managed_default_content_settings.geolocation": 2,  # 禁用地理位置
                "profile.managed_default_content_settings.media_stream": 2,  # 禁用媒体流
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            print(f"[尝试 {attempt+1}/{max_retries}] 配置浏览器选项完成")
            
            # 初始化WebDriver
            if chrome_driver_path:
                driver = webdriver.Chrome(service=Service(chrome_driver_path), options=chrome_options)
            else:
                driver = webdriver.Chrome(options=chrome_options)
            
            # 设置脚本和页面加载超时
            driver.set_page_load_timeout(timeout)
            driver.set_script_timeout(timeout)
            
            # 访问网页
            print(f"[开始] 正在从 {url} 获取海克斯科技数据...")
            driver.get(url)
            
            # 等待页面主体加载完成
            print(f"[信息] 等待页面加载，超时设置为 {timeout} 秒...")
            
            # 添加一小段等待时间，确保页面初始化
            time.sleep(3)
            
            # 设置语言为中文
            print("[信息] 尝试将语言设置为中文...")
            try:
                # 使用JavaScript设置localStorage中的language值为zh_cn
                driver.execute_script("localStorage.setItem('language', 'zh_cn');")
                print("[信息] 已设置localStorage中的language为zh_cn")
                
                # 刷新页面以应用新的语言设置
                driver.refresh()
                print("[信息] 已刷新页面以应用语言设置")
                
                # 添加等待时间，确保页面重新加载
                time.sleep(5)
                
                # 使用显式等待而不是sleep
                wait = WebDriverWait(driver, 15)
                try:
                    # 等待页面中的某个特定元素出现
                    wait.until(EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'TierListRow')]")))
                except:
                    print("[警告] 等待TierListRow超时，继续处理")
                
                # 验证语言是否已切换
                current_language = driver.execute_script("return localStorage.getItem('language');")
                print(f"[信息] 当前语言设置: {current_language}")
                
                # 检查页面上的文本是否为中文
                page_text = driver.find_element(By.TAG_NAME, "body").text
                if "海克斯" in page_text or "强度" in page_text or "等级" in page_text:
                    print("[信息] 确认页面已切换为中文")
                else:
                    print("[警告] 页面可能未成功切换为中文")
            except Exception as e:
                print(f"[警告] 切换语言失败: {e}")
            
            # 尝试多种方法获取海克斯科技数据
            html_content = driver.page_source
            
            # 检查HTML内容是否包含预期数据
            if "augment" not in html_content.lower() and "海克斯" not in html_content:
                print("[警告] HTML内容可能不包含海克斯科技数据，尝试使用其他方法")
                
                # 尝试直接访问其他可能的URL
                alternative_urls = [
                    "https://www.metatft.com/cn/augments",
                    "https://www.op.gg/tft/augments",
                    "https://www.metasrc.com/tft/augments",
                    "https://app.mobalytics.gg/tft/tier-list/augments",
                    "https://tactics.tools/augments"
                ]
                
                for alt_url in alternative_urls:
                    try:
                        print(f"[信息] 尝试替代网址: {alt_url}")
                        driver.get(alt_url)
                        time.sleep(5)  # 等待页面加载
                        
                        # 获取新页面内容
                        alt_html = driver.page_source
                        if "augment" in alt_html.lower() or "海克斯" in alt_html:
                            print(f"[信息] 在替代网址找到可能的数据: {alt_url}")
                            html_content = alt_html
                            break
                    except Exception as alt_e:
                        print(f"[警告] 访问替代网址失败: {alt_e}")
                        continue
            
            # 使用多种解析方法
            print("[开始] 正在解析海克斯科技数据...")
            augments_data = parse_html_content(html_content, base_url=url, download_icons=download_icons)
            
            # 如果没有数据，尝试其他解析方法
            if not augments_data:
                print("[警告] 标准解析方法未能提取数据，尝试使用替代解析方法")
                
                # 尝试使用XPath直接提取
                try:
                    soup = BeautifulSoup(html_content, 'html.parser')
                    # 查找所有可能包含图像的元素
                    all_imgs = soup.find_all('img')
                    print(f"[信息] 找到 {len(all_imgs)} 个图片元素")
                    
                    for img in all_imgs:
                        name = img.get('alt', '') or img.get('title', '')
                        if not name:
                            continue
                            
                        icon_url = img.get('src', '')
                        if not icon_url:
                            continue
                            
                        # 确保URL是绝对路径
                        if icon_url and not icon_url.startswith(('http://', 'https://')):
                            icon_url = urljoin(url, icon_url)
                        
                        # 尝试判断是否为海克斯图标
                        if 'augment' in icon_url.lower() or 'hextech' in icon_url.lower() or '海克斯' in name:
                            local_icon_path = ""
                            if download_icons:
                                local_icon_path = download_icon(name, icon_url, max_retries=3)
                            
                            augment_data = {
                                "name": name,
                                "tier_rank": "未知",  # 无法确定等级
                                "icon_url": icon_url,
                                "icon_path": local_icon_path
                            }
                            augments_data.append(augment_data)
                    
                    print(f"[信息] 通过替代方法找到 {len(augments_data)} 个可能的海克斯科技")
                except Exception as parse_e:
                    print(f"[错误] 替代解析方法失败: {parse_e}")
            
            # 关闭浏览器
            driver.quit()
            driver = None
            
            if not augments_data:
                print("[错误] 所有解析方法均未能从HTML中提取海克斯科技数据")
                if attempt < max_retries - 1:
                    print(f"[信息] 将在 5 秒后进行第 {attempt+2} 次尝试...")
                    time.sleep(5)
                    continue
                return None, None
            
            # 生成映射数据
            mapping_data = create_mapping(augments_data)
            
            print(f"[成功] 已解析 {len(augments_data)} 条海克斯科技数据")
            return augments_data, mapping_data
            
        except Exception as e:
            print(f"[错误] 第 {attempt+1} 次尝试获取海克斯科技数据失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 确保关闭浏览器
            if driver:
                try:
                    driver.quit()
                except:
                    pass
            
            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries - 1:
                print(f"[信息] 将在 5 秒后进行第 {attempt+2} 次尝试...")
                time.sleep(5)
            else:
                return None, None

def parse_html_content(html_content, base_url, download_icons):
    """解析HTML内容，提取海克斯科技数据
    
    Args:
        html_content: HTML内容字符串
        base_url: 要爬取的网站URL
        download_icons: 是否下载海克斯图标
        
    Returns:
        list: 海克斯科技数据列表，每项包含 name、tier_rank 和 icon_url
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
    except Exception as e:
        print(f"[解析错误] 解析HTML失败: {str(e)}")
        return []
    
    augments = []
    
    # 使用多种解析策略获取数据
    # 策略1: 标准梯队行解析
    tier_rows = soup.find_all(class_="TierListRow")
    print(f"[解析器] 找到 {len(tier_rows)} 个梯队")
    
    if tier_rows:
        # 解析每个梯队行
        for row in tier_rows:
            try:
                tier_title = row.find(class_="TierListTierTitle")
                if not tier_title:
                    continue
                    
                tier_rank = tier_title.text.strip()
                
                # 尝试使用多种CSS选择器
                selectors = [
                    "div.TierListTierContent > div > div",
                    ".AugmentWrapper",
                    "div[class*='augment']",
                    "div[class*='Augment']",
                    "div.item"
                ]
                
                augment_items = []
                for selector in selectors:
                    items = row.select(selector)
                    if items:
                        augment_items = items
                        print(f"[信息] 使用选择器 '{selector}' 在梯队 {tier_rank} 中找到 {len(items)} 个海克斯科技")
                        break
                
                for item in augment_items:
                    # 尝试不同的方式找到图片元素
                    img_selectors = ["img.AugmentImg", "img", "image"]
                    img = None
                    
                    for img_selector in img_selectors:
                        found_img = item.select_one(img_selector)
                        if found_img:
                            img = found_img
                            break
                    
                    if not img:
                        continue
                    
                    # 获取海克斯科技名称    
                    name = img.get("alt", "")
                    if not name:
                        name = img.get("title", "")
                    
                    if not name:
                        # 尝试从周围元素获取名称
                        name_elements = item.select(".name, .title, h3, strong, span")
                        for name_el in name_elements:
                            if name_el.text.strip():
                                name = name_el.text.strip()
                                break
                    
                    if not name:
                        # 如果仍然没有名称，尝试从父元素的标题属性获取
                        name = item.get("title", "")
                    
                    if not name or name == "未知名称":
                        print("[警告] 无法获取海克斯科技名称，跳过")
                        continue
                        
                    name = name.strip()
                    
                    # 获取图片URL
                    icon_url = img.get("src", "")
                    # 确保URL是绝对路径
                    if icon_url and not icon_url.startswith(('http://', 'https://')):
                        icon_url = urljoin(base_url, icon_url)
                    
                    # 总是计算预期的本地相对路径
                    local_icon_path = ""
                    if icon_url:
                        # 安全处理文件名，替换不合法字符
                        safe_name = "".join([c if c.isalnum() or c in [' ', '.', '_', '-'] else '_' for c in name])
                        # 提取文件扩展名
                        file_ext = os.path.splitext(urllib.parse.urlparse(icon_url).path)[1]
                        if not file_ext:
                            file_ext = ".png"  # 默认扩展名
                        # 构建相对路径
                        local_icon_path = str(Path("海克斯数据") / "海克斯图标" / f"{safe_name}{file_ext}")
                    
                    # 仅当需要下载时才调用下载函数
                    if download_icons and icon_url:
                        download_success = download_icon(name, icon_url, local_icon_path, max_retries=3)
                        if not download_success:
                            print(f"[警告] 图标 {name} 下载失败，但路径 {local_icon_path} 仍会记录。")
                    
                    # 创建数据对象，包含名称、等级和图标URL
                    augment_data = {
                        "name": name,
                        "tier_rank": tier_rank,
                        "icon_url": icon_url,
                        "icon_path": local_icon_path
                    }
                    
                    # 检查是否已存在相同名称的数据
                    duplicate = False
                    for existing in augments:
                        if existing["name"] == name:
                            duplicate = True
                            break
                    
                    if not duplicate:
                        augments.append(augment_data)
                    
            except Exception as e:
                print(f"[行解析错误] 异常: {str(e)}")
                continue
    
    # 如果没有找到数据，尝试使用用户提供的选择器
    if not augments:
        print("[警告] 未找到任何梯队数据，尝试使用替代选择器")
        try:
            # 多种可能的选择器
            alternative_selectors = [
                "#content-wrap div.TierListTierContent > div > div",
                "div.augment-container",
                "div.hextech-container",
                "div[class*='augment']",
                "div[class*='Augment']",
                "div.tier-list div.item",
                "div.tierlist div.item",
                ".tier-container .item"
            ]
            
            # 尝试每个选择器
            for selector in alternative_selectors:
                augment_elements = soup.select(selector)
                if augment_elements:
                    print(f"[信息] 使用选择器 '{selector}' 找到 {len(augment_elements)} 个元素")
                    
                    tier_rank = "未知"  # 默认等级
                    
                    # 尝试找到等级信息
                    tier_containers = soup.select("div.TierListTierTitle, div.tier-title, div.tierTitle")
                    for tier_container in tier_containers:
                        if tier_container:
                            tier_rank = tier_container.text.strip()
                            break
                    
                    # 处理每个找到的元素
                    for element in augment_elements:
                        try:
                            # 尝试查找图片
                            img = element.find("img")
                            if not img:
                                continue
                            
                            # 获取名称
                            name = img.get("alt", "")
                            if not name:
                                name = img.get("title", "")
                            
                            if not name:
                                # 尝试从相邻元素获取名称
                                name_elements = element.select(".name, .title, h3, strong, span")
                                for name_el in name_elements:
                                    if name_el.text.strip():
                                        name = name_el.text.strip()
                                        break
                            
                            if not name:
                                continue
                                
                            name = name.strip()
                            
                            # 获取图片URL
                            icon_url = img.get("src", "")
                            # 确保URL是绝对路径
                            if icon_url and not icon_url.startswith(('http://', 'https://')):
                                icon_url = urljoin(base_url, icon_url)
                            
                            # 总是计算预期的本地相对路径
                            local_icon_path = ""
                            if icon_url:
                                # 安全处理文件名
                                safe_name = "".join([c if c.isalnum() or c in [' ', '.', '_', '-'] else '_' for c in name])
                                # 提取文件扩展名
                                file_ext = os.path.splitext(urllib.parse.urlparse(icon_url).path)[1]
                                if not file_ext:
                                    file_ext = ".png"
                                # 构建相对路径
                                local_icon_path = str(Path("海克斯数据") / "海克斯图标" / f"{safe_name}{file_ext}")
                            
                            # 仅当需要下载时才调用下载函数
                            if download_icons and icon_url:
                                download_success = download_icon(name, icon_url, local_icon_path, max_retries=3)
                                if not download_success:
                                    print(f"[警告] 图标 {name} 下载失败，但路径 {local_icon_path} 仍会记录。")
                            
                            # 创建数据对象
                            augment_data = {
                                "name": name,
                                "tier_rank": tier_rank,
                                "icon_url": icon_url,
                                "icon_path": local_icon_path
                            }
                            
                            # 检查是否已存在相同名称的数据
                            duplicate = False
                            for existing in augments:
                                if existing["name"] == name:
                                    duplicate = True
                                    break
                            
                            if not duplicate:
                                augments.append(augment_data)
                                
                        except Exception as e:
                            print(f"[警告] 解析元素失败: {e}")
                    
                    # 如果找到足够的数据，则停止尝试其他选择器
                    if len(augments) >= 10:
                        break
            
        except Exception as e:
            print(f"[警告] 使用替代选择器失败: {e}")
    
    # 策略3: 最后的尝试，直接查找所有img标签
    if not augments or len(augments) < 5:
        print("[警告] 未找到足够的数据，尝试直接查找所有图片元素")
        all_imgs = soup.find_all("img")
        print(f"[信息] 找到 {len(all_imgs)} 个图片元素")
        
        augment_keywords = ["augment", "hextech", "海克斯", "装备", "系统", "强化", "提升"]
        
        for img in all_imgs:
            try:
                # 获取alt或title属性作为名称
                name = img.get("alt", "") or img.get("title", "")
                
                # 获取src属性作为URL
                icon_url = img.get("src", "")
                
                # 只处理有名称和URL的图片
                if not name or not icon_url:
                    continue
                
                name = name.strip()
                
                # 过滤掉明显不是海克斯科技的图片
                is_augment = False
                for keyword in augment_keywords:
                    if keyword.lower() in name.lower() or keyword.lower() in icon_url.lower():
                        is_augment = True
                        break
                
                if not is_augment and not ("augment" in icon_url.lower() or "hextech" in icon_url.lower()):
                    continue
                
                # 确保URL是绝对路径
                if icon_url and not icon_url.startswith(('http://', 'https://')):
                    icon_url = urljoin(base_url, icon_url)
                
                # 总是计算预期的本地相对路径
                local_icon_path = ""
                if icon_url:
                    # 安全处理文件名
                    safe_name = "".join([c if c.isalnum() or c in [' ', '.', '_', '-'] else '_' for c in name])
                    # 提取文件扩展名
                    file_ext = os.path.splitext(urllib.parse.urlparse(icon_url).path)[1]
                    if not file_ext:
                        file_ext = ".png"
                    # 构建相对路径
                    local_icon_path = str(Path("海克斯数据") / "海克斯图标" / f"{safe_name}{file_ext}")
                
                # 仅当需要下载时才调用下载函数
                if download_icons and icon_url:
                    download_success = download_icon(name, icon_url, local_icon_path, max_retries=3)
                    if not download_success:
                        print(f"[警告] 图标 {name} 下载失败，但路径 {local_icon_path} 仍会记录。")
                
                # 创建数据对象
                augment_data = {
                    "name": name,
                    "tier_rank": "未知",  # 无法确定等级
                    "icon_url": icon_url,
                    "icon_path": local_icon_path
                }
                
                # 检查是否已存在相同名称的数据
                duplicate = False
                for existing in augments:
                    if existing["name"] == name:
                        duplicate = True
                        break
                
                if not duplicate:
                    augments.append(augment_data)
                    
            except Exception as e:
                continue
    
    # 如果找到的数据太少，可能是网页结构与预期不符
    if len(augments) < 3:
        print(f"[警告] 只找到 {len(augments)} 个海克斯科技，可能网页结构已更改")
    else:
        print(f"[成功] 共解析到 {len(augments)} 个海克斯科技")
    
    return augments

def download_icon(name, icon_url, file_path, max_retries=3, retry_delay=2):
    """下载海克斯科技图标，带有重试机制
    
    Args:
        name: 海克斯科技名称
        icon_url: 图标URL
        file_path: 完整的本地保存绝对路径
        max_retries: 最大重试次数
        retry_delay: 重试延迟时间（秒）
        
    Returns:
        bool: 是否下载成功
    """
    # 获取图标保存目录的绝对路径
    icons_dir = os.path.dirname(file_path)
    
    # 确保目录存在
    os.makedirs(icons_dir, exist_ok=True)
    
    # 构建相对路径用于日志和返回 (虽然外部已计算，这里保留用于日志可能更好)
    # 注意: Path对象需要绝对路径来正确计算相对路径，或者需要知道基准路径
    try:
        # 尝试从绝对路径反推相对路径 (假设基准目录是脚本所在目录的上级)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        relative_path = os.path.relpath(file_path, start=script_dir)
    except ValueError:
        # 如果无法计算相对路径（例如跨驱动器），使用文件名
        relative_path = os.path.basename(file_path)

    # 检查文件是否已存在
    if os.path.exists(file_path):
        # 验证文件完整性
        try:
            with open(file_path, "rb") as f:
                content = f.read()
            if len(content) > 100:  # 简单检查文件大小
                print(f"[跳过] 图标已存在且完整: {file_path}")
                return True # 已存在且完整，算下载成功
            else:
                print(f"[警告] 图标文件可能损坏，将重新下载: {file_path}")
                # 如果文件损坏，删除它并重新下载
                os.remove(file_path)
        except Exception as e:
            print(f"[警告] 检查图标文件时出错，将重新下载: {e}")
            try:
                os.remove(file_path)
            except:
                pass

    # 添加重试机制
    for attempt in range(max_retries):
        try:
            # 下载图片
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
            }
            response = requests.get(icon_url, headers=headers, timeout=30)
            response.raise_for_status()  # 如果请求不成功则抛出异常
            
            # 检查内容类型
            content_type = response.headers.get('Content-Type', '')
            if 'image' not in content_type.lower() and len(response.content) < 1000:
                raise Exception(f"下载的内容不是图片: {content_type}")
            
            # 保存图片
            with open(file_path, "wb") as f:
                f.write(response.content)
            
            # 验证保存的文件
            if os.path.getsize(file_path) < 100:  # 文件过小，可能下载失败
                raise Exception("下载的图片文件过小，可能不完整")
            
            print(f"[成功] 已保存图标: {file_path}")
            return True # 下载成功
        
        except Exception as e:
            print(f"[错误] 下载图标失败 ({name}) 尝试 {attempt+1}/{max_retries}: {str(e)}")
            if attempt < max_retries - 1:
                print(f"  将在 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                # 增加重试延迟时间，避免被限流
                retry_delay = min(retry_delay * 1.5, 10)
            else:
                print(f"[失败] 已达到最大重试次数，放弃下载图标: {name}")
                return False # 下载失败
    
    return False # 所有尝试都失败

def create_mapping(augments_data):
    """创建海克斯科技名称到等级的映射
    
    Args:
        augments_data: 海克斯科技数据列表
        
    Returns:
        dict: 海克斯科技名称到等级和图标的映射
    """
    mapping = {}
    for augment in augments_data:
        name = augment["name"]
        tier_rank = augment["tier_rank"]
        icon_url = augment.get("icon_url", "")
        icon_path = augment.get("icon_path", "")
        
        mapping[name] = {
            "tier_rank": tier_rank,
            "icon_url": icon_url,
            "icon_path": icon_path
        }
    
    return mapping

def save_to_json(data, output_path):
    """将数据保存为JSON文件
    
    Args:
        data: 要保存的数据
        output_path: 输出文件路径
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存JSON文件，使用UTF-8编码并进行格式化
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"[成功] 已保存数据到 {output_path}")
        return True
    except Exception as e:
        print(f"[错误] 保存数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_icon_integrity(augments_data, icons_dir):
    """检查海克斯图标的完整性，返回需要重新下载的图标列表
    
    Args:
        augments_data: 海克斯科技数据列表
        icons_dir: 图标目录路径
        
    Returns:
        list: 需要重新下载的海克斯科技名称和URL列表
    """
    missing_icons = []
    
    # 确保目录存在
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir, exist_ok=True)
        print(f"[信息] 创建图标目录: {icons_dir}")
    
    # 检查每个海克斯科技的图标是否存在
    for augment in augments_data:
        name = augment["name"]
        icon_url = augment.get("icon_url", "")
        icon_path = augment.get("icon_path", "")
        
        # 如果URL为空，跳过检查
        if not icon_url:
            continue
        
        # 提取文件扩展名
        file_ext = os.path.splitext(urllib.parse.urlparse(icon_url).path)[1]
        if not file_ext:
            file_ext = ".png"  # 默认扩展名
        
        # 安全处理文件名
        safe_name = "".join([c if c.isalnum() or c in [' ', '.', '_', '-'] else '_' for c in name])
        
        # 构建预期的图标路径
        expected_path = os.path.join(icons_dir, f"{safe_name}{file_ext}")
        
        # 检查图标是否存在
        icon_exists = os.path.exists(expected_path)
        
        # 如果图标路径不匹配或文件不存在，添加到缺失列表
        if not icon_exists or (icon_path and not os.path.exists(icon_path)):
            print(f"[缺失] 图标不存在: {name}")
            missing_icons.append((name, icon_url))
            continue
            
        # 检查文件是否完整
        try:
            if icon_exists:
                file_size = os.path.getsize(expected_path)
                if file_size < 100:  # 文件过小，可能损坏
                    print(f"[损坏] 图标文件过小: {name} ({file_size} 字节)")
                    missing_icons.append((name, icon_url))
        except Exception as e:
            print(f"[检查错误] 检查图标时出错 {name}: {e}")
            missing_icons.append((name, icon_url))
    
    return missing_icons

def main(debug_mode=False, max_retries=3, timeout=30, download_icons=True, force_update=False):
    """海克斯科技评分主函数
    
    Args:
        debug_mode: 是否开启调试模式（显示浏览器界面）
        max_retries: 最大重试次数
        timeout: 等待页面加载的超时时间（秒）
        download_icons: 是否下载海克斯图标
        force_update: 是否强制更新数据（忽略本地缓存）
        
    Returns:
        bool: 是否成功获取并保存数据
    """
    try:
        print("[开始] 海克斯科技评分获取程序启动")
        
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 设置输出路径
        data_dir = os.path.join(current_dir, "海克斯数据")
        mapping_dir = os.path.join(current_dir, "映射文件")
        icons_dir = os.path.join(data_dir, "海克斯图标")
        
        data_file = os.path.join(data_dir, "海克斯评分.json")
        mapping_file = os.path.join(mapping_dir, "海克斯映射.json")
        
        # 确保目录存在
        os.makedirs(data_dir, exist_ok=True)
        os.makedirs(mapping_dir, exist_ok=True)
        os.makedirs(icons_dir, exist_ok=True)
        
        # 先检查现有数据和图标
        existing_data = None
        existing_mapping = None
        do_full_update = force_update  # 默认跟随force_update参数

        # 读取现有数据
        if os.path.exists(data_file) and os.path.exists(mapping_file):
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    existing_mapping = json.load(f)
                print(f"[信息] 从本地文件加载了 {len(existing_data)} 条海克斯科技数据")
            except Exception as e:
                print(f"[警告] 读取本地海克斯数据失败，将全量更新: {e}")
                do_full_update = True
        else:
            print("[信息] 未找到现有数据文件，将进行全量更新")
            do_full_update = True
        
        # 处理图标下载逻辑
        icons_to_download = []
        
        if download_icons and existing_data and not do_full_update:
            # 如果选择下载图标但不是全量更新
            # 检查现有图标完整性，只下载缺失的图标
            print("[信息] 检查海克斯图标完整性...")
            icons_to_download = check_icon_integrity(existing_data, icons_dir)
            
            if icons_to_download:
                print(f"[信息] 发现 {len(icons_to_download)} 个缺失的图标，将单独下载")
                
                # 单独下载缺失的图标
                for name, icon_url in icons_to_download:
                    print(f"[下载] 正在下载图标: {name}")
                    local_path = download_icon(name, icon_url, max_retries=3)
                    
                    # 如果下载成功，更新数据中的图标路径
                    if local_path and existing_data:
                        for item in existing_data:
                            if item["name"] == name:
                                item["icon_path"] = local_path
                                if name in existing_mapping:
                                    existing_mapping[name]["icon_path"] = local_path
                
                # 保存更新后的数据
                save_to_json(existing_data, data_file)
                save_to_json(existing_mapping, mapping_file)
                
                print(f"[信息] 已更新 {len(icons_to_download)} 个图标的本地路径")
                
                # 如果只需要下载图标，可以直接返回
                if not do_full_update and not force_update:
                    print("[成功] 图标更新完成，使用现有数据")
                    return True
        
        # 如果是强制更新，删除现有数据文件
        if force_update:
            # 删除数据文件和映射文件
            if os.path.exists(data_file):
                os.remove(data_file)
                print(f"[信息] 强制更新模式：已删除旧的数据文件 {data_file}")
            
            if os.path.exists(mapping_file):
                os.remove(mapping_file)
                print(f"[信息] 强制更新模式：已删除旧的映射文件 {mapping_file}")
        
        # 检查图标目录是否存在并包含文件
        icons_exist = os.path.exists(icons_dir) and len(os.listdir(icons_dir)) > 0
        
        # 决定是否下载图标
        # 如果选择了下载图标，或者图标目录为空，且没有明确选择不下载图标
        actual_download_icons = download_icons or (not icons_exist and download_icons is not False)
        
        if actual_download_icons:
            print("[信息] 将下载海克斯图标")
        else:
            print("[信息] 跳过图标下载")
            
        augments_data, mapping_data = fetch_and_parse_hextech_data(
            debug_mode=debug_mode, 
            max_retries=max_retries, 
            timeout=timeout, 
            download_icons=actual_download_icons
        )
        
        if not augments_data or not mapping_data:
            print("[错误] 获取海克斯科技数据失败")
            # 如果获取新数据失败但有现有数据，返回部分成功
            if existing_data and existing_mapping:
                print("[信息] 使用现有数据")
                return True
            return False
        
        # 保存数据和映射
        save_to_json(augments_data, data_file)
        save_to_json(mapping_data, mapping_file)
        
        print(f"[统计] 总共获取了 {len(augments_data)} 条海克斯科技数据")
        print(f"[信息] 海克斯评分数据保存在: {data_file}")
        print(f"[信息] 海克斯映射保存在: {mapping_file}")
        
        print("[成功] 海克斯科技评分获取完成")
        return True
        
    except Exception as e:
        print(f"[严重错误] 海克斯评分获取过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# 修改获取海克斯数据的统一函数
def get_hextech_data(base_dir=None, debug_mode=False, max_retries=3, timeout=30, download_icons=False, force_update=False):
    """获取海克斯评分数据的统一入口函数，返回数据和映射
    
    Args:
        base_dir: 基础目录
        debug_mode: 是否开启调试模式
        max_retries: 最大重试次数
        timeout: 超时时间
        download_icons: 是否下载图标
        force_update: 是否强制更新数据（忽略本地缓存）
        
    Returns:
        tuple: (是否成功, 数据, 映射)
    """
    try:
        # 获取当前脚本所在目录
        current_dir = base_dir or os.path.dirname(os.path.abspath(__file__))
        
        # 设置输出路径
        data_dir = os.path.join(current_dir, "海克斯数据")
        mapping_dir = os.path.join(current_dir, "映射文件")
        
        data_file = os.path.join(data_dir, "海克斯评分.json")
        mapping_file = os.path.join(mapping_dir, "海克斯映射.json")
        
        # 如果文件已存在且不是强制更新模式，则直接读取
        if not force_update and os.path.exists(data_file) and os.path.exists(mapping_file):
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    augments_data = json.load(f)
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    mapping_data = json.load(f)
                print(f"从本地文件加载了 {len(augments_data)} 条海克斯科技数据")
                return True, augments_data, mapping_data
            except Exception as e:
                print(f"读取本地海克斯数据失败，将重新获取: {e}")
        
        # 重新获取数据
        success = main(debug_mode, max_retries, timeout, download_icons, force_update)
        if success:
            # 读取新生成的文件
            with open(data_file, 'r', encoding='utf-8') as f:
                augments_data = json.load(f)
            with open(mapping_file, 'r', encoding='utf-8') as f:
                mapping_data = json.load(f)
            return True, augments_data, mapping_data
        else:
            return False, None, None
    
    except Exception as e:
        print(f"获取海克斯数据时出错: {e}")
        return False, None, None

if __name__ == "__main__":
    # 添加命令行参数支持
    import argparse
    parser = argparse.ArgumentParser(description='海克斯科技数据爬取工具')
    parser.add_argument('--force', action='store_true', help='强制重新爬取数据')
    parser.add_argument('--icons', action='store_true', help='下载图标')
    parser.add_argument('--debug', action='store_true', help='开启调试模式')
    
    args = parser.parse_args()
    
    success = main(
        debug_mode=args.debug,
        download_icons=args.icons,
        force_update=args.force
    )
    
    if success:
        print("海克斯科技评分数据获取成功！")
    else:
        print("海克斯科技评分数据获取失败！") 