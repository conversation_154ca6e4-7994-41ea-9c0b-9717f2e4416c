# -*- coding: utf-8 -*-
"""
自动更新模块

负责检查应用和数据库的更新。
- 从远程URL获取最新的 version.json。
- 与本地的 local_version.json 进行比较。
- 如果应用有新版本，弹窗提示用户。
- 如果数据库有新版本，自动在后台下载并替换。
"""
import os
import sys
import json
import hashlib
import requests
import tkinter as tk
from tkinter import messagebox
from packaging.version import parse as parse_version
import webbrowser
import config # 导入配置模块

# --- 配置常量 ---
# 从config模块获取可写目录路径
LOCAL_VERSION_FILE = os.path.join(config.APP_PATH, "local_version.json")
DB_FILE_PATH = os.path.join(config.APP_PATH, "tft_data.db")

# [修改] 远程制品库的基础URL
REMOTE_REPO_BASE_URL = "https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/"
# [修改] 远程正式版本文件的URL，基于基础URL构建
REMOTE_VERSION_URL = REMOTE_REPO_BASE_URL + "version.json"

def _get_local_version():
    """读取本地版本文件，如果文件不存在则返回一个初始版本对象。"""
    if not os.path.exists(LOCAL_VERSION_FILE):
        return {"app": {"version": "0.0.0"}, "database": {"version": "0"}}
    try:
        with open(LOCAL_VERSION_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        # 如果文件损坏或无法读取，也返回初始版本，强制进行更新检查
        return {"app": {"version": "0.0.0"}, "database": {"version": "0"}}

def _get_remote_version(url=None):
    """从指定的URL获取版本信息。"""
    target_url = url if url else REMOTE_VERSION_URL
    try:
        response = requests.get(target_url, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"获取远程版本失败: {e}")
        return None
    except json.JSONDecodeError:
        print(f"远程 {target_url} 格式错误。")
        return None

def _calculate_md5(file_path, chunk_size=8192):
    """计算文件的MD5校验和。"""
    md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            while chunk := f.read(chunk_size):
                md5.update(chunk)
        return md5.hexdigest()
    except IOError:
        return None

def _handle_db_update(remote_db_info):
    """处理数据库更新。"""
    db_url = remote_db_info.get("url")
    remote_checksum = remote_db_info.get("checksum")
    if not db_url or not remote_checksum:
        messagebox.showerror("更新错误", "数据库更新信息不完整。")
        return False
        
    print("检测到新版数据库，开始后台下载...")
    
    temp_db_path = DB_FILE_PATH + ".tmp"
    
    try:
        with requests.get(db_url, stream=True, timeout=60) as r:
            r.raise_for_status()
            with open(temp_db_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
        
        print("数据库下载完成，正在校验文件...")
        local_checksum = _calculate_md5(temp_db_path)

        if local_checksum == remote_checksum:
            print("校验成功，正在替换旧文件...")
            os.replace(temp_db_path, DB_FILE_PATH)
            messagebox.showinfo("更新完成", f"数据库已成功更新到版本 {remote_db_info['version']}！")
            return True
        else:
            print(f"校验失败！本地: {local_checksum}, 远程: {remote_checksum}")
            messagebox.showerror("更新失败", "数据库文件校验失败，请重试或联系管理员。")
            os.remove(temp_db_path)
            return False

    except requests.exceptions.RequestException as e:
        print(f"数据库下载失败: {e}")
        messagebox.showerror("下载失败", f"下载数据库文件时发生网络错误: {e}")
        if os.path.exists(temp_db_path):
            os.remove(temp_db_path)
        return False

def check_db_only():
    """
    [新增] 仅检查数据库更新的函数。
    供主程序在后台调用。
    """
    print("正在后台检查数据库更新...")
    remote_version_info = _get_remote_version()
    if not remote_version_info:
        return

    local_version_info = _get_local_version()
    
    remote_db_info = remote_version_info.get("database", {})
    remote_db_version = str(remote_db_info.get("version", "0"))
    local_db_version = str(local_version_info.get("database", {}).get("version", "0"))

    # 如果数据库文件不存在，也强制更新
    if remote_db_version > local_db_version or not os.path.exists(DB_FILE_PATH):
        print(f"后台发现新数据库版本: {remote_db_version}")
        if _handle_db_update(remote_db_info):
            # 更新成功后，更新本地版本文件
            local_version_info["database"] = remote_db_info
            try:
                with open(LOCAL_VERSION_FILE, 'w', encoding='utf-8') as f:
                    json.dump(local_version_info, f, ensure_ascii=False, indent=2)
            except IOError as e:
                print(f"写入本地版本文件失败: {e}")
    else:
        print("数据库已是最新版本。")