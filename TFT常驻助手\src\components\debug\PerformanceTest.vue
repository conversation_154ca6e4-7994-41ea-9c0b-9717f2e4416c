<template>
  <div class="performance-test">
    <h2>性能测试对比</h2>
    
    <div class="test-controls">
      <button @click="runAllTests" :disabled="isRunning">
        {{ isRunning ? '测试中...' : '运行所有测试' }}
      </button>
      <button @click="clearResults">清除结果</button>
      <button @click="() => clearCache()">清除缓存</button>
    </div>

    <div class="test-results">
      <h3>测试结果</h3>
      <div v-if="testResults.length === 0" class="no-results">
        暂无测试结果
      </div>
      <div v-else class="results-grid">
        <div v-for="result in testResults" :key="result.name" class="result-item">
          <h4>{{ result.name }}</h4>
          <div class="metrics">
            <div class="metric">
              <span class="label">首次加载:</span>
              <span class="value" :class="getPerformanceClass(result.firstLoad)">
                {{ result.firstLoad }}ms
              </span>
            </div>
            <div class="metric">
              <span class="label">缓存加载:</span>
              <span class="value" :class="getPerformanceClass(result.cachedLoad)">
                {{ result.cachedLoad }}ms
              </span>
            </div>
            <div class="metric">
              <span class="label">数据量:</span>
              <span class="value">{{ result.dataSize }}条</span>
            </div>
            <div class="metric">
              <span class="label">性能提升:</span>
              <span class="value improvement">
                {{ calculateImprovement(result.firstLoad, result.cachedLoad) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="comparison">
      <h3>与Python版本对比</h3>
      <div class="comparison-note">
        <p>Python版本特点：</p>
        <ul>
          <li>直接内存缓存，几乎无延迟（&lt;5ms）</li>
          <li>同步UI更新，无loading状态</li>
          <li>简单的数据库连接</li>
        </ul>
        <p>当前Rust+Vue版本问题：</p>
        <ul>
          <li>IPC通信开销（每次调用约10-50ms）</li>
          <li>异步加载导致的loading状态</li>
          <li>序列化/反序列化开销</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useData } from '@/composables/useData'
import { performanceMonitor } from '@/utils/performanceMonitor'

interface TestResult {
  name: string
  firstLoad: number
  cachedLoad: number
  dataSize: number
}

const { getHeroList, getItemList, getHexList, clearCache } = useData()
const isRunning = ref(false)
const testResults = ref<TestResult[]>([])

const runAllTests = async () => {
  isRunning.value = true
  testResults.value = []
  
  try {
    // 清除缓存确保首次加载测试准确
    clearCache()
    
    // 测试英雄列表
    await testDataLoad('英雄列表', getHeroList)
    
    // 测试装备列表  
    await testDataLoad('装备列表', getItemList)
    
    // 测试海克斯列表
    await testDataLoad('海克斯列表', getHexList)
    
    performanceMonitor.printReport()
  } finally {
    isRunning.value = false
  }
}

const testDataLoad = async (name: string, loadFn: () => Promise<any[]>) => {
  // 首次加载测试
  const firstStart = performance.now()
  const firstData = await loadFn()
  const firstEnd = performance.now()
  const firstLoad = firstEnd - firstStart
  
  // 缓存加载测试
  const cachedStart = performance.now()
  const cachedData = await loadFn()
  const cachedEnd = performance.now()
  const cachedLoad = cachedEnd - cachedStart
  
  testResults.value.push({
    name,
    firstLoad: Math.round(firstLoad),
    cachedLoad: Math.round(cachedLoad),
    dataSize: firstData?.length || 0
  })
}

const getPerformanceClass = (time: number): string => {
  if (time < 10) return 'excellent'
  if (time < 50) return 'good'
  if (time < 100) return 'fair'
  return 'poor'
}

const calculateImprovement = (first: number, cached: number): string => {
  if (first <= cached) return '无提升'
  const improvement = ((first - cached) / first * 100).toFixed(1)
  return `${improvement}%`
}

const clearResults = () => {
  testResults.value = []
  performanceMonitor.clear()
}
</script>

<style scoped>
.performance-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.test-controls button {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  background: #007acc;
  color: white;
  cursor: pointer;
}

.test-controls button:disabled {
  background: #666;
  cursor: not-allowed;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.result-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background: #f9f9f9;
}

.metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric {
  display: flex;
  justify-content: space-between;
}

.label {
  font-weight: bold;
}

.value.excellent { color: #28a745; }
.value.good { color: #17a2b8; }
.value.fair { color: #ffc107; }
.value.poor { color: #dc3545; }
.value.improvement { color: #28a745; font-weight: bold; }

.comparison-note {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  border-left: 4px solid #007acc;
}

.comparison-note ul {
  margin: 10px 0;
  padding-left: 20px;
}
</style>
