# TFT常驻助手重构需求文档

## 项目简介

将现有的基于PySide6的TFT常驻助手重构为现代化的桌面应用，采用Tauri + Vue 3 + TypeScript + Tailwind CSS技术栈，实现更小的打包体积、更流畅的用户交互和更快的响应速度。新应用将创建在TFT常驻助手文件夹中，与现有的yimiaojue文件夹（OCR功能）并列，重构范围仅包括主窗口和视图模块。

## 需求

### 需求1：技术栈迁移

**用户故事：** 作为开发者，我希望将应用从PySide6迁移到Tauri + Vue 3，以便获得更好的性能和更小的打包体积。

#### 验收标准

1. WHEN 选择技术栈 THEN 系统 SHALL 使用Tauri作为后端框架
2. WHEN 开发前端界面 THEN 系统 SHALL 使用Vue 3 + TypeScript + Tailwind CSS + Vite
3. WHEN 处理数据存储 THEN 系统 SHALL 继续使用现有的SQLite数据库结构和数据
4. WHEN 创建项目结构 THEN 系统 SHALL 在TFT常驻助手文件夹中创建Tauri项目
5. WHEN 构建应用 THEN 系统 SHALL 生成小于30MB的可执行文件

### 需求2：主窗口功能复刻

**用户故事：** 作为用户，我希望重构后的主窗口保持现有的所有功能和交互逻辑。

#### 验收标准

1. WHEN 用户打开应用 THEN 系统 SHALL 显示无边框窗口，包含控制条、导航栏和内容区域
2. WHEN 用户点击控制条 THEN 系统 SHALL 支持窗口展开/收起切换
3. WHEN 用户拖拽控制条 THEN 系统 SHALL 支持窗口拖动
4. WHEN 用户点击返回按钮 THEN 系统 SHALL 根据历史记录返回上一页面
5. WHEN 用户点击关闭按钮 THEN 系统 SHALL 关闭应用程序
6. WHEN 窗口收起时 THEN 系统 SHALL 隐藏导航栏和内容区域，仅显示控制条

### 需求3：导航功能复刻

**用户故事：** 作为用户，我希望重构后的导航功能与现有应用完全一致。

#### 验收标准

1. WHEN 用户查看导航栏 THEN 系统 SHALL 显示阵容、英雄、装备、海克斯四个导航按钮
2. WHEN 用户点击导航按钮 THEN 系统 SHALL 切换到对应的列表页面
3. WHEN 用户在详情页时 THEN 系统 SHALL 取消所有导航按钮的选中状态
4. WHEN 用户从详情页返回 THEN 系统 SHALL 恢复对应导航按钮的选中状态
5. WHEN 用户切换主页面 THEN 系统 SHALL 清空历史记录栈

### 需求4：视图模块功能复刻

**用户故事：** 作为用户，我希望重构后的各个视图模块保持现有的所有功能。

#### 验收标准

1. WHEN 用户查看阵容列表 THEN 系统 SHALL 显示所有阵容信息，支持点击查看详情
2. WHEN 用户查看英雄列表 THEN 系统 SHALL 显示所有英雄信息，支持点击查看详情
3. WHEN 用户查看装备列表 THEN 系统 SHALL 显示所有装备信息，支持点击查看详情
4. WHEN 用户查看海克斯列表 THEN 系统 SHALL 显示所有海克斯信息
5. WHEN 用户在详情页点击相关项目 THEN 系统 SHALL 跳转到对应的详情页面
6. WHEN 用户查看详情页 THEN 系统 SHALL 显示完整的详细信息和相关链接

### 需求5：数据库集成保持

**用户故事：** 作为开发者，我希望重构后的应用继续使用现有的数据库操作逻辑。

#### 验收标准

1. WHEN 系统启动 THEN 系统 SHALL 使用现有的数据库文件和表结构
2. WHEN 系统查询数据 THEN 系统 SHALL 复用现有的异步查询框架
3. WHEN 系统缓存数据 THEN 系统 SHALL 使用现有的缓存机制
4. WHEN 系统预加载数据 THEN 系统 SHALL 保持现有的全局数据预加载逻辑

### 需求6：自定义组件复刻

**用户故事：** 作为开发者，我希望重构后的应用保持现有的自定义组件功能。

#### 验收标准

1. WHEN 系统显示图标 THEN 系统 SHALL 使用现有的图标缓存和加载逻辑
2. WHEN 用户点击可点击元素 THEN 系统 SHALL 保持现有的点击交互逻辑
3. WHEN 用户滚动列表 THEN 系统 SHALL 支持水平滚动和鼠标滚轮控制
4. WHEN 用户使用搜索功能 THEN 系统 SHALL 提供带清除按钮的搜索框
5. WHEN 系统显示占位符 THEN 系统 SHALL 使用现有的占位符样式和逻辑

### 需求7：样式和配置保持

**用户故事：** 作为用户，我希望重构后的应用保持现有的视觉风格和配置。

#### 验收标准

1. WHEN 系统显示界面 THEN 系统 SHALL 使用现有的颜色配置和主题
2. WHEN 系统显示文本 THEN 系统 SHALL 使用现有的字体配置
3. WHEN 系统显示评级 THEN 系统 SHALL 使用现有的评级颜色配置
4. WHEN 系统管理资源 THEN 系统 SHALL 使用现有的内存管理和缓存配置