<template>
  <div class="hex-detail-page">
    <!-- 返回按钮 -->
    <div class="back-section">
      <button @click="goBack" class="back-button">
        ← 返回海克斯列表
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载海克斯详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">⚠️</div>
      <h3>加载失败</h3>
      <p>{{ error }}</p>
      <button @click="loadHexDetail" class="retry-button">重新加载</button>
    </div>

    <!-- 海克斯详情内容 -->
    <div v-else-if="hexDetail" class="hex-detail-content">
      
      <!-- 海克斯基本信息卡片 -->
      <div class="hex-info-card">
        <div class="hex-header">
          <!-- 海克斯图标 -->
          <div class="hex-icon-section">
            <HexIcon
              :hex-name="hexDetail.name"
              :icon-path="hexDetail.icon_path"
              :tier="hexDetail.tier"
              :size="120"
              :show-tier-badge="true"
              :clickable="false"
            />
          </div>
          
          <!-- 海克斯基本信息 -->
          <div class="hex-basic-info">
            <h1 class="hex-name">{{ hexDetail.name }}</h1>
            <div class="hex-tier-info">
              <span 
                class="tier-badge-large"
                :class="getTierBadgeClass(hexDetail.tier)"
              >
                {{ hexDetail.tier }}级
              </span>
            </div>
          </div>
        </div>

        <!-- 海克斯描述 -->
        <div v-if="hexDetail.description" class="hex-description-section">
          <h3 class="section-title">描述</h3>
          <div class="description-content">
            {{ hexDetail.description }}
          </div>
        </div>

        <!-- 海克斯效果 -->
        <div v-if="hexDetail.effect" class="hex-effect-section">
          <h3 class="section-title">效果</h3>
          <div class="effect-content">
            {{ hexDetail.effect }}
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import HexIcon from '@/components/common/HexIcon.vue'

// Props
interface Props {
  hexName: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  back: []
}>()

// 海克斯详情数据接口
interface HexDetailData {
  name: string
  tier: string
  icon_url?: string
  icon_path?: string
  description?: string
  effect?: string
}

// === 响应式数据 ===
const hexDetail = ref<HexDetailData | null>(null)
const isLoading = ref(true)
const error = ref<string | null>(null)

// === 样式辅助函数 ===
const getTierBadgeClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s-badge-large',
    'A': 'tier-a-badge-large',
    'B': 'tier-b-badge-large',
    'C': 'tier-c-badge-large',
    'D': 'tier-d-badge-large'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-default-badge-large'
}

// === 返回处理 ===
const goBack = () => {
  emit('back')
}

// === 数据加载 ===
const loadHexDetail = async () => {
  if (!props.hexName) {
    error.value = '海克斯名称不能为空'
    isLoading.value = false
    return
  }

  try {
    isLoading.value = true
    error.value = null
    console.log('🔍 开始加载海克斯详情:', props.hexName)
    
    const results = await invoke('get_hex_detail', { hexName: props.hexName })
    console.log('🔮 海克斯详情加载结果:', results)
    
    // 检查是否是QueryResult格式
    if (results && typeof results === 'object' && 'data' in results) {
      if ('error' in results && results.error) {
        console.error('❌ 数据库查询错误:', results.error)
        error.value = String(results.error)
        return
      }
      
      if (Array.isArray(results.data) && results.data.length > 0) {
        hexDetail.value = results.data[0]
        console.log('✅ 成功加载海克斯详情:', hexDetail.value)
      } else {
        error.value = '未找到该海克斯的详细信息'
      }
    } else if (Array.isArray(results) && results.length > 0) {
      hexDetail.value = results[0]
      console.log('✅ 成功加载海克斯详情:', hexDetail.value)
    } else {
      error.value = '未找到该海克斯的详细信息'
    }
  } catch (err) {
    console.error('❌ 加载海克斯详情失败:', err)
    error.value = `加载失败: ${String(err)}`
  } finally {
    isLoading.value = false
  }
}

// === 监听hexName变化 ===
watch(() => props.hexName, () => {
  if (props.hexName) {
    loadHexDetail()
  }
}, { immediate: true })

// === 组件挂载时加载数据 ===
onMounted(() => {
  if (props.hexName) {
    loadHexDetail()
  }
})
</script>

<style scoped>
/* === 海克斯详情页面 === */
.hex-detail-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  gap: 1rem;
  overflow: hidden;
}

/* === 返回按钮区域 === */
.back-section {
  flex-shrink: 0;
}

.back-button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
}

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 错误状态 === */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 海克斯详情内容 === */
.hex-detail-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.hex-detail-content::-webkit-scrollbar {
  width: 6px;
}

.hex-detail-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.hex-detail-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.hex-detail-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 海克斯信息卡片 === */
.hex-info-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
}

/* === 海克斯头部 === */
.hex-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.hex-icon-section {
  flex-shrink: 0;
}

.hex-basic-info {
  flex: 1;
}

.hex-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hex-tier-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.tier-badge-large {
  padding: 0.5rem 1rem;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.tier-s-badge-large { background: linear-gradient(135deg, #ff6b9d, #ff4757); }
.tier-a-badge-large { background: linear-gradient(135deg, #ffa726, #ff8f00); }
.tier-b-badge-large { background: linear-gradient(135deg, #ffeb3b, #ffc107); }
.tier-c-badge-large { background: linear-gradient(135deg, #cddc39, #8bc34a); }
.tier-d-badge-large { background: linear-gradient(135deg, #4caf50, #2e7d32); }
.tier-default-badge-large { background: linear-gradient(135deg, #666666, #444444); }

/* === 描述和效果区域 === */
.hex-description-section,
.hex-effect-section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.75rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.description-content,
.effect-content {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  padding: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-wrap;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .hex-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .hex-name {
    font-size: 2rem;
  }
  
  .hex-info-card {
    padding: 1.5rem;
  }
}
</style>