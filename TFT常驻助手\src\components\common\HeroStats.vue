<template>
  <div class="hero-stats" @mouseenter="handleMouseEnter($event)" @mouseleave="handleMouseLeave" @mousemove="handleMouseMove($event)">
    <div
      class="stats-container"
      :style="{ backgroundColor: playRateData.color + 'B0', border: `1px solid ${playRateData.color}` }"
    >
      <!-- 出场率 -->
      <div class="stat-section play-rate-section">
        <div class="stat-value">
          {{ formatPlayRate(playRateData.value) }}
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="stat-divider"></div>

      <!-- 平均排名 -->
      <div class="stat-section avg-place-section">
        <div class="stat-value">
          {{ formatDisplayData(avgPlaceData.value, 'avgPlace') }}
        </div>
      </div>
    </div>

    <!-- 侧边显示的Tooltip -->
    <Teleport to="body">
      <div
        v-if="showTooltip"
        class="tooltip-sidebar"
        :style="{
          left: tooltipPosition.x + 'px',
          top: tooltipPosition.y + 'px'
        }"
      >
        <div class="tooltip-item">
          <strong>出场率:</strong> {{ formatPlayRate(playRateData.value) }}
          <span class="tooltip-level" :style="{ color: playRateData.color }">({{ getLevelDescription(playRateData.level) }})</span>
        </div>
        <div class="tooltip-item">
          <strong>平均排名:</strong> {{ formatDisplayData(avgPlaceData.value, 'avgPlace') }}
          <span class="tooltip-level" :style="{ color: avgPlaceData.color }">({{ getLevelDescription(avgPlaceData.level) }})</span>
        </div>
        <div v-if="top4RateData" class="tooltip-item">
          <strong>前四率:</strong> {{ formatDisplayData(top4RateData.value, 'top4Rate') }}
          <span class="tooltip-level" :style="{ color: top4RateData.color }">({{ getLevelDescription(top4RateData.level) }})</span>
        </div>
        <div v-if="top1RateData" class="tooltip-item">
          <strong>登顶率:</strong> {{ formatDisplayData(top1RateData.value, 'top1Rate') }}
          <span class="tooltip-level" :style="{ color: top1RateData.color }">({{ getLevelDescription(top1RateData.level) }})</span>
        </div>
        <div class="tooltip-note">
          等级由同费用英雄对比得出
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, Teleport } from 'vue'
import type { PercentileData } from '@/utils/dataAnalysis'
import { formatDisplayData, getLevelDescription } from '@/utils/dataAnalysis'

// Props
interface Props {
  playRateData: PercentileData
  avgPlaceData: PercentileData
  top4RateData?: PercentileData
  top1RateData?: PercentileData
  compact?: boolean
  heroIndex?: number
  totalHeroes?: number
}

withDefaults(defineProps<Props>(), {
  compact: false,
  heroIndex: 0,
  totalHeroes: 1
})

// 响应式数据
const showTooltip = ref(false)
const tooltipPosition = ref({ x: 0, y: 0 })

// 格式化出场率：直接使用数据库原始数据，不做转换
const formatPlayRate = (rate: number): string => {
  return rate.toFixed(2)
}

// 更新tooltip位置，参考海克斯页面的逻辑
const updateTooltipPosition = (event: MouseEvent) => {
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const tooltipWidth = 250 // 提示框的大概宽度
  const tooltipHeight = 120 // 提示框的大概高度
  const offsetX = 10 // 水平偏移距离

  // 如果鼠标在屏幕右半部分，则将提示框显示在左侧
  const showOnLeft = event.clientX > windowWidth / 2

  // 如果鼠标在屏幕下方75%的位置，则将提示框显示在上方
  const showAbove = event.clientY > windowHeight * 0.75

  tooltipPosition.value = {
    x: showOnLeft ? event.clientX - tooltipWidth - offsetX : event.clientX + offsetX,
    y: showAbove ? event.clientY - tooltipHeight - 10 : event.clientY - tooltipHeight / 2
  }
}

// 鼠标事件处理
const handleMouseEnter = (event: MouseEvent) => {
  showTooltip.value = true
  updateTooltipPosition(event)
}

const handleMouseLeave = () => {
  showTooltip.value = false
}

const handleMouseMove = (event: MouseEvent) => {
  if (showTooltip.value) {
    updateTooltipPosition(event)
  }
}
</script>

<style scoped>
.hero-stats {
  position: relative;
  cursor: pointer;
  padding: 0.25rem 0;
  z-index: 10;
}

.hero-stats:hover {
  z-index: 1000;
}

.stats-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  padding: 0.25rem 0.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  height: 24px;
}

.stat-section {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

/* 出场率和平均排名区域样式已在.stat-section中定义 */

.stat-value {
  font-weight: 600;
  font-size: 11px;
  color: white;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
  letter-spacing: 0.5px;
}

.stat-divider {
  width: 1px;
  height: 16px;
  background: rgba(255, 255, 255, 0.5);
  flex-shrink: 0;
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.3);
  margin: 0 2px;
}

/* 侧边显示的tooltip样式 */
.tooltip-sidebar {
  position: fixed;
  background: rgba(0, 0, 0, 0.95);
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 9999;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 220px;
  pointer-events: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 旧的tooltip样式已移除，使用新的侧边显示样式 */

.tooltip-item {
  margin-bottom: 0.25rem;
}

.tooltip-item:last-of-type {
  margin-bottom: 0.5rem;
}

.tooltip-level {
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.tooltip-note {
  font-size: 9px;
  opacity: 0.7;
  font-style: italic;
}

/* 紧凑模式 */
.hero-stats.compact .stat-value {
  font-size: 10px;
}

.hero-stats.compact .stat-divider {
  height: 14px;
}

.hero-stats.compact .stats-container {
  padding: 0.2rem 0.375rem;
  height: 22px;
}
</style>