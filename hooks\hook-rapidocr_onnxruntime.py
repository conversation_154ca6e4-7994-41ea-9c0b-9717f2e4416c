# -*- coding: utf-8 -*-
# -----------------------------------------------------------------------------
# hook-rapidocr_onnxruntime.py
#
# PyInstaller hook for rapidocr_onnxruntime
# -----------------------------------------------------------------------------

from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 搜集 rapidocr_onnxruntime 包内的所有数据文件（如 .yaml, .onnx）
datas = collect_data_files('rapidocr_onnxruntime', include_py_files=True)

# 搜集 rapidocr_onnxruntime 包内的所有子模块
hiddenimports = collect_submodules('rapidocr_onnxruntime')

# 手动添加一些 PyInstaller 可能仍然找不到的、RapidOCR 的间接依赖
# 这是为了确保万无一失
hiddenimports += [
    'onnxruntime',
    'pyclipper',
    'PyYAML',
    'shapely',
    'six'
]