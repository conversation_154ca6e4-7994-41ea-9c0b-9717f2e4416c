import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { useDataStore } from './stores/data'
import { invoke } from '@tauri-apps/api/core'
import { getSmartIconPath } from './utils/iconUtils'

import App from './App.vue'
import './assets/index.css'

// 预加载海克斯图标函数
const preloadHexIcons = async (hexes: any[]) => {
  console.log(`🖼️ 主应用预加载${hexes.length}个海克斯图标...`)
  try {
    await Promise.allSettled(
      hexes.map(hex => {
        if (hex.icon_path) {
          return getSmartIconPath(hex.icon_path)
        }
        return Promise.resolve(null)
      })
    )
    console.log(`✅ 主应用海克斯图标预加载完成`)
  } catch (error) {
    console.warn('海克斯图标预加载失败:', error)
  }
}

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)

// 先正常挂载应用，避免白屏问题
app.mount('#app')

// 应用挂载后立即进行数据预加载，实现Python版本的效果
setTimeout(async () => {
  try {
    console.log('🚀 应用已挂载，开始激进预加载所有数据...')

    const dataStore = useDataStore()

    // 并行预加载所有基础数据到前端缓存
    const [heroResults, itemResults, hexResults] = await Promise.all([
      invoke('get_hero_list'),
      invoke('get_item_list'),
      invoke('get_hex_list')
    ])

    // 直接设置到缓存中，确保后续访问零延迟
    if (heroResults && !(heroResults as any).error) {
      const heroData = (heroResults as any).data || heroResults
      dataStore.setCachedQuery('hero_list', heroData)
      console.log(`✅ 英雄数据预加载完成，共${heroData.length}条`)
    } else {
      console.error('❌ 英雄数据预加载失败:', heroResults)
    }

    if (itemResults && !(itemResults as any).error) {
      const itemData = (itemResults as any).data || itemResults
      dataStore.setCachedQuery('item_list', itemData)
      console.log(`✅ 装备数据预加载完成，共${itemData.length}条`)
    } else {
      console.error('❌ 装备数据预加载失败:', itemResults)
    }

    if (hexResults && !(hexResults as any).error) {
      const hexData = (hexResults as any).data || hexResults
      dataStore.setCachedQuery('hex_list_fast', hexData)
      console.log(`✅ 海克斯数据预加载完成，共${hexData.length}条`)

      // 预加载前20个海克斯图标
      setTimeout(() => {
        preloadHexIcons(hexData.slice(0, 20))
      }, 200)
    } else {
      console.error('❌ 海克斯数据预加载失败:', hexResults)
    }

    console.log('🎉 所有数据预加载完成，后续访问将实现零延迟！')

    // 验证缓存是否正确设置
    setTimeout(() => {
      console.log('🔍 验证缓存状态:')
      console.log('- 英雄缓存:', !!dataStore.getCachedQuery('hero_list'))
      console.log('- 装备缓存:', !!dataStore.getCachedQuery('item_list'))
      console.log('- 海克斯缓存:', !!dataStore.getCachedQuery('hex_list_fast'))
      console.log('- 所有缓存键:', Object.keys(dataStore.queryCache))
    }, 1000)
  } catch (error) {
    console.error('❌ 数据预加载失败:', error)
  }
}, 50) // 减少延迟到50ms
