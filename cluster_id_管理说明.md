# cluster_id 管理说明

## 📋 概述

由于MetaTFT网站的cluster_id会定期更新（通常每个游戏版本更新时），自动获取功能不稳定，因此改为**手动管理模式**，更加可靠和稳定。

## 🔧 使用方法

### 1. 测试当前cluster_id是否有效
```bash
python 阵容.py test
```

### 2. 手动更新cluster_id
```bash
python 阵容.py update <新的cluster_id>
```

### 3. 正常运行爬虫
```bash
python 阵容.py
```

### 4. 查看帮助
```bash
python 阵容.py help
```

## 🔍 如何获取最新的cluster_id

1. **打开浏览器**访问：https://www.metatft.com/comps

2. **打开开发者工具**：按 `F12` 键

3. **切换到Network标签**：点击开发者工具中的"Network"标签

4. **刷新页面**：按 `F5` 或点击刷新按钮

5. **查找API请求**：在Network列表中搜索包含 `comp_details` 的请求

6. **提取cluster_id**：点击该请求，在URL中找到 `cluster_id=数字` 的部分

7. **更新代码**：
   - 方法1：修改 `阵容.py` 第40行的cluster_id值
   - 方法2：使用命令 `python 阵容.py update <新ID>`

## 📅 更新频率建议

- **正常情况**：每个游戏大版本更新时检查一次（约2-3个月）
- **API失败时**：立即检查并更新
- **定期维护**：建议每月测试一次有效性

## 🚨 故障排除

### 问题：API返回500错误
**原因**：cluster_id已过期
**解决**：按照上述步骤获取并更新最新的cluster_id

### 问题：无法找到comp_details请求
**原因**：页面可能使用了懒加载
**解决**：
1. 等待页面完全加载
2. 滚动页面到底部
3. 点击任意阵容查看详情
4. 再次查看Network请求

### 问题：测试显示cluster_id无效但实际可用
**原因**：网络问题或临时服务器问题
**解决**：
1. 等待几分钟后重试
2. 检查网络连接
3. 尝试运行完整爬虫验证

## 💡 优势

✅ **稳定可靠**：避免自动获取的不确定性
✅ **快速诊断**：一键测试cluster_id有效性  
✅ **简单更新**：命令行直接更新，无需修改代码
✅ **长期缓存**：有效的cluster_id缓存7天
✅ **清晰提示**：详细的获取步骤说明

## 📝 示例操作流程

```bash
# 1. 测试当前状态
python 阵容.py test

# 2. 如果无效，获取新ID（假设新ID是341）
python 阵容.py update 341

# 3. 运行爬虫
python 阵容.py
``` 