# -*- coding: utf-8 -*-
"""
辅助工具模块

本文件包含通用的、与具体业务无关的辅助函数。
主要负责图像处理、文本清洗等底层操作。
"""
import sys
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageGrab, ImageFilter
import imagehash
import re
import win32gui
import win32api

# --- 窗口与坐标转换工具 ---

_window_not_found_logged = False

def get_game_window_rect(use_client_area=True):
    """
    查找并返回云顶之弈游戏窗口的矩形信息 (x, y, width, height)。
    支持获取整个窗口或仅游戏画面区域。
    
    Args:
        use_client_area (bool): True=获取游戏画面区域（去除标题栏），False=获取整个窗口
    
    Returns:
        tuple: (x, y, width, height) 成功时返回游戏窗口的坐标和尺寸。
               失败时返回主显示器的全屏尺寸作为后备。
    """
    global _window_not_found_logged
    try:
        # 窗口标题，根据测试这个是游戏进程的主窗口
        window_title = "League of Legends (TM) Client"
        hwnd = win32gui.FindWindow(None, window_title)
        
        if hwnd:
            _window_not_found_logged = False # 找到窗口后重置标志
            if use_client_area:
                # 获取游戏画面区域（客户端区域）
                try:
                    # 获取客户端区域大小
                    client_rect = win32gui.GetClientRect(hwnd)
                    client_width = client_rect[2] - client_rect[0]
                    client_height = client_rect[3] - client_rect[1]
                    
                    # 获取客户端区域在屏幕上的位置
                    client_left, client_top = win32gui.ClientToScreen(hwnd, (0, 0))
                    
                    # 检查异常坐标
                    if client_left < -10000 or client_top < -10000 or client_width < 100 or client_height < 100:
                        raise ValueError("客户端区域异常")
                    
                    return (client_left, client_top, client_width, client_height)
                    
                except Exception as e:
                    print(f"获取客户端区域失败，降级到整个窗口: {e}")
                    # 降级到整个窗口
                    use_client_area = False
            
            if not use_client_area:
                # 获取整个窗口区域
                left, top, right, bottom = win32gui.GetWindowRect(hwnd)
                
                # 排除最小化或异常情况
                if left >= right or top >= bottom:
                    raise ValueError("窗口尺寸异常，可能已最小化")

                width = right - left
                height = bottom - top
                
                return (left, top, width, height)
        else:
            # 如果没找到游戏窗口，则使用整个主屏幕作为后备
            if not _window_not_found_logged:
                print("未找到游戏窗口，将使用主屏幕作为截图区域。")
                _window_not_found_logged = True
            screen_width = win32api.GetSystemMetrics(0)
            screen_height = win32api.GetSystemMetrics(1)
            return (0, 0, screen_width, screen_height)
            
    except Exception as e:
        if not _window_not_found_logged:
            print(f"获取游戏窗口时出错: {e}。将使用主屏幕作为后备。")
            _window_not_found_logged = True
        # 发生任何异常，都回退到使用整个主屏幕
        screen_width = win32api.GetSystemMetrics(0)
        screen_height = win32api.GetSystemMetrics(1)
        return (0, 0, screen_width, screen_height)

def convert_relative_to_absolute(relative_coords, game_rect):
    """
    [最终优化 - 虚拟16:9边框方案] 将config.py中定义的比例坐标转换为绝对屏幕坐标。
    采用"虚拟16:9边框"算法：统一以游戏窗口高度为基准，水平居中构造16:9虚拟区域，
    然后在虚拟区域内应用相对坐标，完美适配任意分辨率比例。

    Args:
        relative_coords (tuple): (x1_ratio, y1_ratio, x2_ratio, y2_ratio)，
                                 这是在config.py中定义的基于16:9的比例坐标。
        game_rect (tuple): (game_x, game_y, game_width, game_height)，
                           由 get_game_window_rect() 返回的游戏窗口矩形。

    Returns:
        tuple: (abs_x1, abs_y1, abs_x2, abs_y2)，可直接用于截图的绝对屏幕坐标点。
    """
    # 从游戏窗口矩形中解包数据
    game_x, game_y, game_width, game_height = game_rect
    
    # 第一步：创建虚拟16:9区域（核心算法）
    # 计算中心点
    center_x = game_x + game_width / 2
    center_y = game_y + game_height / 2
    
    # 统一以游戏窗口高度为基准构造16:9虚拟框
    virtual_height = game_height  # 虚拟框高度 = 游戏窗口高度
    virtual_width = virtual_height * (16 / 9)  # 虚拟框宽度 = 高度 * (16/9)
    
    # 虚拟框在游戏窗口中水平居中，垂直对齐
    virtual_x = center_x - virtual_width / 2
    virtual_y = game_y  # 顶部对齐，因为高度完全相同
    
    # 第二步：在虚拟16:9区域内应用相对坐标
    x1_ratio, y1_ratio, x2_ratio, y2_ratio = relative_coords
    
    abs_x1 = int(virtual_x + virtual_width * x1_ratio)
    abs_y1 = int(virtual_y + virtual_height * y1_ratio)
    abs_x2 = int(virtual_x + virtual_width * x2_ratio)
    abs_y2 = int(virtual_y + virtual_height * y2_ratio)
    
    return (abs_x1, abs_y1, abs_x2, abs_y2)

# --- 图像处理工具函数 ---

def calculate_image_hash(img):
    """计算图像的感知哈希值。"""
    if not img:
        return None
    return imagehash.phash(img)

def hash_distance(hash1, hash2):
    """计算两个哈希值之间的汉明距离"""
    if hash1 is None or hash2 is None:
        return 100  # 返回一个较大的值表示差异很大
    return hash1 - hash2  # imagehash库原生支持差异计算

def remove_vertical_lines(image):
    """移除图像中的竖线"""
    # 转换为numpy数组
    img_array = np.array(image)
    
    # 转换为灰度图
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array
        
    # 二值化
    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    
    # 定义竖线检测的核
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 20))
    
    # 检测竖线
    vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
    
    # 移除竖线
    result = cv2.add(binary, vertical_lines)
    
    return Image.fromarray(result)

# --- 图像预处理函数 ---

def preprocess_image(img, strategy='default'):
    """
    对图像进行预处理以提高OCR识别率，支持多种策略。
    该函数基于旧版代码中被验证有效的参数进行重构。

    Args:
        img (Image): PIL图像对象。
        strategy (str): 使用的预处理策略。

    Returns:
        Image: 处理后的PIL图像对象。
    """
    # 定义不同策略的参数集
    # 参数: (resize_factor, contrast_factor, sharpness_factor, threshold)
    strategies = {
        'default': (1.3, 2.5, 1.3, 195),
        'alt1':    (1.5, 2.8, 1.5, 190), # 源于旧代码的 "增大参数" 方案
        'alt2':    (1.3, 2.2, 1.1, 200), # 源于旧代码的 "减小参数" 方案
        'contrast':(1.4, 3.0, 1.0, 185)  # 一个新的高对比度方案
    }
    
    params = strategies.get(strategy, strategies['default'])
    resize_factor, contrast_factor, sharpness_factor, threshold = params

    try:
        # 统一处理流程
        resized_img = img.resize(
            (int(img.width * resize_factor), int(img.height * resize_factor)), 
            Image.Resampling.LANCZOS
        )
        gray_img = resized_img.convert('L')
        contrasted_img = ImageEnhance.Contrast(gray_img).enhance(contrast_factor)
        sharpened_img = ImageEnhance.Sharpness(contrasted_img).enhance(sharpness_factor)
        binary_img = sharpened_img.point(lambda x: 0 if x < threshold else 255)
        return binary_img
    except Exception as e:
        print(f"图像预处理错误 (策略: {strategy}): {e}")
        return img.convert('L') # 出错时返回灰度图作为保底

def equip_preprocess_image(image):
    """装备图像预处理"""
    # 1. 转换为灰度图
    img_gray = image.convert('L')
    
    # 2. 增强对比度
    img_contrast = ImageEnhance.Contrast(img_gray).enhance(1.0)
    
    # 3. 移除竖线
    img_no_lines = remove_vertical_lines(img_contrast)
    
    # 4. 二值化
    img_binary = img_no_lines.point(lambda x: 0 if x < 200 else 300)
    
    # 5.增加图片高度1.15倍
    img_binary = img_binary.resize((img_binary.width, int(img_binary.height * 1.1)), Image.Resampling.LANCZOS)
    
    return img_binary

def preprocess_image_alt1(image):
    """替代预处理方案1：稍微增加各个维度的参数"""
    try:
        return ImageEnhance.Sharpness(
            ImageEnhance.Contrast(
                image.resize(
                    (int(image.width * 1.5), int(image.height * 1.5)), 
                    Image.LANCZOS
                ).convert('L')
            ).enhance(2.8)
        ).enhance(1.5).point(lambda x: 0 if x < 190 else 255)
    except Exception as e:
        if hasattr(sys, '_called_from_test'):
            raise e
        print(f"预处理方案1出错: {e}")
        # 出错时返回原始图像灰度版本
        return image.convert('L')

def preprocess_image_alt2(image):
    """替代预处理方案2：稍微减少各个维度的参数"""
    try:
        return ImageEnhance.Sharpness(
            ImageEnhance.Contrast(
                image.resize(
                    (int(image.width * 1.3), int(image.height * 1.3)), 
                    Image.LANCZOS
                ).convert('L')
            ).enhance(2.2)
        ).enhance(1.1).point(lambda x: 0 if x < 200 else 255)
    except Exception as e:
        if hasattr(sys, '_called_from_test'):
            raise e
        print(f"预处理方案2出错: {e}")
        # 出错时返回原始图像灰度版本
        return image.convert('L')

# --- 文本处理工具函数 ---

def split_text_by_spaces(text):
    """根据连续空格分割文本"""
    # 将连续的空格替换为单个特殊标记
    text = re.sub(r'\s+', '|', text)
    # 分割文本
    parts = [part.strip() for part in text.split('|') if part.strip()]
    return parts

def clean_text(text):
    """清理文本，只保留中文字符"""
    return re.sub(r'[^\u4e00-\u9fa5]', '', text)

def clean_ocr_text(text):
    """清理OCR识别出的原始文本。"""
    # 移除所有非中文字符、数字和字母（保留I V X等罗马数字）
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    
    # 将常见的识别错误替换为正确字符
    replacements = {
        '丨': 'I', '丨I': 'II', 'IlI': 'III', '丨II': 'III', 'l': 'I', '0': 'o',
    }
    for old, new in replacements.items():
        text = text.replace(old, new)
        
    return text.strip()

# --- 颜色和像素处理工具 ---

def is_color_similar(color1, color2, threshold):
    """检查两个RGB颜色是否相似"""
    r1, g1, b1 = color1
    r2, g2, b2 = color2
    return (abs(r1 - r2) <= threshold and 
            abs(g1 - g2) <= threshold and 
            abs(b1 - b2) <= threshold)

def count_matching_colors(img, target_colors, threshold):
    """统计图像中与目标颜色匹配的像素数量"""
    img_array = np.array(img)
    height, width, _ = img_array.shape
    
    matching_count = 0
    total_pixels = width * height
    
    # 获取图片中所有像素颜色及其计数
    colors = {}
    for y in range(height):
        for x in range(width):
            pixel = tuple(img_array[y, x])
            if pixel in colors:
                colors[pixel] += 1
            else:
                colors[pixel] = 1
    
    # 计算与目标颜色匹配的像素数量
    for color, count in colors.items():
        for target in target_colors:
            if is_color_similar(color, target, threshold):
                matching_count += count
                break
    
    return matching_count, total_pixels 