<template>
  <!-- 英雄页面内容 -->
  <div class="hero-page">
    
    <!-- 搜索区域 -->
    <div class="search-section">
      <input 
        v-model="searchQuery"
        type="text" 
        placeholder="搜索英雄名称..."
        class="search-input"
      />
    </div>

    <!-- 费用筛选区域 -->
    <div class="filter-section">
      <button 
        v-for="cost in costFilters" 
        :key="cost.value"
        class="cost-filter-button"
        :class="[
          { 'active': selectedCosts.includes(cost.value) },
          getCostFilterClass(cost.value)
        ]"
        @click="toggleCostFilter(cost.value)"
      >
        {{ cost.label }}
      </button>
    </div>

    <!-- 英雄列表区域 -->
    <div class="hero-list-area">
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>正在加载英雄数据...</p>
      </div>

      <!-- 暂无数据提示 -->
      <div v-else-if="allHeroes.length === 0" class="no-data-state">
        <div class="no-data-icon">📋</div>
        <h3>暂无英雄数据</h3>
        <p>数据库连接正常，但暂未加载到英雄数据</p>
        <button @click="loadHeroes" class="retry-button">重新加载</button>
      </div>

      <!-- 英雄分组显示 -->
      <div v-else class="hero-groups">
        <!-- 颜色图例 -->
        <ColorLegend />
        
        <!-- 英雄分组内容 -->
        <div 
          v-for="cost in visibleCosts" 
          :key="cost"
          class="cost-group"
        >
          
          <!-- 费用标签 -->
          <div 
            class="cost-label"
            :class="getCostLabelClass(cost)"
          >
            {{ cost }}
          </div>
          
          <!-- 英雄网格 -->
          <div class="hero-grid">
            <div 
              v-for="(hero, heroIndex) in getHeroesByCost(cost)" 
              :key="hero.cn_name"
              class="hero-card"
              @click="handleHeroClick(hero)"
            >
              
              <!-- 头像区域 -->
              <div class="hero-avatar-container">
                <HeroIcon
                  :hero-name="hero.cn_name || '未知英雄'"
                  :icon-path="hero.icon_path"
                  :cost="hero.cost"
                  :size="48"
                  :show-cost-badge="false"
                  @click="handleHeroClick"
                />
              </div>
              
              <!-- 英雄信息 -->
              <div class="hero-info">
                <p class="hero-name">{{ hero.cn_name || '未知英雄' }}</p>
                <HeroStats
                  :play-rate-data="getHeroPercentileInfo(hero).playRate"
                  :avg-place-data="getHeroPercentileInfo(hero).avgPlace"
                  :top4-rate-data="getHeroPercentileInfo(hero).top4Rate"
                  :top1-rate-data="getHeroPercentileInfo(hero).top1Rate"
                  :compact="true"
                  :hero-index="heroIndex"
                  :total-heroes="getHeroesByCost(cost).length"
                />
              </div>
            </div>
          </div>
        </div>
      </div> <!-- 英雄分组内容结束 -->
    </div> <!-- hero-groups结束 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import HeroIcon from '@/components/common/HeroIcon.vue'
import HeroStats from '@/components/common/HeroStats.vue'
import ColorLegend from '@/components/common/ColorLegend.vue'
import { calculateCostGroupStats, getHeroPercentileData, type HeroData, type CostGroupStats } from '@/utils/dataAnalysis'
import { useData } from '@/composables/useData'
import { useDataStore } from '@/stores/data'

// Emits
const emit = defineEmits<{
  heroSelected: [heroName: string]
}>()

// === 数据组合式函数 ===
const { getHeroList } = useData()

// === 英雄数据状态 ===
const allHeroes = ref<HeroData[]>([])
const isLoading = ref(true)
const searchQuery = ref('')
const selectedCosts = ref<(string | number)[]>(['all'])
const costGroupStats = ref<Map<number, CostGroupStats>>(new Map())

// === 费用筛选配置 ===
const costFilters = [
  { label: '全部', value: 'all' },
  { label: '1费', value: 1 },
  { label: '2费', value: 2 },
  { label: '3费', value: 3 },
  { label: '4费', value: 4 },
  { label: '5费', value: 5 }
]

// === 计算属性 ===
const filteredHeroes = computed(() => {
  let heroes = allHeroes.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    heroes = heroes.filter(hero => hero.cn_name?.toLowerCase().includes(query))
  }

  if (!selectedCosts.value.includes('all') && selectedCosts.value.length > 0) {
    heroes = heroes.filter(hero => selectedCosts.value.includes(hero.cost))
  }

  return heroes
})

const getHeroesByCost = (cost: number) => {
  return filteredHeroes.value
    .filter(hero => hero.cost === cost)
    .sort((a, b) => (b.play_rate || 0) - (a.play_rate || 0)) // 按出场率降序排列
}

// 计算应该显示的费用组
const visibleCosts = computed(() => {
  const allCosts = [1, 2, 3, 4, 5]
  
  return allCosts.filter(cost => {
    const heroesInCost = getHeroesByCost(cost)
    
    // 如果该费用组没有英雄，不显示
    if (heroesInCost.length === 0) {
      return false
    }
    
    // 如果选择了"全部"，显示所有有英雄的费用组
    if (selectedCosts.value.includes('all')) {
      return true
    }
    
    // 如果没有选择任何费用，显示所有有英雄的费用组
    if (selectedCosts.value.length === 0) {
      return true
    }
    
    // 如果选择了特定费用，只显示选中的费用组
    return selectedCosts.value.includes(cost)
  })
})



// 获取英雄的百分位数据
const getHeroPercentileInfo = (hero: HeroData) => {
  const costStats = costGroupStats.value.get(hero.cost)
  if (!costStats) {
    // 如果没有统计数据，返回默认值
    return {
      playRate: {
        value: hero.play_rate || 0,
        percentile: 50,
        color: '#6B7280',
        level: 'average' as const
      },
      avgPlace: {
        value: hero.avg_place || 0,
        percentile: 50,
        color: '#6B7280',
        level: 'average' as const
      },
      top4Rate: {
        value: hero.top4_rate || 0,
        percentile: 50,
        color: '#6B7280',
        level: 'average' as const
      },
      top1Rate: {
        value: hero.top1_rate || 0,
        percentile: 50,
        color: '#6B7280',
        level: 'average' as const
      }
    }
  }
  return getHeroPercentileData(hero, costStats)
}

// === 费用筛选功能 ===
const toggleCostFilter = (cost: string | number) => {
  if (cost === 'all') {
    selectedCosts.value = ['all']
  } else {
    const index = selectedCosts.value.indexOf(cost)
    if (index > -1) {
      selectedCosts.value.splice(index, 1)
      // 如果没有选择任何费用，回到"全部"
      if (selectedCosts.value.length === 0) {
        selectedCosts.value = ['all']
      }
    } else {
      // 移除"全部"选项
      const allIndex = selectedCosts.value.indexOf('all')
      if (allIndex > -1) {
        selectedCosts.value.splice(allIndex, 1)
      }
      selectedCosts.value.push(cost)
    }
  }
  
  console.log('当前选择的费用:', selectedCosts.value)
}

// === 样式辅助函数 ===
const getCostLabelClass = (cost: number) => {
  const costClasses = {
    1: 'cost-1-label',
    2: 'cost-2-label', 
    3: 'cost-3-label',
    4: 'cost-4-label',
    5: 'cost-5-label'
  }
  return costClasses[cost as keyof typeof costClasses]
}

const getCostBadgeClass = (cost: number) => {
  const costClasses = {
    1: 'cost-1-badge',
    2: 'cost-2-badge',
    3: 'cost-3-badge', 
    4: 'cost-4-badge',
    5: 'cost-5-badge'
  }
  return costClasses[cost as keyof typeof costClasses]
}

const getCostFilterClass = (cost: string | number) => {
  if (cost === 'all') return 'filter-all'
  const costClasses = {
    1: 'filter-cost-1',
    2: 'filter-cost-2',
    3: 'filter-cost-3',
    4: 'filter-cost-4',
    5: 'filter-cost-5'
  }
  return costClasses[cost as keyof typeof costClasses] || ''
}

// === 数据格式化函数已移至dataAnalysis工具 ===

// === 英雄点击处理 ===
const handleHeroClick = (hero: any) => {
  console.log('点击英雄:', hero.cn_name)
  if (hero.cn_name) {
    emit('heroSelected', hero.cn_name)
  }
}

// === 数据加载 ===
const loadHeroes = async () => {
  console.log('🔍 开始加载英雄数据...')

  try {
    // 先尝试同步获取缓存数据，避免设置loading状态
    const dataStore = useDataStore()
    const cachedData = dataStore.getCachedQuery('hero_list')

    if (cachedData && cachedData.length > 0) {
      console.log('✅ 从缓存即时加载英雄数据，零延迟')
      allHeroes.value = cachedData
      costGroupStats.value = calculateCostGroupStats(cachedData)
      return
    }

    // 缓存未命中时才显示loading
    console.log('缓存未命中，显示loading并从数据库加载...')
    isLoading.value = true

    const results = await invoke('get_hero_list')
    console.log('📋 英雄数据加载结果:', results)
    console.log('📋 结果类型:', typeof results)
    console.log('📋 是否为数组:', Array.isArray(results))
    
    // 检查是否是QueryResult格式
    if (results && typeof results === 'object' && 'data' in results) {
      console.log('📋 检测到QueryResult格式')
      console.log('📋 QueryResult.data:', results.data)
      console.log('📋 QueryResult.error:', 'error' in results ? results.error : 'none')
      
      if ('error' in results && results.error) {
        console.error('❌ 数据库查询错误:', results.error)
        allHeroes.value = []
        return
      }
      
      if (Array.isArray(results.data)) {
        allHeroes.value = results.data
        console.log(`✅ 成功加载 ${allHeroes.value.length} 个英雄`)
        if (results.data.length > 0) {
          console.log('👤 第一个英雄示例:', results.data[0])
        }
        
        // 计算费用组统计数据
        costGroupStats.value = calculateCostGroupStats(allHeroes.value)
        console.log('📊 费用组统计数据已计算:', costGroupStats.value)
      } else {
        console.warn('⚠️ QueryResult.data 不是数组格式:', results.data)
        allHeroes.value = []
      }
    } else if (Array.isArray(results)) {
      allHeroes.value = results
      console.log(`✅ 成功加载 ${allHeroes.value.length} 个英雄`)
      if (results.length > 0) {
        console.log('👤 第一个英雄示例:', results[0])
      }
      
      // 计算费用组统计数据
      costGroupStats.value = calculateCostGroupStats(allHeroes.value)
      console.log('📊 费用组统计数据已计算:', costGroupStats.value)
    } else {
      console.warn('⚠️ 返回的数据格式不正确:', results)
      allHeroes.value = []
    }
  } catch (error) {
    console.error('❌ 加载英雄数据失败:', error)
    console.error('❌ 错误详情:', {
      name: (error as any)?.name,
      message: (error as any)?.message,
      stack: (error as any)?.stack
    })
    allHeroes.value = []
  } finally {
    isLoading.value = false
  }
}

// === 组件挂载时加载数据 ===
onMounted(() => {
  loadHeroes()
})
</script>

<style scoped>
/* === 英雄页面 === */
.hero-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
}

/* === 搜索区域 === */
.search-section {
  flex-shrink: 0;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* === 筛选区域 === */
.filter-section {
  flex-shrink: 0;
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.cost-filter-button {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cost-filter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.cost-filter-button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cost-filter-button:hover::before {
  left: 100%;
}

.cost-filter-button.active {
  color: white;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

/* 全部按钮激活状态 */
.filter-all.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.3));
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* 1费按钮激活状态 */
.filter-cost-1.active {
  background: linear-gradient(135deg, rgba(211, 211, 211, 0.8), rgba(168, 168, 168, 0.8));
  border-color: #d3d3d3;
  box-shadow: 0 0 20px rgba(211, 211, 211, 0.4);
}

/* 2费按钮激活状态 */
.filter-cost-2.active {
  background: linear-gradient(135deg, rgba(50, 205, 50, 0.8), rgba(34, 139, 34, 0.8));
  border-color: #32cd32;
  box-shadow: 0 0 20px rgba(50, 205, 50, 0.4);
}

/* 3费按钮激活状态 */
.filter-cost-3.active {
  background: linear-gradient(135deg, rgba(0, 191, 255, 0.8), rgba(0, 128, 255, 0.8));
  border-color: #00bfff;
  box-shadow: 0 0 20px rgba(0, 191, 255, 0.4);
}

/* 4费按钮激活状态 */
.filter-cost-4.active {
  background: linear-gradient(135deg, rgba(191, 0, 255, 0.8), rgba(139, 0, 255, 0.8));
  border-color: #bf00ff;
  box-shadow: 0 0 20px rgba(191, 0, 255, 0.4);
}

/* 5费按钮激活状态 */
.filter-cost-5.active {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.8), rgba(255, 179, 71, 0.8));
  border-color: #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);
}

/* === 英雄列表区域 === */
.hero-list-area {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.hero-list-area::-webkit-scrollbar {
  width: 6px;
}

.hero-list-area::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.hero-list-area::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.hero-list-area::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 暂无数据状态 === */
.no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-data-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.no-data-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 英雄分组 === */
.hero-groups {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.cost-group {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  min-height: 120px;
}

/* === 费用标签 === */
.cost-label {
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cost-1-label { background: linear-gradient(135deg, #d3d3d3, #a8a8a8); }
.cost-2-label { background: linear-gradient(135deg, #32cd32, #228b22); }
.cost-3-label { background: linear-gradient(135deg, #00bfff, #0080ff); }
.cost-4-label { background: linear-gradient(135deg, #bf00ff, #8b00ff); }
.cost-5-label { background: linear-gradient(135deg, #ffd700, #ffb347); }

/* === 英雄网格 === */
.hero-grid {
  flex: 1;
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.75rem;
  align-content: start;
}

/* === 英雄卡片 === */
.hero-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 10px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  position: relative;
  z-index: 1;
}

.hero-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

/* === 头像区域 === */
.hero-avatar-container {
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: center;
}

/* === 英雄信息 === */
.hero-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.hero-name {
  color: white;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 0.25rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  text-align: center;
}
</style>