# 视图模块/阵容详情视图.py

import json
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea, QFrame,
                               QPushButton, QTabWidget, QSizePolicy, QSpacerItem, QGridLayout,
                               QTabBar, QProgressBar) # 添加 QProgressBar
from PySide6.QtCore import Qt, Signal, Slot, QSize, QTimer
from PySide6.QtGui import QFont, QMovie

from 常量与配置 import (
    WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_HIGHLIGHT,
    TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR, ITEM_ROW_BG_HOVER,
    ICON_SIZE_XLARGE, ICON_SIZE_LARGE, ICON_SIZE_MEDIUM, ICON_SIZE_SMALL,
    SECTION_BG_COLOR, CONTROL_BAR_BG, TAB_STYLE, BUTTON_STYLE_BASE, BUTTON_BG,
    FONT_SIZE_MEDIUM, FONT_SIZE_LARGE, FONT_WEIGHT_BOLD, FONT_WEIGHT_NORMAL,
    GLOBAL_HERO_INFO_MAP, GLOBAL_TRAIT_ICON_MAP, GLOBAL_HERO_BASE_STATS # <-- 导入全局字典
)
from 自定义组件 import IconLabel, ClickableLabel, WheelHorizontalScrollArea # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

# --- 内部辅助函数 ---
def format_placement(value):
    return f"{value:.2f}" if value is not None else "--"

def format_rate(value):
    if value is None:
        return "--"
    # 如果值大于1，假设是正常百分比值(0-100)，直接显示
    if value > 1:
        return f"{value:.1f}%"
    # 如果值是小数(0-1)，则按百分比格式化（乘以100）
    return f"{value:.1%}"

def format_count(value):
     return f"{value:,}" if value is not None else "--" # 带千位分隔符

# --- "Options & Quick Start" Tab 内容 Widget ---
class OptionsTabContent(QWidget):
    """显示"等级阵容变体"内容的 Widget (使用 QTabWidget 分级)"""
    hero_selected = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        # 主布局现在包含一个 QTabWidget
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0) # Tab widget 充满
        self.main_layout.setSpacing(0)

        # 创建 TabWidget
        self.level_tabs = QTabWidget()
        # 应用稍微调整过的 Tab 样式，确保与整体风格一致
        tab_style_adjusted = TAB_STYLE.replace("padding: 6px 15px;", "padding: 5px 10px;") # 减小 padding
        tab_style_adjusted = tab_style_adjusted.replace("min-width: 70px;", "min-width: 60px;") # 减小 min-width
        self.level_tabs.setStyleSheet(tab_style_adjusted)
        self.level_tabs.setDocumentMode(True) # 可选，让 Tab 更紧凑
        self.level_tabs.tabBar().setExpanding(False) # 防止 Tab 拉伸填充

        self.main_layout.addWidget(self.level_tabs)
        
        # 初始化羁绊映射字典
        self.trait_id_to_name_map = {}  # 存储 trait_id 到中文名的映射
        self.trait_level_number_map = {}  # 存储 trait_name+level 到实际数字的映射
        
        # 加载羁绊映射数据
        self.load_trait_mappings()

    def load_trait_mappings(self):
        """从数据库加载羁绊ID到中文名和羁绊等级到实际数字的映射"""
        # 查询羁绊ID和名称的映射
        sql_traits = "SELECT name, en_name FROM traits"
        execute_query_async(sql_traits, on_success=self.on_trait_names_loaded, query_key="trait_id_to_name")
        
        # 查询羁绊等级和实际所需数字的映射
        sql_levels = "SELECT trait_name, level, level_number FROM trait_levels"
        execute_query_async(sql_levels, on_success=self.on_trait_levels_loaded, query_key="trait_level_numbers")

    def on_trait_names_loaded(self, results):
        """处理羁绊名称查询结果"""
        if results:
            # 创建双向映射（ID到名称和名称到ID）
            for row in results:
                trait_name = row['name']
                trait_id = row['en_name']
                if trait_id:
                    # 标准化ID（小写）以确保匹配
                    standardized_id = trait_id.lower()
                    self.trait_id_to_name_map[standardized_id] = trait_name
                    
            print(f"已加载 {len(self.trait_id_to_name_map)} 个羁绊ID到名称的映射")

    def on_trait_levels_loaded(self, results):
        """处理羁绊等级查询结果"""
        if results:
            for row in results:
                trait_name = row['trait_name']
                level = row['level']
                level_number = row['level_number']
                # 使用组合键存储
                key = f"{trait_name}:{level}"
                self.trait_level_number_map[key] = level_number
                
            print(f"已加载 {len(self.trait_level_number_map)} 个羁绊等级到实际数字的映射")

    def update_content(self, level_recs_data, trait_icon_map):
        """使用加载的数据更新内容 (填充 TabWidget, 筛选等级 <= 10)"""
        self.level_tabs.clear() # 清空旧的 Tabs
        if not level_recs_data:
            # 如果没有数据，可以在 TabWidget 中间显示提示
            no_data_label = QLabel("暂无等级阵容推荐数据", alignment=Qt.AlignmentFlag.AlignCenter)
            no_data_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; padding: 40px;")
            container = QWidget() # 需要一个容器来添加 Label
            layout = QVBoxLayout(container)
            layout.addWidget(no_data_label)
            self.level_tabs.addTab(container, "无数据") # 添加一个提示 Tab
            return

        # 按等级排序并筛选 <= 10 的等级
        sorted_levels = sorted([level for level in level_recs_data.keys() if level <= 10]) # <--- 筛选等级

        if not sorted_levels: # 如果筛选后没有有效等级
            no_data_label = QLabel("暂无 10 级及以下阵容推荐数据", alignment=Qt.AlignmentFlag.AlignCenter)
            no_data_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; padding: 40px;")
            container = QWidget()
            layout = QVBoxLayout(container)
            layout.addWidget(no_data_label)
            self.level_tabs.addTab(container, "无数据")
            return

        for level in sorted_levels:
            recs_list = level_recs_data.get(level, []) # 安全获取
            if not recs_list: continue # 如果某等级无数据则跳过

            sorted_recs = sorted(recs_list, key=lambda r: r.get('count') if r.get('count') is not None else 0, reverse=True)
            top_recs = sorted_recs[:3] # 只取前三个

            # --- 创建每个 Tab 的内容 ---
            # 滚动区域，以便内容过多时可以滚动
            content_scroll = QScrollArea()
            content_scroll.setWidgetResizable(True)
            content_scroll.setStyleSheet("QScrollArea{border:none; background:transparent;} QWidget{background:transparent;}") # 简洁样式
            content_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

            # 滚动区域的内容 Widget 和布局
            tab_content_widget = QWidget()
            tab_layout = QVBoxLayout(tab_content_widget)
            tab_layout.setContentsMargins(8, 8, 8, 8) # Tab 内部边距
            tab_layout.setSpacing(10) # 推荐项之间的间距

            if not top_recs:
                 no_recs_label = QLabel(f"等级 {level} 暂无推荐", alignment=Qt.AlignmentFlag.AlignCenter)
                 no_recs_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; padding: 20px;")
                 tab_layout.addWidget(no_recs_label)
            else:
                for i, rec in enumerate(top_recs):
                    # 直接将 rec 传递过去，里面包含了 units_cn 和 units_path
                    rec_widget = self.create_recommendation_widget(rec)
                    tab_layout.addWidget(rec_widget)
                    if i < len(top_recs) - 1:
                        line = QFrame(); line.setFrameShape(QFrame.Shape.HLine); line.setStyleSheet(f"border-top: 1px solid {BORDER_COLOR}; margin: 5px 0;"); tab_layout.addWidget(line)

            tab_layout.addStretch(1) # 将内容推到顶部
            content_scroll.setWidget(tab_content_widget) # 将内容放入滚动区

            # 添加 Tab
            self.level_tabs.addTab(content_scroll, f"Lvl {level}")

    def create_recommendation_widget(self, rec_data):
        """创建单个等级推荐的显示 Widget (调整排名位置)"""
        widget = QWidget()
        main_layout = QHBoxLayout(widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15) # 左右面板间距

        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(8) # 羁绊行和英雄行的间距

        # --- 羁绊行 ---
        traits_layout = QHBoxLayout()
        traits_layout.setSpacing(5)
        traits_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        traits = rec_data.get('traits', [])
        if traits:
            for trait_info in traits:
                # 获取羁绊ID并尝试转换为中文名
                trait_id = trait_info.get('trait_id', '').lower() if isinstance(trait_info.get('trait_id'), str) else ''
                trait_level = trait_info.get('level', '')
                
                # 尝试从映射中获取中文名，如果没有则使用ID
                trait_name = self.trait_id_to_name_map.get(trait_id, trait_id)
                
                # 尝试获取实际所需羁绊数字
                actual_number = None
                if trait_name in self.trait_level_number_map:
                    key = f"{trait_name}:{trait_level}"
                    actual_number = self.trait_level_number_map.get(key)
                
                # 获取羁绊图标路径
                icon_path = GLOBAL_TRAIT_ICON_MAP.get(trait_name)

                trait_widget = QWidget()
                trait_item_layout = QHBoxLayout(trait_widget)
                trait_item_layout.setContentsMargins(3, 2, 3, 2); trait_item_layout.setSpacing(3)

                trait_icon_label = IconLabel(icon_size=ICON_SIZE_SMALL, icon_type='trait', placeholder_text=trait_name[:1])
                trait_icon_label.set_icon(icon_path, trait_name)
                
                # 创建显示名称+数字的标签
                trait_name_label = QLabel(trait_name)
                trait_name_label.setStyleSheet(f"font-size: 9px; color: {TEXT_COLOR_LIGHT};")
                
                # 显示实际数字（如果有）或等级
                trait_level_text = str(actual_number) if actual_number is not None else str(trait_level)
                trait_level_label = QLabel(trait_level_text)
                trait_level_label.setStyleSheet(f"font-size: 9px; font-weight: bold; color: {TEXT_COLOR_LIGHT};")
                
                # 设置工具提示显示完整信息
                tooltip_text = f"{trait_name} {trait_level}级"
                if actual_number is not None:
                    tooltip_text += f" ({actual_number}个)"
                trait_widget.setToolTip(tooltip_text)

                trait_item_layout.addWidget(trait_icon_label)
                trait_item_layout.addWidget(trait_name_label)
                trait_item_layout.addWidget(trait_level_label)
                trait_widget.setStyleSheet(f"background-color:{BUTTON_BG}; border-radius:3px;")
                traits_layout.addWidget(trait_widget)
            traits_layout.addStretch(1)
        left_layout.addLayout(traits_layout)

        # --- 英雄行 (图标 + 名称) ---
        heroes_widget = QWidget()
        heroes_layout = QHBoxLayout(heroes_widget)
        heroes_layout.setContentsMargins(0, 0, 0, 0)
        heroes_layout.setSpacing(5)
        heroes_layout.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        # 获取转换后的中文名和图标路径列表
        units_cn_list = rec_data.get('units_cn', [])
        units_path_list = rec_data.get('units_path', [])

        # 确保列表长度一致 (防御性编程)
        num_units = min(len(units_cn_list), len(units_path_list))

        if num_units > 0:
            for i in range(num_units):
                unit_cn = units_cn_list[i]
                icon_path = units_path_list[i]
                if not unit_cn: continue

                hero_container = QWidget()
                hero_v_layout = QVBoxLayout(hero_container)
                hero_v_layout.setContentsMargins(0, 0, 0, 0)
                hero_v_layout.setSpacing(2)
                hero_v_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                hero_icon = IconLabel(data=unit_cn, icon_size=ICON_SIZE_LARGE, icon_type='hero')
                hero_icon.set_icon(icon_path, unit_cn)
                hero_icon.setToolTip(unit_cn)
                hero_icon.clicked.connect(lambda name=unit_cn: self.hero_selected.emit(name))

                name_label = QLabel(unit_cn)
                name_label.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size:8px;")
                name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                hero_v_layout.addWidget(hero_icon)
                hero_v_layout.addWidget(name_label)
                heroes_layout.addWidget(hero_container)

            heroes_layout.addStretch(1)

        left_layout.addWidget(heroes_widget)

        # --- 右侧统计 (调整 Stretch 比例以降低位置) ---
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(2)
        right_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        avg_place = rec_data.get('avg_placement')

        avg_label = QLabel(format_placement(avg_place))
        avg_label.setStyleSheet(f"color:{TEXT_COLOR_LIGHT}; font-size:16px; font-weight:bold;")
        avg_label_sub = QLabel("平均排名")
        avg_label_sub.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size:9px;")

        right_layout.addStretch(3) # <--- 增加顶部 Stretch 的比例 (例如 3)
        right_layout.addWidget(avg_label, 0, Qt.AlignmentFlag.AlignRight)
        right_layout.addWidget(avg_label_sub, 0, Qt.AlignmentFlag.AlignRight)
        right_layout.addStretch(1) # <--- 底部 Stretch 的比例相对较小 (例如 1)

        main_layout.addWidget(left_panel, 1)
        main_layout.addWidget(right_panel)
        return widget


# --- "Units & Items" Tab 内容 Widget ---
class UnitsItemsTabContent(QWidget):
    """显示"单位和装备统计"内容的 Widget"""
    hero_selected = Signal(str)
    item_selected = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(5, 8, 5, 8)
        self.layout.setSpacing(10)

        # --- 新增：排序状态 --- 
        self.unit_sort_column = 'appearance_rate'
        self.unit_sort_order = Qt.SortOrder.DescendingOrder
        self.item_sort_column = 'overall_appearance_rate'
        self.item_sort_order = Qt.SortOrder.DescendingOrder

        # --- 新增/移动：存储表头控件 (移到调用 _create_list_widget 之前) --- 
        self.unit_header_widgets = {} # {col_key: ClickableLabel}
        self.item_header_widgets = {} # {col_key: ClickableLabel}

        # --- 创建列表区域 --- 
        self.unit_list_widget = self._create_list_widget("英雄出场率排名", 'unit')
        self.item_list_widget = self._create_list_widget("装备出场率排名", 'item')

        # --- 修改：调整左右列表宽度比例为 4:5 --- 
        self.layout.addWidget(self.unit_list_widget, 6) # 英雄列表占 4 份
        self.layout.addWidget(self.item_list_widget, 7) # 装备列表占 5 份

        # 存储数据
        self.unit_stats = []
        self.item_stats = []
        self.item_unit_stats = {}

    def _create_list_widget(self, title, list_type):
        """创建一个带标题、可点击表头和滚动列表的通用框架 (增大表头列宽)"""
        frame = QFrame()
        frame.setStyleSheet(f"background-color: {SECTION_BG_COLOR}; border-radius: 4px; border: 1px solid {BORDER_COLOR};")
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(8, 5, 8, 8)
        layout.setSpacing(5)

        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {TEXT_COLOR_HIGHLIGHT}; font-size: 11px; font-weight: bold; border: none; background: transparent; margin-bottom: 3px;")
        layout.addWidget(title_label)

        # --- 添加表头 --- 
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(4, 2, 4, 2)
        header_layout.setSpacing(3) # 保持较大的间距

        first_col_name = "英雄" if list_type == 'unit' else "装备"
        header_name = QLabel(first_col_name)
        header_style = f"color: {TEXT_COLOR_MEDIUM}; font-size: 10px; font-weight: bold; background: transparent; border: none;"
        header_name.setStyleSheet(header_style)
        header_name.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)

        rate_key = 'appearance_rate' if list_type == 'unit' else 'overall_appearance_rate'
        place_key = 'avg_placement' if list_type == 'unit' else 'overall_avg_placement'

        header_rate = ClickableLabel(data=rate_key)
        header_rate.setText("出场率")
        header_rate.setStyleSheet(header_style)
        header_rate.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        header_rate.setFixedWidth(60) # e.g., 增大到 60
        header_rate.setProperty("original_text", "出场率")
        header_rate.clicked.connect(lambda key=rate_key, ltype=list_type: self.handle_sort_request(key, ltype))

        header_place = ClickableLabel(data=place_key)
        header_place.setText("平均排名")
        header_place.setStyleSheet(header_style)
        header_place.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        header_place.setFixedWidth(60) # e.g., 增大到 60
        header_place.setProperty("original_text", "平均排名")
        header_place.clicked.connect(lambda key=place_key, ltype=list_type: self.handle_sort_request(key, ltype))

        # --- 存储表头控件引用 --- 
        header_widgets_map = self.unit_header_widgets if list_type == 'unit' else self.item_header_widgets
        # header_widgets_map['name'] = header_name # 名称列不需要排序指示
        header_widgets_map[rate_key] = header_rate
        header_widgets_map[place_key] = header_place

        header_layout.addWidget(header_name, 1)
        header_layout.addWidget(header_rate)
        header_layout.addWidget(header_place)
        layout.addLayout(header_layout)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE)
        scroll_area.setFixedHeight(350)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(1) # 行间距保持紧凑，让行本身变高
        scroll_area.setWidget(content_widget)

        layout.addWidget(scroll_area)
        frame.setProperty("content_layout", content_layout)
        return frame

    def update_content(self, unit_stats, item_stats, item_unit_stats):
        """使用加载的数据更新所有区域"""
        self.unit_stats = sorted(unit_stats or [], key=lambda x: x.get('appearance_rate') if x.get('appearance_rate') is not None else 0, reverse=True)
        self.item_stats = sorted(item_stats or [], key=lambda x: x.get('overall_appearance_rate') if x.get('overall_appearance_rate') is not None else 0, reverse=True)
        self.item_unit_stats = item_unit_stats or {}

        self._update_unit_list()
        self._update_item_list()

    def _update_unit_list(self):
        """更新英雄列表区域显示 (添加排序)"""
        layout = self.unit_list_widget.property("content_layout")
        # 清空旧内容
        while layout.count():
            item = layout.takeAt(0)
            if item and item.widget(): item.widget().deleteLater()

        if not self.unit_stats:
            empty = QLabel("暂无数据")
            empty.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 9px; padding: 5px;")
            empty.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(empty)
            return

        # --- 在填充前排序 --- 
        try:
            sort_key = self.unit_sort_column
            is_reversed = (self.unit_sort_order == Qt.SortOrder.DescendingOrder)
            default_value = float('-inf') if is_reversed else float('inf')

            safe_key_func = lambda x: x.get(sort_key) if x.get(sort_key) is not None else default_value

            sorted_units = sorted(self.unit_stats, key=safe_key_func, reverse=is_reversed)
        except Exception as e:
            print(f"英雄列表排序失败: {e}")
            sorted_units = self.unit_stats # 排序失败则使用原顺序
        # --- 排序结束 --- 

        for unit_data in sorted_units:
            # --- 添加检查：如果缺少关键信息，则跳过 --- 
            hero_name = unit_data.get('hero_cn_name')
            icon_path = unit_data.get('icon_path')
            if not hero_name or not icon_path: # 英雄名和图标路径都必须有
                print(f"警告: 跳过无效的英雄统计行: {unit_data}")
                continue
            # --- 检查结束 --- 

            row = self._create_unit_list_row(unit_data)
            if row: layout.addWidget(row)

        # 更新表头样式
        self.update_header_styles('unit')

    def _create_unit_list_row(self, unit_data):
        """创建单个英雄统计行 (减小列间距)"""
        hero_name = unit_data.get('hero_cn_name', 'N/A')
        icon_path = unit_data.get('icon_path')

        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(4, 8, 4, 8)
        row_layout.setSpacing(4) # e.g., 减小到 4

        # --- 图标 --- 
        icon_label = IconLabel(data=hero_name, icon_size=ICON_SIZE_MEDIUM, icon_type='hero')
        icon_label.set_icon(icon_path, hero_name)
        icon_label.setToolTip(hero_name)
        icon_label.clicked.connect(lambda data=hero_name: self.hero_selected.emit(data))

        # --- 名称 (改为 ClickableLabel) --- 
        name_label = ClickableLabel(data=hero_name) # <-- 改为 ClickableLabel
        name_label.setText(hero_name)
        name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: 10px;")
        name_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        name_label.clicked.connect(lambda data=hero_name: self.hero_selected.emit(data)) # <-- 连接信号

        # --- 出场率 --- 
        rate_value = unit_data.get('appearance_rate')
        rate_text = "--"
        if rate_value is not None:
            try:
                if float(rate_value) > 1:
                    rate_text = f"{float(rate_value) * 100:.1f}%"
                else:
                    rate_text = format_rate(rate_value)
            except (ValueError, TypeError):
                rate_text = "ERR"
        play_rate_label = QLabel(rate_text)
        play_rate_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT};font-size: 10px;")
        play_rate_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        play_rate_label.setFixedWidth(60) # 同步增大到 60

        # --- 平均排名 --- 
        avg_place = unit_data.get('avg_placement')
        avg_place_label = QLabel(format_placement(avg_place))
        avg_place_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT};font-size: 10px;")
        avg_place_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        avg_place_label.setFixedWidth(60) # 同步增大到 60

        # --- 添加到布局 --- 
        row_layout.addWidget(icon_label)
        row_layout.addWidget(name_label)
        row_layout.addStretch(1)
        row_layout.addWidget(play_rate_label)
        row_layout.addWidget(avg_place_label)

        # --- 修改：移除 row_widget 默认样式，调整悬停 --- 
        row_widget.setStyleSheet("QWidget { background-color: transparent; border: none; }") # 默认透明无边框
        row_widget.setAutoFillBackground(True) # 需要设置这个才能让背景色生效

        # 悬停效果直接修改背景色
        row_widget.enterEvent = lambda event, w=row_widget: w.setStyleSheet(f"QWidget {{ background-color: {ITEM_ROW_BG_HOVER}; border: none; border-radius: 3px; }}")
        row_widget.leaveEvent = lambda event, w=row_widget: w.setStyleSheet("QWidget { background-color: transparent; border: none; }")

        return row_widget

    def _update_item_list(self):
        """更新装备列表区域显示 (添加排序)"""
        layout = self.item_list_widget.property("content_layout")
        # 清空旧内容
        while layout.count():
            item = layout.takeAt(0)
            if item and item.widget(): item.widget().deleteLater()

        if not self.item_stats:
            empty = QLabel("暂无数据")
            empty.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 9px; padding: 5px;")
            empty.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(empty)
            return

        # --- 在填充前排序 --- 
        try:
            sort_key = self.item_sort_column
            is_reversed = (self.item_sort_order == Qt.SortOrder.DescendingOrder)
            default_value = float('-inf') if is_reversed else float('inf')

            safe_key_func = lambda x: x.get(sort_key) if x.get(sort_key) is not None else default_value

            sorted_items = sorted(self.item_stats, key=safe_key_func, reverse=is_reversed)
        except Exception as e:
            print(f"装备列表排序失败: {e}")
            sorted_items = self.item_stats # 排序失败则使用原顺序
        # --- 排序结束 --- 

        for item_data in sorted_items:
            row = self._create_item_list_row(item_data)
            if row: layout.addWidget(row)

        # 更新表头样式
        self.update_header_styles('item')

    def _create_item_list_row(self, item_data):
        """创建装备列表中的一行 (减小列间距)"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(4, 8, 4, 8)
        layout.setSpacing(4) # e.g., 减小到 4

        item_name = item_data.get('item_name', '?')
        icon_path = item_data.get('icon_path')

        # --- 装备图标 --- 
        icon_label = IconLabel(data=item_name, icon_size=ICON_SIZE_MEDIUM, icon_type='item')
        icon_label.set_icon(icon_path, item_name)
        icon_label.setToolTip(item_name)
        icon_label.clicked.connect(lambda data=item_name: self.item_selected.emit(data))

        # --- 装备名称 (改为 ClickableLabel) --- 
        name_label = ClickableLabel(data=item_name) # <-- 改为 ClickableLabel
        name_label.setText(item_name)
        name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: 10px;")
        name_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        name_label.clicked.connect(lambda data=item_name: self.item_selected.emit(data)) # <-- 连接信号

        # --- 出场率 --- 
        rate_value = item_data.get('overall_appearance_rate')
        rate_text = "--"
        if rate_value is not None:
            try:
                if float(rate_value) > 1:
                    rate_text = f"{float(rate_value) * 100:.1f}%"
                else:
                    rate_text = format_rate(rate_value)
            except (ValueError, TypeError):
                rate_text = "ERR"
        rate_label = QLabel(rate_text)
        rate_label.setStyleSheet(f"color:{TEXT_COLOR_LIGHT}; font-size:10px;")
        rate_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        rate_label.setFixedWidth(60) # 同步增大到 60

        # --- 平均排名 --- 
        place_label = QLabel(format_placement(item_data.get('overall_avg_placement')))
        place_label.setStyleSheet(f"color:{TEXT_COLOR_LIGHT}; font-size:10px;")
        place_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        place_label.setFixedWidth(60) # 同步增大到 60

        # --- 添加到布局 --- 
        layout.addWidget(icon_label)
        layout.addWidget(name_label)
        layout.addStretch(1)
        layout.addWidget(rate_label)
        layout.addWidget(place_label)

        # --- 修改：移除 row_widget 默认样式，调整悬停 --- 
        widget.setStyleSheet("QWidget { background-color: transparent; border: none; }") # 默认透明无边框
        widget.setAutoFillBackground(True)

        # 悬停效果直接修改背景色
        widget.enterEvent = lambda event, w=widget: w.setStyleSheet(f"QWidget {{ background-color: {ITEM_ROW_BG_HOVER}; border: none; border-radius: 3px; }}")
        widget.leaveEvent = lambda event, w=widget: w.setStyleSheet("QWidget { background-color: transparent; border: none; }")

        return widget

    # --- 新增：排序处理方法 --- 
    @Slot(str, str)
    def handle_sort_request(self, column_key, list_type):
        """处理表头点击，更新排序状态"""
        print(f"排序请求: 列={column_key}, 列表={list_type}")
        if list_type == 'unit':
            current_column = self.unit_sort_column
            current_order = self.unit_sort_order
            if current_column == column_key:
                self.unit_sort_order = Qt.SortOrder.AscendingOrder if current_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
            else:
                self.unit_sort_column = column_key
                # 设置默认排序方向
                self.unit_sort_order = Qt.SortOrder.AscendingOrder if column_key == 'avg_placement' else Qt.SortOrder.DescendingOrder
            self._update_unit_list() # 重新排序并更新列表
        elif list_type == 'item':
            current_column = self.item_sort_column
            current_order = self.item_sort_order
            if current_column == column_key:
                self.item_sort_order = Qt.SortOrder.AscendingOrder if current_order == Qt.SortOrder.DescendingOrder else Qt.SortOrder.DescendingOrder
            else:
                self.item_sort_column = column_key
                # 设置默认排序方向
                self.item_sort_order = Qt.SortOrder.AscendingOrder if column_key == 'overall_avg_placement' else Qt.SortOrder.DescendingOrder
            self._update_item_list() # 重新排序并更新列表

    def update_header_styles(self, list_type):
        """更新指定列表的表头样式以显示排序指示"""
        if list_type == 'unit':
            header_widgets = self.unit_header_widgets
            current_column = self.unit_sort_column
            current_order = self.unit_sort_order
        elif list_type == 'item':
            header_widgets = self.item_header_widgets
            current_column = self.item_sort_column
            current_order = self.item_sort_order
        else:
            return

        sort_indicator = " ▼" if current_order == Qt.SortOrder.DescendingOrder else " ▲"
        base_style = f"color: {TEXT_COLOR_MEDIUM}; font-size: 10px; font-weight: bold; background: transparent; border: none;"
        selected_style = f"color: {TEXT_COLOR_LIGHT}; font-size: 10px; font-weight: bold; background: transparent; border: none;" # 选中列高亮

        for key, label in header_widgets.items():
            original_text = label.property("original_text")
            if key == current_column:
                label.setStyleSheet(selected_style)
                label.setText(original_text + sort_indicator)
            else:
                label.setStyleSheet(base_style)
                label.setText(original_text)


# --- 主视图 ---
class 阵容详情视图(QWidget):
    """显示阵容详细信息的视图"""
    hero_selected_from_detail = Signal(str) # 点击英雄图标时发出
    item_selected_from_detail = Signal(str) # 点击装备图标时发出

    def __init__(self, parent=None):
        super().__init__(parent)
        self.comp_name = ""
        self.comp_base_data = {}
        self.comp_heroes = []
        self.level_recs_data = {}
        self.unit_stats = []
        self.item_stats = []
        self.item_unit_stats = {}
        
        # 用于跟踪加载状态
        self.loading_tasks = {
            "base_data": False,
            "heroes": False,
            "level_recs": False,
            "unit_stats": False,
            "item_stats": False,
            "item_unit_stats": False
        }
        self.loading_finished = False

        self.init_ui()

    def init_ui(self):
        """初始化用户界面 (添加提示标签)"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(10)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 顶部阵容信息区域 ---
        self.top_info_frame = QFrame()
        top_info_layout = QVBoxLayout(self.top_info_frame)
        top_info_layout.setContentsMargins(0, 0, 0, 0)
        top_info_layout.setSpacing(5) # 名称、统计、英雄行之间的间距

        # --- 标题行 (阵容名称 + 提示) --- 
        title_layout = QHBoxLayout()
        self.comp_name_label = QLabel("阵容名称")
        self.comp_name_label.setStyleSheet(f"font-size: 22px; font-weight: bold; color: {TEXT_COLOR_LIGHT};")
        title_layout.addWidget(self.comp_name_label)
        title_layout.addStretch() # 推向右侧

        # --- 新增：提示标签 --- 
        hint_label = QLabel("(点击英雄进入装备推荐)")
        # 使用与统计数据相似的字体大小
        hint_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_MEDIUM}px;")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        title_layout.addWidget(hint_label)
        # --- 提示标签结束 --- 

        top_info_layout.addLayout(title_layout)

        # 统计行 (在名称下方, 靠左对齐, 字体加大加粗)
        stats_layout = QHBoxLayout()
        stats_layout.setContentsMargins(0, 4, 0, 0) # 顶部加一点间距
        stats_layout.setSpacing(15) # 统计项之间的间距增大
        self.avg_place_label = QLabel("均名: --")
        self.pick_rate_label = QLabel("出场率: --")
        self.win_rate_label = QLabel("登顶: --")
        self.top4_rate_label = QLabel("前四: --")
        # 使用常量定义字体大小和加粗
        stat_font_size = FONT_SIZE_MEDIUM # 例如 13px
        stat_font_weight = FONT_WEIGHT_BOLD # 例如 700
        stat_style = f"color:{TEXT_COLOR_MEDIUM}; font-size: {stat_font_size}px; font-weight: {stat_font_weight};"
        # stats_layout.addStretch(1) # 移除左侧 stretch
        for label in [self.avg_place_label, self.pick_rate_label, self.win_rate_label, self.top4_rate_label]:
            label.setStyleSheet(stat_style)
            stats_layout.addWidget(label, 0, Qt.AlignmentFlag.AlignLeft) # 靠左对齐
        stats_layout.addStretch(1) # 末尾添加 stretch，让所有统计项靠左
        top_info_layout.addLayout(stats_layout)


        # 核心英雄行 (加大图标和字体)
        self.core_heroes_widget = QWidget()
        self.core_heroes_layout = QHBoxLayout(self.core_heroes_widget)
        self.core_heroes_layout.setContentsMargins(0, 8, 0, 0) # 增大上方间距
        self.core_heroes_layout.setSpacing(10) # 增大英雄间距
        top_info_layout.addWidget(self.core_heroes_widget)

        self.main_layout.addWidget(self.top_info_frame)

        # --- 创建加载遮罩 ---
        self.loading_mask = QWidget(self)
        self.loading_mask.setObjectName("LoadingMask")
        self.loading_mask.setStyleSheet(f"""
            #LoadingMask {{
                background-color: rgba(20, 20, 20, 0.75);
                border-radius: 10px;
            }}
        """)
        self.loading_mask.setVisible(False)  # 初始隐藏
        
        loading_layout = QVBoxLayout(self.loading_mask)
        loading_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 进度条
        self.loading_progress = QProgressBar()
        self.loading_progress.setFixedSize(250, 20)
        self.loading_progress.setRange(0, 100)
        self.loading_progress.setValue(0)
        self.loading_progress.setStyleSheet("""
            QProgressBar {
                background-color: #2A2C36;
                border: 1px solid #3B3F51;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #6495ED;
                border-radius: 4px;
            }
        """)
        loading_layout.addWidget(self.loading_progress)
        
        self.loading_text = QLabel("阵容数据加载中...")
        self.loading_text.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: 16px;")
        self.loading_text.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_layout.addWidget(self.loading_text)
        
        self.loading_subtitle = QLabel("请稍候，这可能需要几秒钟")
        self.loading_subtitle.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 12px;")
        self.loading_subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_layout.addWidget(self.loading_subtitle)

        # --- Tabs ---
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet(TAB_STYLE)

        # 创建 Tab 内容 Widget
        self.options_tab = OptionsTabContent(parent=self)
        self.units_items_tab = UnitsItemsTabContent()

        # 连接内部信号到外部
        self.options_tab.hero_selected.connect(self.hero_selected_from_detail)
        self.units_items_tab.hero_selected.connect(self.hero_selected_from_detail)
        self.units_items_tab.item_selected.connect(self.item_selected_from_detail)

        self.tabs.addTab(self.options_tab, "阵容推荐")
        self.tabs.addTab(self.units_items_tab, "单位&装备统计")

        self.main_layout.addWidget(self.tabs, 1)
        
        # 加载超时计时器
        self.loading_timer = QTimer(self)
        self.loading_timer.timeout.connect(self.update_loading_progress)
        self.loading_timer.setInterval(100)  # 每100毫秒更新一次
        self.progress_value = 0
        self.loading_timeout_timer = QTimer(self)
        self.loading_timeout_timer.timeout.connect(self.handle_loading_timeout)
        self.loading_timeout_timer.setSingleShot(True)

    def load_comp_data(self, comp_name):
        """加载指定阵容的数据 (移除全局数据加载)"""
        if not comp_name or self.comp_name == comp_name:
            return
        
        self.comp_name = comp_name
        print(f"开始加载阵容详情: {self.comp_name}")
        print(f"阵容 {self.comp_name} 详情正在加载中...")
        
        # 重置状态 (不包括全局 map)
        self._reset_data()
        
        # 显示加载中效果
        self.show_loading_mask(True)
        
        # 重置加载状态跟踪
        for key in self.loading_tasks:
            self.loading_tasks[key] = False
        self.loading_finished = False
        
        # 开始加载进度显示
        self.progress_value = 0
        self.loading_progress.setValue(0)
        self.loading_timer.start()
            
        # 设置加载超时（10秒）
        self.loading_timeout_timer.start(10000)

        # --- 异步加载所有需要的数据 (使用特定 query_key) --- 
        # 1. 阵容基础信息 (不变)
        sql_base = "SELECT name, tier, avg_placement, frequency, win_rate, top4_rate FROM comps_base WHERE name = ?"
        query_key_base = f"comp_base_{comp_name}"
        execute_query_async(sql_base, (comp_name,), self.on_base_data_loaded, self.on_load_error, query_key=query_key_base)

        # 2. 阵容核心英雄 (不变)
        sql_heroes = "SELECT ch.hero_cn_name, h.icon_path FROM comp_heroes ch JOIN heroes h ON ch.hero_cn_name = h.cn_name WHERE ch.comp_name = ? ORDER BY ch.hero_order"
        query_key_heroes = f"comp_heroes_{comp_name}"
        execute_query_async(sql_heroes, (comp_name,), self.on_heroes_loaded, self.on_load_error, query_key=query_key_heroes)

        # 3. 等级推荐数据 (不变)
        sql_level_recs = "SELECT level, units, traits, avg_placement, count FROM comp_detail_level_recs WHERE comp_name = ? ORDER BY level, recommendation_index"
        query_key_level_recs = f"comp_level_recs_{comp_name}"
        execute_query_async(sql_level_recs, (comp_name,), self.on_level_recs_loaded, self.on_load_error, query_key=query_key_level_recs)

        # 4. 单位统计数据 (不变)
        sql_unit_stats = """
            SELECT cs.unit_id, cs.appearance_rate, cs.avg_placement, cs.total_count, h.cn_name as hero_cn_name, h.icon_path
            FROM comp_detail_unit_stats cs
            LEFT JOIN heroes h ON cs.unit_id = h.en_name OR cs.unit_id = h.cn_name /* 尝试匹配英文名或中文名 */
            WHERE cs.comp_name = ?
        """
        query_key_unit_stats = f"comp_unit_stats_{comp_name}"
        execute_query_async(sql_unit_stats, (comp_name,), self.on_unit_stats_loaded, self.on_load_error, query_key=query_key_unit_stats)

        # 5. 装备整体统计 (不变)
        sql_item_stats = """
            SELECT cis.item_id, cis.overall_appearance_rate, cis.overall_avg_placement, cis.overall_count, i.name as item_name, i.icon_path
            FROM comp_detail_item_overall_stats cis
            LEFT JOIN items i ON cis.item_id = i.en_name OR cis.item_id = i.name /* 尝试匹配英文名或中文名 */
            WHERE cis.comp_name = ?
        """
        query_key_item_stats = f"comp_item_stats_{comp_name}"
        execute_query_async(sql_item_stats, (comp_name,), self.on_item_stats_loaded, self.on_load_error, query_key=query_key_item_stats)

        # 6. 装备单位统计 (不变)
        sql_item_unit_stats = """
            SELECT cius.item_id, cius.unit_id, cius.avg_placement_on_unit, cius.count_on_unit,
                   h.cn_name as hero_cn_name, h.icon_path as hero_icon_path
            FROM comp_detail_item_unit_stats cius
            LEFT JOIN heroes h ON cius.unit_id = h.en_name OR cius.unit_id = h.cn_name
            WHERE cius.comp_name = ?
        """
        query_key_item_unit_stats = f"comp_item_unit_stats_{comp_name}"
        execute_query_async(sql_item_unit_stats, (comp_name,), self.on_item_unit_stats_loaded, self.on_load_error, query_key=query_key_item_unit_stats)

    def show_loading_mask(self, show=True):
        """显示或隐藏加载遮罩"""
        if show:
            # 调整遮罩大小与位置以覆盖整个视图
            self.loading_mask.setGeometry(self.rect())
            self.loading_mask.raise_()  # 确保在最前面
            self.loading_mask.setVisible(True)
        else:
            self.loading_timer.stop()
            self.loading_mask.setVisible(False)

    def update_loading_progress(self):
        """更新加载进度"""
        # 模拟进度增加，但不超过90%（除非全部加载完成）
        if not self.loading_finished:
            # 慢慢增加到90%
            if self.progress_value < 90:
                self.progress_value += min(5, 90 - self.progress_value)
        else:
            self.progress_value = 100
            
        self.loading_progress.setValue(self.progress_value)
        
        # 如果已经完成，停止定时器并隐藏遮罩
        if self.progress_value >= 100:
            self.loading_timer.stop()
            self.show_loading_mask(False)

    def handle_loading_timeout(self):
        """处理加载超时"""
        if not self.loading_finished:
            print(f"警告: 加载阵容 {self.comp_name} 数据超时")
            # 可以决定是否强制显示已加载的内容
            self.loading_text.setText("加载时间较长，正在继续...")
            # 不自动隐藏，等待数据最终加载完成

    def resizeEvent(self, event):
        """重写调整大小事件，确保加载遮罩始终覆盖整个视图"""
        super().resizeEvent(event)
        if self.loading_mask.isVisible():
            self.loading_mask.setGeometry(self.rect())
    
    def _reset_data(self):
        """重置所有数据 (不包括全局 map)"""
        self.comp_base_data = {}
        self.comp_heroes = []
        self.level_recs_data = {}
        self.unit_stats = []
        self.item_stats = []
        self.item_unit_stats = {}

    def check_loading_complete(self):
        """检查所有加载任务是否完成"""
        all_complete = all(self.loading_tasks.values())
        if all_complete and not self.loading_finished:
            self.loading_finished = True
            print(f"所有阵容 {self.comp_name} 详情数据已加载完成")
            
            # 更新加载进度到100%
            self.progress_value = 100
            self.loading_progress.setValue(100)
            
            # 等待进度条动画完成后隐藏遮罩
            QTimer.singleShot(300, lambda: self.show_loading_mask(False))
            
            # 停止超时计时器
            self.loading_timeout_timer.stop()

    def on_load_error(self, error_message):
        """统一处理加载错误"""
        print(f"加载阵容 {self.comp_name} 数据时出错: {error_message}")
        # 可以在界面上显示统一的错误提示
        self.comp_name_label.setText(f"{self.comp_name} (加载错误)")
        # 清空可能已部分加载的内容
        self.options_tab.update_content({}, {})
        self.units_items_tab.update_content([], [], {})
        
        # 隐藏加载遮罩并显示错误
        self.loading_text.setText(f"加载失败: {error_message}")
        self.loading_subtitle.setText("请返回重试")
        QTimer.singleShot(2000, lambda: self.show_loading_mask(False))


    def on_base_data_loaded(self, results):
        if results: self.comp_base_data = results[0]
        self.loading_tasks["base_data"] = True
        self.update_top_info_ui()
        self.check_loading_complete()

    def on_heroes_loaded(self, results):
        if results: self.comp_heroes = results
        self.loading_tasks["heroes"] = True
        self.update_top_info_ui() # 更新核心英雄显示
        self.check_loading_complete()

    def on_level_recs_loaded(self, results):
        """处理等级推荐数据 (使用全局 hero_info_map，跳过未找到的英雄)"""
        temp_recs = {}
        if results:
            for row in results:
                level = row['level']
                if level not in temp_recs: temp_recs[level] = []
                try: units_en = json.loads(row['units']) if row['units'] else []
                except Exception as e: print(f"E: units JSON {e}"); units_en = [] # 解析失败视为空列表
                try: traits = json.loads(row['traits']) if row['traits'] else []
                except Exception as e: print(f"E: traits JSON {e}"); traits = [] # 解析失败视为空列表

                # --- 转换英雄信息 (使用全局 map，跳过未找到的) ---
                units_cn = []
                units_path = []
                original_units_en_for_rec = [] # 存储原始英文名列表
                if isinstance(units_en, list):
                    for unit_en in units_en:
                        # 使用全局 GLOBAL_HERO_INFO_MAP
                        hero_info = GLOBAL_HERO_INFO_MAP.get(str(unit_en).lower())
                        if hero_info:
                            units_cn.append(hero_info['cn_name'])
                            units_path.append(hero_info['icon_path'])
                            original_units_en_for_rec.append(str(unit_en)) # 保留找到的英雄原始ID
                        else:
                            # 如果找不到英雄信息，直接跳过，不添加到列表中
                            print(f"警告: 在等级 {level} 推荐中未找到英雄 {unit_en} 的信息，已跳过。")
                # --- 转换结束 ---

                rec_data = dict(row)
                # 存储处理后的英雄列表
                rec_data['units'] = original_units_en_for_rec # 更新为实际找到的英雄英文ID列表
                rec_data['units_cn'] = units_cn
                rec_data['units_path'] = units_path
                rec_data['traits'] = traits # 假定 traits 结构是正确的
                temp_recs[level].append(rec_data)
        self.level_recs_data = temp_recs
        self.loading_tasks["level_recs"] = True
        self.update_options_tab()
        self.check_loading_complete()

    def on_unit_stats_loaded(self, results):
        self.unit_stats = results if results else []
        self.loading_tasks["unit_stats"] = True
        self.update_units_items_tab()
        self.check_loading_complete()
        print(f"加载操作完成 (成功): comp_detail_unit_stats_{self.comp_name},剩余待加载: {len(self.loading_tasks) - sum(1 for v in self.loading_tasks.values() if v)}")

    def on_item_stats_loaded(self, results):
        self.item_stats = results if results else []
        self.loading_tasks["item_stats"] = True
        self.update_units_items_tab()
        self.check_loading_complete()

    def on_item_unit_stats_loaded(self, results):
        temp_stats = {}
        if results:
            for row in results:
                item_id = row['item_id']
                if item_id not in temp_stats: temp_stats[item_id] = []
                temp_stats[item_id].append(row)
        self.item_unit_stats = temp_stats
        self.loading_tasks["item_unit_stats"] = True
        self.update_units_items_tab()
        self.check_loading_complete()

    def update_ui(self):
        """更新整个视图的显示"""
        self.update_top_info_ui()
        self.update_options_tab()
        self.update_units_items_tab()

    def update_top_info_ui(self):
        """更新顶部信息区域 (统计数据靠左, 核心英雄图标/名称加大)"""
        self.comp_name_label.setText(self.comp_base_data.get('name', self.comp_name or '加载中...'))
        self.avg_place_label.setText(f"均名: {format_placement(self.comp_base_data.get('avg_placement'))}")
        freq = self.comp_base_data.get('frequency')
        # 修改 "选用" 文本为 "出场率"
        self.pick_rate_label.setText(f"出场率: {freq * 100:.1f}%" if freq is not None else "出场率: --")
        self.win_rate_label.setText(f"登顶: {format_rate(self.comp_base_data.get('win_rate'))}")
        self.top4_rate_label.setText(f"前四: {format_rate(self.comp_base_data.get('top4_rate'))}")

        layout = self.core_heroes_layout
        while self.core_heroes_layout.count():
            item = self.core_heroes_layout.takeAt(0)
            if item and item.widget(): item.widget().deleteLater() # 安全删除

        if not self.comp_heroes: pass
        else:
            # 移除 max_heroes_to_show 限制，直接迭代所有英雄
            for hero_info in self.comp_heroes: # <-- 直接迭代 self.comp_heroes
                hero_name = hero_info['hero_cn_name']
                icon_path = hero_info['icon_path']

                hero_container = QWidget()
                hero_layout = QVBoxLayout(hero_container)
                hero_layout.setContentsMargins(0,0,0,0)
                hero_layout.setSpacing(3) # 稍微增大图标和名称间距
                hero_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

                # 使用更大的图标尺寸 (例如 XLARGE)
                hero_icon = IconLabel(data=hero_name, icon_size=ICON_SIZE_XLARGE, icon_type='hero') #<-- 改为 XLARGE
                hero_icon.set_icon(icon_path, hero_name)
                hero_icon.setToolTip(f"查看 {hero_name} 详情")
                hero_icon.clicked.connect(lambda data=hero_name: self.hero_selected_from_detail.emit(data))

                name_label = QLabel(hero_name)
                # 使用稍大的字号常量
                name_label.setStyleSheet(f"color:{TEXT_COLOR_MEDIUM}; font-size: {FONT_SIZE_MEDIUM}px;") #<-- 使用 FONT_SIZE_MEDIUM (例如 13px)
                name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

                hero_layout.addWidget(hero_icon)
                hero_layout.addWidget(name_label)
                layout.addWidget(hero_container)

            layout.addStretch() # 确保靠左

    def update_options_tab(self):
        """更新 Options Tab 的内容 (检查全局 map 是否加载)"""
        # 确保 level_recs 加载后，并且全局 trait_icon_map 和 hero_info_map 已加载
        if self.level_recs_data and GLOBAL_TRAIT_ICON_MAP and GLOBAL_HERO_INFO_MAP:
            self.options_tab.update_content(self.level_recs_data, GLOBAL_TRAIT_ICON_MAP)
        elif self.level_recs_data:
             # 如果全局数据还没加载完，可以稍后尝试更新，或显示提示
             print("等待全局 Trait/Hero 信息加载以更新 Options Tab...")
             # 可以在这里设置一个定时器或标志，等待全局数据加载完成后再调用一次 update_options_tab
             # 简单起见，暂时只打印日志

    def update_units_items_tab(self):
        """更新 Units & Items Tab 的内容 (移除 hero_base_stats)"""
        # 需要 unit_stats, item_stats, item_unit_stats 加载完成
        if self.unit_stats is not None and self.item_stats is not None and self.item_unit_stats is not None:
             # 移除对 GLOBAL_HERO_BASE_STATS 的检查
             self.units_items_tab.update_content(self.unit_stats, self.item_stats, self.item_unit_stats)
        else:
             # 可以添加日志或处理未加载完的情况
             print("等待 Units/Items Stats 加载以更新 Units & Items Tab...")

    def release_resources(self):
        """释放视图占用的资源，减少内存占用"""
        print(f"释放阵容详情视图 '{self.comp_name}' 的资源")
        
        # 停止所有定时器
        if hasattr(self, 'loading_timer') and self.loading_timer:
            self.loading_timer.stop()
        if hasattr(self, 'loading_timeout_timer') and self.loading_timeout_timer:
            self.loading_timeout_timer.stop()
            
        # 隐藏加载遮罩
        if hasattr(self, 'loading_mask') and self.loading_mask:
            self.loading_mask.setVisible(False)
            
        # 清理数据
        self.comp_base_data = {}
        self.comp_heroes = []
        self.level_recs_data = {}
        self.unit_stats = []
        self.item_stats = []
        self.item_unit_stats = {}
        
        # 重置加载状态
        for key in self.loading_tasks:
            self.loading_tasks[key] = False
        self.loading_finished = False
        
        # 递归清理图标
        self._clear_pixmaps_recursive(self)
        
        # 重置UI到初始状态
        if hasattr(self, 'comp_name_label'):
            self.comp_name_label.setText("阵容名称")
        if hasattr(self, 'avg_place_label'):
            self.avg_place_label.setText("均名: --")
        if hasattr(self, 'pick_rate_label'):
            self.pick_rate_label.setText("出场率: --")
        if hasattr(self, 'win_rate_label'):
            self.win_rate_label.setText("登顶: --")
        if hasattr(self, 'top4_rate_label'):
            self.top4_rate_label.setText("前四: --")
            
        # 清理图标标签
        if hasattr(self, 'core_heroes_layout'):
            self._clear_layout(self.core_heroes_layout)
            
        # 清理选项卡内容
        if hasattr(self, 'options_tab'):
            self.options_tab.update_content({}, {})
        if hasattr(self, 'units_items_tab'):
            self.units_items_tab.update_content([], [], {})
            
    def _clear_pixmaps_recursive(self, widget):
        """递归清理Widget中的所有IconLabel的图像缓存"""
        # 导入IconLabel类以便进行类型检查
        from 自定义组件 import IconLabel
        
        # 递归查找所有子控件
        for child in widget.findChildren(IconLabel):
            if hasattr(child, 'clearPixmap'):
                try:
                    child.clearPixmap()
                except Exception as e:
                    print(f"清理IconLabel时出错: {e}")

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置阵容详情视图到初始状态")
        
        # 阵容详情视图主要是基于传入的阵容名称显示信息
        # 大部分状态由 load_comp_data 方法重新设置
        # 这里主要重置一些可能的UI状态
        
        # 重置Tab选择到第一个（如果有Tab的话）
        if hasattr(self, 'tab_widget') and self.tab_widget:
            self.tab_widget.setCurrentIndex(0)
        
        # 重置可能存在的搜索框（在各个Tab内部）
        # 这些搜索框可能在UnitsItemsTabContent等子组件中
        if hasattr(self, 'units_items_tab') and self.units_items_tab:
            # 重置单位和装备Tab的排序状态
            if hasattr(self.units_items_tab, 'unit_sort_column'):
                self.units_items_tab.unit_sort_column = 'appearance_rate'
            if hasattr(self.units_items_tab, 'unit_sort_order'):
                self.units_items_tab.unit_sort_order = Qt.SortOrder.DescendingOrder
            if hasattr(self.units_items_tab, 'item_sort_column'):
                self.units_items_tab.item_sort_column = 'overall_appearance_rate'
            if hasattr(self.units_items_tab, 'item_sort_order'):
                self.units_items_tab.item_sort_order = Qt.SortOrder.DescendingOrder

    def _clear_layout(self, layout):
        """清空布局中的所有项目"""
        if layout is None:
            return
            
        while layout.count():
            item = layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
            child_layout = item.layout()
            if child_layout:
                self._clear_layout(child_layout)