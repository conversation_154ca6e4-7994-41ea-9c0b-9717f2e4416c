# -*- coding: utf-8 -*-
"""
弈秒决 - 版本发布工具

一个独立的GUI工具，用于方便地将新的数据库和应用版本发布到制品库。
"""

import os
import sys
import json
import time
import hashlib
import requests
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from datetime import datetime
from packaging.version import parse as parse_version
import zipfile # [新增] 导入zipfile模块

# ========== 制品库配置 (与主程序updater.py保持绝对一致) ==========
REPO_CONFIG = {
    "package": "shujugengxin/yu",
    "repo": "g-iuob0664-generic",
    "base_url": "https://g-iuob0664-generic.pkg.coding.net/shujugengxin/yu/"
}
DB_FILE_NAME = "tft_data.db" 

# ========== 核心上传与校验函数 (从GUI总爬取程序.py复用) ==========
def get_md5(file_path):
    """计算文件MD5值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except IOError as e:
        print(f"[错误] 计算MD5失败: {e}")
        return None

def _calculate_sha256(file_path):
    """计算文件的SHA256校验和 (与 updater_main.py 保持一致)"""
    sha256 = hashlib.sha256()
    try:
        with open(file_path, "rb") as f:
            while chunk := f.read(4096):
                sha256.update(chunk)
        return sha256.hexdigest()
    except IOError as e:
        print(f"[错误] 计算SHA256失败: {e}")
        return None

def upload_file_to_repo(file_path, file_name_in_repo):
    """上传单个文件到制品库"""
    coding_token = os.getenv('CODING_TOKEN')
    if not coding_token:
        messagebox.showerror("错误", "未设置 CODING_TOKEN 环境变量！")
        return False
        
    upload_url = REPO_CONFIG['base_url'] + file_name_in_repo
    print(f"开始上传 {os.path.basename(file_path)} -> {upload_url}")
    
    headers = {"Authorization": f"token {coding_token}"}
    try:
        with open(file_path, 'rb') as f:
            response = requests.put(upload_url, headers=headers, data=f)
        
        if response.status_code in [200, 201]:
            print(f"[成功] {file_name_in_repo} 上传成功。")
            return True
        else:
            print(f"[错误] {file_name_in_repo} 上传失败: HTTP {response.status_code}, {response.text}")
            messagebox.showerror("上传失败", f"{file_name_in_repo} 上传失败: {response.status_code}\n{response.text}")
            return False
    except Exception as e:
        print(f"[错误] 上传时发生异常: {e}")
        messagebox.showerror("上传异常", str(e))
        return False

# ========== GUI 应用部分 ==========
class UploadToolApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("弈秒决 - 版本发布工具 v1.5")
        self.geometry("800x750")

        self.db_file_path = tk.StringVar()
        self.zip_file_path = tk.StringVar()
        self.online_app_version = tk.StringVar(value="未知")
        self.online_db_version = tk.StringVar(value="未知")
        self.new_app_version = tk.StringVar()
        self.new_db_version = tk.StringVar()
        self.include_updater_var = tk.BooleanVar(value=False)
        self.is_beta = tk.BooleanVar(value=False)
        
        self.online_version_data = {}

        self._create_widgets()
        self.fetch_online_versions()

    def _create_widgets(self):
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # --- 版本类型选择 ---
        version_type_frame = ttk.LabelFrame(main_frame, text="0. 选择发布类型", padding="10")
        version_type_frame.pack(fill=tk.X, pady=5)
        ttk.Checkbutton(version_type_frame, text="发布为Beta版本 (将上传到独立的beta渠道)", 
                        variable=self.is_beta, command=self.fetch_online_versions).pack(anchor='w')

        # --- 文件选择 ---
        file_frame = ttk.LabelFrame(main_frame, text="1. 选择要上传的数据库文件", padding="10")
        file_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(file_frame, textvariable=self.db_file_path, state="readonly").pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(file_frame, text="浏览...", command=self.browse_db_file).pack(side=tk.LEFT)

        # --- 版本信息 ---
        version_frame = ttk.LabelFrame(main_frame, text="2. 填写版本信息", padding="10")
        version_frame.pack(fill=tk.X, pady=5)
        
        online_frame = ttk.Frame(version_frame)
        online_frame.pack(fill=tk.X, pady=5)
        ttk.Label(online_frame, text="当前线上版本:").pack(side=tk.LEFT, anchor='w')
        ttk.Button(online_frame, text="刷新", command=self.fetch_online_versions).pack(side=tk.RIGHT)
        ttk.Label(online_frame, textvariable=self.online_app_version, foreground="blue").pack(side=tk.LEFT, padx=10)
        ttk.Label(online_frame, text="数据库:").pack(side=tk.LEFT)
        ttk.Label(online_frame, textvariable=self.online_db_version, foreground="blue").pack(side=tk.LEFT, padx=10)

        new_version_grid = ttk.Frame(version_frame)
        new_version_grid.pack(fill=tk.X, pady=5)
        new_version_grid.columnconfigure(1, weight=1)

        ttk.Label(new_version_grid, text="发布应用新版:").grid(row=0, column=0, sticky='w', pady=2)
        ttk.Entry(new_version_grid, textvariable=self.new_app_version).grid(row=0, column=1, sticky='we', padx=5)

        ttk.Label(new_version_grid, text="发布数据库新版:").grid(row=1, column=0, sticky='w', pady=2)
        ttk.Entry(new_version_grid, textvariable=self.new_db_version).grid(row=1, column=1, sticky='we', padx=5)
        ttk.Button(new_version_grid, text="自动生成", command=self.generate_db_version).grid(row=1, column=2, padx=5)

        # --- 发布日志 ---
        notes_frame = ttk.LabelFrame(main_frame, text="3. 填写应用更新日志 (Release Notes)", padding="10")
        notes_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.release_notes_text = scrolledtext.ScrolledText(notes_frame, height=8, wrap=tk.WORD)
        self.release_notes_text.pack(fill=tk.BOTH, expand=True)
        self.release_notes_text.insert(tk.END, "【本次更新内容】\n- \n- \n")

        # --- 应用更新包上传与自动打包 ---
        zip_frame = ttk.LabelFrame(main_frame, text="4. 上传应用更新包 (.zip)", padding="10")
        zip_frame.pack(fill=tk.X, pady=5)
        
        ttk.Entry(zip_frame, textvariable=self.zip_file_path, state="readonly").pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Button(zip_frame, text="一键打包", command=self.create_update_package).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(zip_frame, text="浏览...", command=self.browse_zip_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(zip_frame, text="同时更新启动器", variable=self.include_updater_var).pack(side=tk.LEFT, padx=5)

        # --- 上传按钮 ---
        upload_button = ttk.Button(main_frame, text="开始上传", command=self.start_upload, style="Accent.TButton")
        upload_button.pack(pady=10, ipady=5)

    def browse_db_file(self):
        path = filedialog.askopenfilename(
            title="选择数据库文件",
            filetypes=[("Database files", "*.db"), ("All files", "*.*")]
        )
        if path:
            self.db_file_path.set(path)
            print(f"已选择数据库文件: {path}")

    def browse_zip_file(self):
        path = filedialog.askopenfilename(
            title="选择应用更新压缩包",
            filetypes=[("Zip archives", "*.zip"), ("All files", "*.*")]
        )
        if path:
            self.zip_file_path.set(path)
            print(f"已选择应用更新包: {path}")

    def create_update_package(self):
        # [修复] 使用基于脚本位置的绝对路径，解决工作目录问题
        # __file__ 是当前脚本(upload_tool.py)的路径
        # os.path.dirname(__file__) 是 'yimiaojue' 目录
        # os.path.dirname(...) 是项目根目录 'yundzhenghe'
        project_root = os.path.dirname(os.path.abspath(__file__))
        dist_dir = os.path.join(project_root, "..", "dist") # 从yimiaojue目录上溯一级到根，再进入dist
        
        main_app_dir = os.path.join(dist_dir, "YimiaoJue")
        
        if not os.path.isdir(main_app_dir):
            messagebox.showerror("错误", f"找不到目录: '{os.path.abspath(main_app_dir)}'\n\n请确保已运行 build.bat 并成功生成了 'dist/YimiaoJue' 目录。")
            return

        main_exe_path = os.path.join(main_app_dir, "YimiaoJue.exe")
        if not os.path.exists(main_exe_path):
            messagebox.showerror("错误", f"在 '{main_app_dir}' 目录中找不到 'YimiaoJue.exe'。\n\n请检查构建是否成功。")
            return

        updater_path = os.path.join(dist_dir, "updater.exe")
        if self.include_updater_var.get() and not os.path.exists(updater_path):
            messagebox.showerror("打包失败", f"选择了包含启动器，但找不到 '{updater_path}'。")
            return
        
        output_zip_name = "YimiaoJue-update.zip"
        output_zip_path = os.path.join(dist_dir, output_zip_name)
        
        try:
            print(f"正在创建更新包: {output_zip_path}")
            with zipfile.ZipFile(output_zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
                print("正在添加主程序目录...")
                for root, _, files in os.walk(main_app_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, dist_dir).replace('\\', '/')
                        zf.write(file_path, arcname)
                
                if self.include_updater_var.get():
                    zf.write(updater_path, "updater.exe")
                    print(f" - 已添加: updater.exe")
            
            self.zip_file_path.set(output_zip_path)
            messagebox.showinfo("打包成功", f"更新包 '{output_zip_name}' 创建成功！")
            
        except Exception as e:
            messagebox.showerror("打包失败", f"创建zip文件时发生错误：\n{e}")

    def fetch_online_versions(self):
        print("正在获取线上版本信息...")
        is_beta = self.is_beta.get()
        channel_name = "Beta" if is_beta else "正式"
        version_filename = "version-beta.json" if is_beta else "version.json"
        version_url = REPO_CONFIG['base_url'] + version_filename
        
        print(f"正在获取 {channel_name} 版本信息 from {version_url}")
        try:
            response = requests.get(version_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.online_version_data = data
                
                app_ver = data.get("app", {}).get("version", "未找到")
                db_ver = data.get("database", {}).get("version", "未找到")
                self.online_app_version.set(f"App: v{app_ver}")
                self.online_db_version.set(f"v{db_ver}")
                self.new_app_version.set(app_ver)
                self.new_db_version.set(db_ver)
                self.release_notes_text.delete('1.0', tk.END)
                self.release_notes_text.insert(tk.END, data.get("app", {}).get("release_notes", ""))
                print(f"获取成功 -> App: {app_ver}, DB: {db_ver}")
            elif response.status_code == 404:
                messagebox.showinfo("提示", f"未找到线上的 {channel_name} 版本文件。\n将使用默认值。")
                self.online_version_data = {}
                self.online_app_version.set(f"App: v0.0.0")
                self.online_db_version.set(f"v0")
                self.new_app_version.set("0.0.1-beta.1" if is_beta else "0.0.1")
                self.generate_db_version()
                self.release_notes_text.delete('1.0', tk.END)
                self.release_notes_text.insert(tk.END, f"【{channel_name}版本首次发布】\n- \n")
            else:
                messagebox.showwarning("获取失败", f"无法获取版本信息, HTTP状态码: {response.status_code}")
        except Exception as e:
            messagebox.showerror("获取异常", str(e))

    def generate_db_version(self):
        today = datetime.now().strftime("%Y%m%d")
        current_ver = self.new_db_version.get()
        if current_ver.startswith(today):
            parts = current_ver.split('.')
            new_seq = int(parts[1]) + 1
            self.new_db_version.set(f"{today}.{new_seq}")
        else:
            self.new_db_version.set(f"{today}.1")

    def start_upload(self):
        db_path = self.db_file_path.get()
        zip_path = self.zip_file_path.get()
        new_app_ver = self.new_app_version.get()
        new_db_ver = self.new_db_version.get()
        release_notes = self.release_notes_text.get("1.0", tk.END).strip()
        is_beta = self.is_beta.get()

        if is_beta:
            version_filename = "version-beta.json"
            zip_filename_in_repo = "YimiaoJue-beta.zip"
            db_filename_in_repo = "tft_data-beta.db"
        else:
            version_filename = "version.json"
            zip_filename_in_repo = "YimiaoJue-update.zip"
            db_filename_in_repo = DB_FILE_NAME

        if not new_app_ver or not new_db_ver:
            messagebox.showerror("错误", "新版本号不能为空！")
            return
        try:
            parse_version(new_app_ver)
        except ValueError:
            messagebox.showerror("错误", "应用版本号格式不正确！(例如: 1.2.3)")
            return
        
        online_app_ver = self.online_version_data.get("app", {}).get("version", "0.0.0")
        # [移除] 移除对新版本必须上传文件的强制校验，增加灵活性
        # if parse_version(new_app_ver) > parse_version(online_app_ver) and not zip_path:
        #     messagebox.showerror("错误", "您正在发布一个新的应用版本，\n必须选择一个对应的.zip更新包进行上传！")
        #     return

        confirm_msg = f"请确认发布信息：[ {'BETA' if is_beta else '正式版'} ]\n\n"
        if zip_path:
            confirm_msg += f"📦 应用更新包: {os.path.basename(zip_path)}\n"
        else:
            confirm_msg += "📦 应用更新包: (不上传, 沿用旧版本文件)\n"
        confirm_msg += f"   应用版本: {self.online_app_version.get()} -> v{new_app_ver}\n"
        if db_path:
            confirm_msg += f"\n💿 数据库文件: {os.path.basename(db_path)}\n"
        else:
            confirm_msg += "\n💿 数据库文件: (不上传, 沿用旧版本文件)\n"
        confirm_msg += f"   数据库版本: {self.online_db_version.get()} -> {new_db_ver}\n\n"
        if not messagebox.askyesno("发布确认", confirm_msg):
            return

        app_checksum = None
        app_checksum_type = "sha256"
        app_update_url = self.online_version_data.get("app", {}).get("url")
        db_checksum = None
        db_url = self.online_version_data.get("database", {}).get("url")

        if zip_path:
            app_checksum = _calculate_sha256(zip_path)
            if not app_checksum:
                messagebox.showerror("错误", "应用更新包校验和(SHA256)计算失败，上传中止。")
                return
            if not upload_file_to_repo(zip_path, zip_filename_in_repo):
                 print("应用更新包上传失败，上传中止。")
                 return
            app_update_url = REPO_CONFIG['base_url'] + zip_filename_in_repo
        else:
            app_checksum = self.online_version_data.get("app", {}).get("checksum")
            app_checksum_type = self.online_version_data.get("app", {}).get("checksum_type")
            if not all([app_update_url, app_checksum, app_checksum_type]):
                messagebox.showerror("错误", "无法获取旧的应用版本信息！")
                return

        if db_path:
            db_checksum = get_md5(db_path)
            if not db_checksum:
                print("数据库MD5计算失败，上传中止。")
                return
            if not upload_file_to_repo(db_path, db_filename_in_repo):
                print("数据库上传失败，上传中止。")
                return
            db_url = REPO_CONFIG['base_url'] + db_filename_in_repo
        else:
            db_checksum = self.online_version_data.get("database", {}).get("checksum")
            if not all([db_url, db_checksum]):
                messagebox.showerror("错误", "无法获取旧的数据库版本信息！")
                return

        new_version_data = {
            "app": {
                "version": new_app_ver,
                "release_notes": release_notes,
                "url": app_update_url,
                "checksum": app_checksum,
                "checksum_type": app_checksum_type
            },
            "database": {
                "version": new_db_ver,
                "url": db_url,
                "checksum": db_checksum
            }
        }
        
        temp_version_path = "version.json.tmp"
        with open(temp_version_path, 'w', encoding='utf-8') as f:
            json.dump(new_version_data, f, indent=2, ensure_ascii=False)
            
        if not upload_file_to_repo(temp_version_path, version_filename):
            print(f"{version_filename} 上传失败！")
            os.remove(temp_version_path)
            return

        os.remove(temp_version_path)
        print("="*20 + " 发布成功！ " + "="*20)
        messagebox.showinfo("成功", "新版本已成功发布到制品库！")
        self.fetch_online_versions()

if __name__ == "__main__":
    app = UploadToolApp()
    style = ttk.Style(app)
    style.configure("Accent.TButton", foreground="white", background="dodgerblue")
    app.mainloop()