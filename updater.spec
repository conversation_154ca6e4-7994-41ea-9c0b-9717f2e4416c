# -*- mode: python ; coding: utf-8 -*-

# 为 onefile 模式正确配置
block_cipher = None

# --- 定义所有需要明确排除的大型库 ---
excludes = [
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6', 
    'matplotlib', 'torch', 'tensorflow', 'sklearn', 
    'notebook', 'jupyter', 'sympy', 'IPython', 'pandas',
    'scipy', 'cv2', 'numpy', 'onnxruntime', 'rapidocr_onnxruntime'
]

# --- DPI 感知 Manifest (与主程序一致) ---
manifest = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <assemblyIdentity
    version="*******"
    processorArchitecture="*"
    name="updater.exe"
    type="win32"
  />
  <description>YimiaoJue Updater</description>
  <dependency>
    <dependentAssembly>
      <assemblyIdentity
        type="win32"
        name="Microsoft.Windows.Common-Controls"
        version="*******"
        processorArchitecture="*"
        publicKeyToken="6595b64144ccf1df"
        language="*"
      />
    </dependentAssembly>
  </dependency>
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v2">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
  <application xmlns="urn:schemas-microsoft-com:asm.v3">
    <windowsSettings>
      <dpiAware xmlns="http://schemas.microsoft.com/SMI/2005/WindowsSettings">true/PM</dpiAware>
      <dpiAwareness xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">PerMonitorV2, PerMonitor</dpiAwareness>
    </windowsSettings>
  </application>
</assembly>
'''

a = Analysis(
    ['yimiaojue\\updater_main.py'],
    pathex=['yimiaojue'],
    binaries=[],
    datas=[],
    hiddenimports=['packaging', 'requests'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 这是正确的 onefile EXE 配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='updater',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='yimiaojue/modules/app.ico',
    manifest=manifest,
)