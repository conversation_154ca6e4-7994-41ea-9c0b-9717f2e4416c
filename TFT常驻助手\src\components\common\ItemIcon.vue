<template>
  <div 
    class="item-icon-container"
    :style="{ width: `${size}px`, height: `${size}px` }"
    @click="handleClick"
  >
    <!-- 图标图片 -->
    <img 
      v-if="iconSrc && !imageError"
      :src="iconSrc"
      :alt="itemName"
      class="item-icon-image"
      @load="onImageLoad"
      @error="onImageError"
    />
    
    <!-- 占位符 -->
    <div 
      v-else
      class="item-icon-placeholder"
      :style="{ fontSize: `${size * 0.4}px` }"
    >
      {{ placeholder }}
    </div>
    
    <!-- 评级徽章 -->
    <div 
      v-if="showTierBadge && tier"
      class="tier-badge"
      :class="getTierBadgeClass(tier)"
    >
      {{ tier }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { getSmartIconPath } from '@/utils/iconUtils'
import type { QueryResult } from '@/types'

// Props
interface Props {
  itemName: string
  iconPath?: string | null
  tier?: string
  size?: number
  showTierBadge?: boolean
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 60,
  showTierBadge: true,
  clickable: true
})

// Emits
const emit = defineEmits<{
  click: [itemName: string]
}>()

// 响应式数据
const iconSrc = ref<string | null>(null)
const imageError = ref(false)

// 全局图标缓存
const iconCache = new Map<string, string | null>()

// 计算属性
const placeholder = computed(() => {
  if (!props.itemName) return '?'
  return props.itemName.charAt(0).toUpperCase()
})

// 评级徽章样式类
const getTierBadgeClass = (tier: string) => {
  const tierClasses = {
    'S': 'tier-s-badge',
    'A': 'tier-a-badge',
    'B': 'tier-b-badge',
    'C': 'tier-c-badge',
    'D': 'tier-d-badge'
  }
  return tierClasses[tier as keyof typeof tierClasses] || 'tier-default-badge'
}

// 加载图标
const loadIcon = async () => {
  let iconPath = props.iconPath

  // 如果没有提供iconPath，尝试通过装备名称获取
  if (!iconPath && props.itemName) {
    try {
      const { invoke } = await import('@tauri-apps/api/core')
      const result = await invoke<QueryResult>('get_item_icon_path', { itemName: props.itemName })
      if (result && !result.error && result.data && result.data.length > 0) {
        iconPath = result.data[0].icon_path
      }
    } catch (error) {
      console.warn(`无法获取装备 ${props.itemName} 的图标路径:`, error)
    }
  }

  if (!iconPath) {
    iconSrc.value = null
    return
  }

  // 检查缓存
  const cacheKey = iconPath
  if (iconCache.has(cacheKey)) {
    iconSrc.value = iconCache.get(cacheKey) || null
    imageError.value = iconSrc.value === null
    return
  }

  imageError.value = false

  try {
    const smartPath = await getSmartIconPath(iconPath)

    // 缓存结果
    iconCache.set(cacheKey, smartPath)
    iconSrc.value = smartPath

    if (!smartPath) {
      console.warn(`未找到装备 ${props.itemName} 的图标: ${iconPath}`)
    }
  } catch (error) {
    console.error(`加载装备 ${props.itemName} 图标失败:`, error)
    iconCache.set(cacheKey, null)
    iconSrc.value = null
  }
}

// 图片加载成功
const onImageLoad = () => {
  imageError.value = false
}

// 图片加载失败
const onImageError = () => {
  console.warn(`装备 ${props.itemName} 图标加载失败: ${iconSrc.value}`)
  imageError.value = true
}

// 点击处理
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.itemName)
  }
}

// 监听iconPath变化
watch(() => props.iconPath, loadIcon, { immediate: true })

// 组件挂载时加载图标
onMounted(() => {
  loadIcon()
})
</script>

<style scoped>
.item-icon-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.item-icon-container:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.item-icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.item-icon-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(255, 255, 255, 0.2);
}

.tier-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.tier-s-badge { background: linear-gradient(135deg, #ff6b9d, #ff4757); }
.tier-a-badge { background: linear-gradient(135deg, #ffa726, #ff8f00); }
.tier-b-badge { background: linear-gradient(135deg, #ffeb3b, #ffc107); }
.tier-c-badge { background: linear-gradient(135deg, #cddc39, #8bc34a); }
.tier-d-badge { background: linear-gradient(135deg, #4caf50, #2e7d32); }
.tier-default-badge { background: linear-gradient(135deg, #666666, #444444); }
</style>