# 弈秒决 (YimiaoJue) - 项目结构说明 v3.0

本项目是云顶之弈（TFT）的一款高效、模块化的辅助工具，基于 `RapidOCR` 引擎实现，旨在提供精准、低延迟的游戏信息识别功能。

## 核心特性

- **动态坐标适配**: 采用创新的“虚拟16:9边框”方案，无论用户使用何种分辨率或屏幕比例，都能精准定位游戏内元素，确保截图和识别的准确性。
- **事件驱动架构**: 以 `TriggerManager` 作为中央协调器，实时监控游戏内的关键事件（如新回合开始、商店刷新等），并智能调度相应的功能模块（如海克斯识别、装备识别），实现了低资源占用的高效运行模式。
- **高效OCR引擎**: 已从 `Tesseract` 整体迁移至 `RapidOCR`，大幅减小了应用打包体积，解决了中文路径的兼容性问题，并显著提升了在复杂游戏背景下的文字识别准确率。
- **灵活的更新渠道**: 通过根目录下的 `update_config.json` 文件，可以轻松在“正式版 (release)”和“测试版 (beta)”之间切换更新渠道，便于开发者进行A/B测试和灰度发布。

---

## 文件结构详解 (v3.0)

```
yimiaojue/
├── updater_main.py      # 用户启动入口，负责更新检查和启动主程序
├── ocr查询.py           # 主程序核心逻辑和UI协调器
├── updater.py           # 后台更新检查模块
├── update_config.json   # (可选) 用于切换beta/release更新渠道
├── config.py            # 全局静态配置文件
├── config.ini           # 动态配置文件 (如调试模式)
├── utils.py             # 通用工具函数模块 (含坐标转换核心算法)
├── ocr_handler.py       # OCR识别核心模块 (基于RapidOCR)
├── data_query.py        # 数据查询模块 (数据库交互)
├── managers/            # 核心业务逻辑管理器包
│   ├── __init__.py
│   ├── hex_manager.py   # 海克斯识别管理器
│   ├── equip_manager.py # 装备识别管理器
│   └── trigger_manager.py # 中央事件触发与协调器
├── logger_setup.py      # 日志系统配置模块
├── instance_checker.py  # 单例运行检查模块
├── modules/             # 存放依赖项 (如数据库文件)
│   ├── tft_data.db
│   └── ...
└── README.md            # 本说明文件
```

---

## 模块功能详解

### `updater_main.py` & `update_config.json` - 启动与更新中心

- **`updater_main.py`**: 这是用户实际双击运行的 **唯一入口**。它首先会检查自身是否有新版本，然后根据 `update_config.json` 的设置决定从哪个渠道下载更新，完成更新后再启动主程序 `YimiaoJue.exe`。
- **`update_config.json`**: 这是一个可选的配置文件。如果它存在且内容为 `{"channel": "beta"}`，启动器将从测试渠道拉取更新。如果文件不存在，则默认使用正式渠道。

### `ocr查询.py` - 主程序核心

应用的主要业务逻辑和UI界面所在。它负责：
- 创建主应用窗口 (`TFTAssistantApp`)。
- 初始化所有管理器 (`HexManager`, `EquipManager`, `TriggerManager`)。
- 将用户的UI操作分发给对应的管理器。
- 在后台启动一个只检查数据库更新的线程。

### `managers/` (包) - 核心业务逻辑大脑

这是项目分层设计的核心，每个管理器负责一个独立的功能模块。
- **`trigger_manager.py`**: **中央事件触发与协调器**。它接管了之前 `round_manager.py` 的所有职责，负责在后台低功耗地监控游戏状态（如回合、商店状态、特殊按钮等）。当检测到关键事件时，它会精确地唤醒并调用其他管理器执行任务，是整个事件驱动模型的核心。
- **`hex_manager.py`**: 负责海克斯识别的完整生命周期，包括UI显示、状态管理和用户交互。
- **`equip_manager.py`**: 负责装备识别的完整生命周期。

### `ocr_handler.py` & `utils.py` - 图像与坐标处理

- **`ocr_handler.py`**: 封装了所有与 `RapidOCR` 引擎的直接交互，提供了统一的截图函数 `capture_screen`。
- **`utils.py`**: 提供了项目中最关键的坐标转换函数 `convert_relative_to_absolute`，该函数实现了“虚拟16:9边框”算法，是保证多分辨率适配的核心。

### `logger_setup.py` - 日志系统

- 负责配置全局日志系统。所有模块的日志（包括之前的 `ocr_debug.log`）现在都统一输出到 `logs/yimiaojue.log` 文件中。

---

## 调试与诊断

- **诊断模式**: 为了便于远程解决用户问题，程序内置了一个隐藏的诊断模式。在主窗口的任意空白处**点击鼠标右键**，选择"切换诊断模式"，然后重启程序即可开启或关闭。
- **日志查看**: 开启诊断模式后，之前被屏蔽的 `DEBUG` 级别日志（如坐标计算、循环状态等）将被详细记录在 **`logs/yimiaojue.log`** 文件中，便于开发者进行问题定位。