<template>
  <!-- 装备详情页面 -->
  <div class="item-detail-page">
    


    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载装备详情...</p>
    </div>

    <!-- 装备详情内容 -->
    <div v-else-if="itemDetail" class="item-detail-content">
      
      <!-- 装备基本信息 -->
      <div class="item-info-section">
        <div class="item-icon-container">
          <ItemIcon
            :item-name="itemDetail.name || '未知装备'"
            :icon-path="itemDetail.icon_path"
            :tier="itemDetail.tier"
            :size="80"
            :show-tier-badge="true"
            :clickable="false"
          />
        </div>
        
        <div class="item-basic-info">
          <h1 class="item-name">{{ itemDetail.name || '未知装备' }}</h1>
          <div class="item-stats-row">
            <span class="stat-inline">
              出场率: <span class="stat-value" :style="{ color: getStatColor(itemDetail.play_rate, 'playRate') }">
                {{ formatStat(itemDetail.play_rate, 'playRate') }}%
              </span>
            </span>
            <span class="stat-inline">
              平均排名: <span class="stat-value" :style="{ color: getStatColor(itemDetail.avg_placement, 'avgPlace') }">
                {{ formatStat(itemDetail.avg_placement, 'avgPlace') }}
              </span>
            </span>
            <span class="stat-inline">
              登顶率: <span class="stat-value" :style="{ color: getStatColor(itemDetail.top1_rate, 'top1Rate') }">
                {{ formatStat(itemDetail.top1_rate, 'top1Rate') }}%
              </span>
            </span>
          </div>
          
          <!-- 装备类别 -->
          <div v-if="itemCategories.length > 0" class="item-categories">
            <span class="categories-label">装备类型:</span>
            <div class="category-tags">
              <span 
                v-for="category in itemCategories" 
                :key="category"
                class="category-tag"
              >
                {{ category }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 适用英雄统计 -->
      <div class="hero-stats-section">
        <!-- 标题和搜索框在同一行 -->
        <div class="hero-stats-header-row">
          <h2 class="section-title">适用英雄统计</h2>
          <input
            v-model="heroSearchQuery"
            type="text"
            placeholder="搜索英雄名称..."
            class="hero-search-input"
          />
        </div>

        <!-- 英雄列表 -->
        <div v-if="sortedHeroStats.length > 0" class="hero-stats-container">
          <!-- 表头 -->
          <div class="hero-stats-header">
            <div class="header-item header-hero-icon">
              <span class="hero-header-text" @click="sortHeroBy('hero_cn_name')">
                英雄
                <span v-if="heroSortColumn === 'hero_cn_name'" class="sort-indicator">
                  {{ heroSortOrder === 'asc' ? '▲' : '▼' }}
                </span>
              </span>
            </div>
            <div class="header-item header-hero-name"></div>
            <div class="header-item header-hero-play-rate" @click="sortHeroBy('play_rate')">
              出场率
              <span v-if="heroSortColumn === 'play_rate'" class="sort-indicator">
                {{ heroSortOrder === 'asc' ? '▲' : '▼' }}
              </span>
            </div>
            <div class="header-item header-hero-avg-place" @click="sortHeroBy('avg_place')">
              平均排名
              <span v-if="heroSortColumn === 'avg_place'" class="sort-indicator">
                {{ heroSortOrder === 'asc' ? '▲' : '▼' }}
              </span>
            </div>
            <div class="header-item header-hero-top4-rate" @click="sortHeroBy('top4_rate')">
              前四率
              <span v-if="heroSortColumn === 'top4_rate'" class="sort-indicator">
                {{ heroSortOrder === 'asc' ? '▲' : '▼' }}
              </span>
            </div>
            <div class="header-item header-hero-top1-rate" @click="sortHeroBy('top1_rate')">
              登顶率
              <span v-if="heroSortColumn === 'top1_rate'" class="sort-indicator">
                {{ heroSortOrder === 'asc' ? '▲' : '▼' }}
              </span>
            </div>
          </div>

          <!-- 英雄数据行 -->
          <div class="hero-stats-content">
            <div 
              v-for="heroStat in sortedHeroStats" 
              :key="heroStat.hero_cn_name"
              class="hero-row"
              @click="handleHeroClick(heroStat)"
            >
              <!-- 英雄图标 -->
              <div class="hero-cell hero-icon-cell">
                <HeroIcon
                  :hero-name="heroStat.hero_cn_name || '未知英雄'"
                  :icon-path="heroStat.hero_icon_path"
                  :cost="heroStat.cost || 0"
                  :size="40"
                  :show-cost-badge="false"
                  :clickable="false"
                />
              </div>
              
              <!-- 英雄名称 -->
              <div class="hero-cell hero-name-cell">
                {{ heroStat.hero_cn_name }}
              </div>
              
              <!-- 出场率 -->
              <div class="hero-cell hero-play-rate-cell">
                <span :style="{ color: getStatColor(heroStat.play_rate, 'playRate') }">
                  {{ formatStat(heroStat.play_rate, 'playRate') }}%
                </span>
              </div>
              
              <!-- 平均排名 -->
              <div class="hero-cell hero-avg-place-cell">
                <span :style="{ color: getStatColor(heroStat.avg_place, 'avgPlace') }">
                  {{ formatStat(heroStat.avg_place, 'avgPlace') }}
                </span>
              </div>

              <!-- 前四率 -->
              <div class="hero-cell hero-top4-rate-cell">
                <span :style="{ color: getStatColor(heroStat.top4_rate, 'top4Rate') }">
                  {{ formatStat(heroStat.top4_rate, 'top4Rate') }}%
                </span>
              </div>

              <!-- 登顶率 -->
              <div class="hero-cell hero-top1-rate-cell">
                <span :style="{ color: getStatColor(heroStat.top1_rate, 'top1Rate') }">
                  {{ formatStat(heroStat.top1_rate, 'top1Rate') }}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 无英雄数据提示 -->
        <div v-else class="no-hero-data">
          <div class="no-data-icon">👤</div>
          <p>{{ getNoDataMessage() }}</p>
          <p v-if="!isLoading && heroStats.length === 0" class="no-data-hint">
            该装备可能没有足够的使用数据，或者暂未收录相关英雄统计信息
          </p>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-state">
      <div class="error-icon">❌</div>
      <h3>加载失败</h3>
      <p>无法加载装备详情，请稍后重试</p>
      <button @click="loadItemDetail" class="retry-button">重新加载</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { invoke } from '@tauri-apps/api/core'
import ItemIcon from '@/components/common/ItemIcon.vue'
import HeroIcon from '@/components/common/HeroIcon.vue'

// Props
interface Props {
  itemName: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  back: []
  heroClick: [heroName: string]
}>()

// === 装备详情数据接口 ===
interface ItemDetailData {
  name: string
  en_name: string
  tier: string
  icon_path: string
  avg_placement: number
  top1_rate: number
  play_rate: number
  hero_stats?: string
  categories?: string
}

interface HeroStatData {
  hero_cn_name: string
  play_rate: number
  avg_place: number
  top4_rate: number
  top1_rate: number
  cost: number
  hero_icon_path: string
}

// === 响应式数据 ===
const itemDetail = ref<ItemDetailData | null>(null)
const heroStats = ref<HeroStatData[]>([])
const isLoading = ref(true)
const heroSearchQuery = ref('')

// 英雄排序状态
const heroSortColumn = ref<string>('play_rate')
const heroSortOrder = ref<'asc' | 'desc'>('desc')

// === 计算属性 ===
const itemCategories = computed(() => {
  if (!itemDetail.value?.categories) return []
  return itemDetail.value.categories.split(',').map(cat => cat.trim()).filter(cat => cat)
})

const filteredHeroStats = computed(() => {
  console.log('🔍 计算filteredHeroStats, heroStats.value长度:', heroStats.value.length)
  console.log('🔍 搜索查询:', heroSearchQuery.value)
  
  if (!heroSearchQuery.value) return heroStats.value
  
  const query = heroSearchQuery.value.toLowerCase()
  const filtered = heroStats.value.filter(hero => 
    hero.hero_cn_name && hero.hero_cn_name.toLowerCase().includes(query)
  )
  
  console.log('🔍 过滤后的英雄数量:', filtered.length)
  return filtered
})

const sortedHeroStats = computed(() => {
  const stats = [...filteredHeroStats.value]
  console.log('🔍 排序前的英雄数量:', stats.length)
  
  stats.sort((a, b) => {
    let aValue = a[heroSortColumn.value as keyof HeroStatData]
    let bValue = b[heroSortColumn.value as keyof HeroStatData]
    
    // 处理字符串排序
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return heroSortOrder.value === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }
    
    // 处理数字排序
    if (heroSortOrder.value === 'asc') {
      return (aValue as number || 0) - (bValue as number || 0)
    } else {
      return (bValue as number || 0) - (aValue as number || 0)
    }
  })
  
  console.log('🔍 排序后的英雄数量:', stats.length)
  return stats
})

// === 英雄排序功能 ===
const sortHeroBy = (column: string) => {
  if (heroSortColumn.value === column) {
    heroSortOrder.value = heroSortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    heroSortColumn.value = column
    // 设置默认排序方向
    if (column === 'avg_place') {
      heroSortOrder.value = 'asc' // 平均排名越小越好
    } else {
      heroSortOrder.value = 'desc' // 其他指标越大越好
    }
  }
}

// === 数据格式化函数 ===
const formatStat = (value: number, type: 'playRate' | 'avgPlace' | 'top1Rate' | 'top4Rate'): string => {
  if (!value && value !== 0) return '--'
  
  if (type === 'avgPlace') {
    return value.toFixed(2)
  } else {
    return value.toFixed(1)
  }
}

const getStatColor = (value: number, type: 'playRate' | 'avgPlace' | 'top1Rate' | 'top4Rate'): string => {
  // 取消颜色显示，统一返回默认文字颜色
  return 'rgba(255, 255, 255, 0.9)'
}

const getNoDataMessage = (): string => {
  if (isLoading.value) {
    return '正在加载英雄统计数据...'
  }

  if (heroSearchQuery.value.trim()) {
    return '未找到匹配的英雄'
  }

  if (heroStats.value.length === 0) {
    return '暂无适用英雄数据'
  }

  return '无数据'
}

// === 事件处理 ===
const handleHeroClick = (heroStat: HeroStatData) => {
  console.log('点击英雄:', heroStat.hero_cn_name)
  emit('heroClick', heroStat.hero_cn_name)
}

// === 数据加载 ===
const loadItemDetail = async () => {
  try {
    isLoading.value = true
    console.log('🔍 开始加载装备详情:', props.itemName)
    console.log('🔍 当前heroStats长度:', heroStats.value.length)
    
    // 并行加载装备详情和英雄统计
    const [itemResults, heroResults] = await Promise.all([
      invoke('get_item_detail', { itemName: props.itemName }),
      invoke('get_equipment_hero_stats', { itemName: props.itemName })
    ])
    
    console.log('🛡️ 装备详情加载结果:', itemResults)
    console.log('👥 英雄统计加载结果:', heroResults)
    
    // 处理装备详情数据
    if (itemResults && typeof itemResults === 'object' && 'data' in itemResults) {
      if ('error' in itemResults && itemResults.error) {
        console.error('❌ 装备详情查询错误:', itemResults.error)
        itemDetail.value = null
      } else if (Array.isArray(itemResults.data) && itemResults.data.length > 0) {
        itemDetail.value = itemResults.data[0]
        console.log('✅ 装备详情加载成功')
      } else {
        console.warn('⚠️ 装备详情数据为空')
        itemDetail.value = null
      }
    } else {
      console.warn('⚠️ 装备详情数据格式不正确:', itemResults)
      itemDetail.value = null
    }
    
    // 处理英雄统计数据
    console.log('🔍 英雄统计原始数据结构:', heroResults)
    if (heroResults && typeof heroResults === 'object' && 'data' in heroResults) {
      if ('error' in heroResults && heroResults.error) {
        console.error('❌ 英雄统计查询错误:', heroResults.error)
        heroStats.value = []
      } else if (Array.isArray(heroResults.data)) {
        heroStats.value = heroResults.data
        console.log(`✅ 成功加载 ${heroStats.value.length} 个英雄统计`)
        console.log('📊 英雄统计数据示例:', heroStats.value.slice(0, 2))
      } else {
        console.warn('⚠️ 英雄统计数据格式不正确:', heroResults.data)
        heroStats.value = []
      }
    } else if (Array.isArray(heroResults)) {
      heroStats.value = heroResults
      console.log(`✅ 成功加载 ${heroStats.value.length} 个英雄统计`)
      console.log('📊 英雄统计数据示例:', heroStats.value.slice(0, 2))
    } else {
      console.warn('⚠️ 英雄统计数据格式不正确:', heroResults)
      heroStats.value = []
    }
    
  } catch (error) {
    console.error('❌ 加载装备详情失败:', error)
    itemDetail.value = null
    heroStats.value = []
  } finally {
    isLoading.value = false
    console.log('🔍 数据加载完成，最终heroStats长度:', heroStats.value.length)
  }
}

// === 监听装备名称变化 ===
watch(() => props.itemName, (newItemName) => {
  console.log('🔍 装备名称变化:', newItemName)
  if (newItemName) {
    loadItemDetail()
  }
}, { immediate: true })

// === 组件挂载时加载数据 ===
onMounted(() => {
  if (props.itemName) {
    loadItemDetail()
  }
})
</script>

<style scoped>
/* === 装备详情页面 === */
.item-detail-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  gap: 1rem;
  overflow: hidden;
}



/* === 加载状态 === */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 错误状态 === */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: rgba(255, 255, 255, 0.8);
  gap: 1rem;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.error-state p {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  margin-bottom: 1rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* === 装备详情内容 === */
.item-detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

/* === 装备基本信息区域 === */
.item-info-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: stretch;
  gap: 1rem;
  height: 120px;
  flex-shrink: 0;
}

.item-icon-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
}

.item-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.5rem 0;
}

.item-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.item-stats-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 0.75rem 1.5rem;
  align-items: center;
}

.stat-inline {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
  line-height: 1.3;
}

.stat-value {
  color: white;
  font-weight: 600;
}

.item-categories {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.categories-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.category-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.category-tag {
  padding: 0.25rem 0.75rem;
  background: rgba(100, 200, 255, 0.2);
  border: 1px solid rgba(100, 200, 255, 0.4);
  border-radius: 16px;
  color: rgba(100, 200, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
}

/* === 英雄统计区域 === */
.hero-stats-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  overflow: hidden;
}

.hero-stats-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.hero-search-input {
  width: 200px;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 13px;
  outline: none;
  transition: all 0.2s ease;
}

.hero-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.hero-search-input:focus {
  border-color: rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

/* === 英雄统计容器 === */
.hero-stats-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* === 英雄统计表头 === */
.hero-stats-header {
  display: grid;
  grid-template-columns: 50px 1.5fr 70px 75px 70px 70px;
  gap: 0.3rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
}

.header-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.header-item:hover {
  color: white;
}

.header-hero-icon {
  justify-content: center;
  position: relative;
}

.hero-header-text {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  display: flex;
  align-items: center;
}

.hero-header-text:hover {
  color: white;
}

.header-hero-name {
  justify-content: flex-start;
}

.header-hero-play-rate,
.header-hero-avg-place,
.header-hero-top4-rate,
.header-hero-top1-rate {
  justify-content: center;
  text-align: center;
}

.sort-indicator {
  margin-left: 0.5rem;
  font-size: 12px;
  color: rgba(100, 200, 255, 0.8);
}

/* === 英雄统计内容 === */
.hero-stats-content {
  flex: 1;
  overflow-y: auto;
}

.hero-stats-content::-webkit-scrollbar {
  width: 6px;
}

.hero-stats-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.hero-stats-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.hero-stats-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* === 英雄行 === */
.hero-row {
  display: grid;
  grid-template-columns: 50px 1.5fr 70px 75px 70px 70px;
  gap: 0.3rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
}

.hero-row:hover {
  background: rgba(255, 255, 255, 0.08);
}

.hero-row:last-child {
  border-bottom: none;
}

/* === 英雄单元格 === */
.hero-cell {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
}

.hero-icon-cell {
  justify-content: center;
}

.hero-name-cell {
  font-weight: 500;
  color: white;
}

.hero-play-rate-cell,
.hero-avg-place-cell,
.hero-top4-rate-cell,
.hero-top1-rate-cell {
  justify-content: center;
  text-align: center;
  font-weight: 500;
}

/* === 无英雄数据提示 === */
.no-hero-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: rgba(255, 255, 255, 0.6);
  gap: 0.75rem;
  padding: 2rem;
}

.no-data-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.no-hero-data p {
  font-size: 14px;
  text-align: center;
  margin: 0;
  line-height: 1.4;
}

.no-data-hint {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.4) !important;
  max-width: 300px;
}
</style>