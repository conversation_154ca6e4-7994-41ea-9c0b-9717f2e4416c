<template>
  <div class="p-4 space-y-4">
    <h2 class="text-xl font-bold">状态管理示例</h2>
    
    <!-- 应用状态示例 -->
    <div class="border p-4 rounded">
      <h3 class="text-lg font-semibold mb-2">应用状态</h3>
      <div class="space-y-2">
        <p>当前页面: {{ currentPageName }}</p>
        <p>是否最小化: {{ isMinimized ? '是' : '否' }}</p>
        <p>历史记录数量: {{ historyStack.length }}</p>
        <p>是否详情页: {{ isDetailPage ? '是' : '否' }}</p>
        
        <div class="space-x-2">
          <button 
            @click="switchToHeroList" 
            class="px-3 py-1 bg-blue-500 text-white rounded"
          >
            切换到英雄列表
          </button>
          <button 
            @click="navigateToHeroDetail" 
            class="px-3 py-1 bg-green-500 text-white rounded"
          >
            导航到英雄详情
          </button>
          <button 
            @click="goBack" 
            :disabled="!hasHistory"
            class="px-3 py-1 bg-gray-500 text-white rounded disabled:opacity-50"
          >
            返回
          </button>
          <button 
            @click="toggleWindow" 
            class="px-3 py-1 bg-purple-500 text-white rounded"
          >
            切换窗口状态
          </button>
        </div>
      </div>
    </div>

    <!-- 数据缓存示例 -->
    <div class="border p-4 rounded">
      <h3 class="text-lg font-semibold mb-2">数据缓存状态</h3>
      <div class="space-y-2">
        <p>数据是否就绪: {{ isDataReady ? '是' : '否' }}</p>
        <p>是否正在预加载: {{ isPreloading ? '是' : '否' }}</p>
        <p>缓存统计:</p>
        <ul class="ml-4 space-y-1">
          <li>英雄数量: {{ cacheStats.heroCount }}</li>
          <li>装备数量: {{ cacheStats.itemCount }}</li>
          <li>羁绊数量: {{ cacheStats.traitCount }}</li>
          <li>查询缓存数量: {{ cacheStats.queryCacheSize }}</li>
        </ul>
        
        <div class="space-x-2">
          <button 
            @click="preloadData" 
            :disabled="isPreloading"
            class="px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            预加载数据
          </button>
          <button 
            @click="testHeroQuery" 
            class="px-3 py-1 bg-green-500 text-white rounded"
          >
            测试英雄查询
          </button>
          <button 
            @click="clearCache" 
            class="px-3 py-1 bg-red-500 text-white rounded"
          >
            清除缓存
          </button>
        </div>
      </div>
    </div>

    <!-- 查询结果示例 -->
    <div class="border p-4 rounded" v-if="queryResult">
      <h3 class="text-lg font-semibold mb-2">查询结果</h3>
      <pre class="bg-gray-100 p-2 rounded text-sm overflow-auto">{{ JSON.stringify(queryResult, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAppStore } from '@/stores/app';
import { useDataStore } from '@/stores/data';
import { useData } from '@/composables/useData';
import { PAGE_CONSTANTS } from '@/types';

// 使用状态管理
const appStore = useAppStore();
const dataStore = useDataStore();
const { preloadData: preloadDataComposable, getHeroInfo } = useData();

// 应用状态
const currentPage = computed(() => appStore.currentPage);
const isMinimized = computed(() => appStore.isMinimized);
const historyStack = computed(() => appStore.historyStack);
const hasHistory = computed(() => appStore.hasHistory);
const isDetailPage = computed(() => appStore.isDetailPage);

// 数据状态
const isDataReady = computed(() => dataStore.isDataReady);
const isPreloading = computed(() => dataStore.isPreloading);
const cacheStats = computed(() => dataStore.cacheStats);

// 查询结果
const queryResult = ref<any>(null);

// 计算当前页面名称
const currentPageName = computed(() => {
  const pageNames: Record<number, string> = {
    [PAGE_CONSTANTS.PAGE_COMP_LIST]: '阵容列表',
    [PAGE_CONSTANTS.PAGE_HERO_LIST]: '英雄列表',
    [PAGE_CONSTANTS.PAGE_ITEM_LIST]: '装备列表',
    [PAGE_CONSTANTS.PAGE_HEX_LIST]: '海克斯列表',
    [PAGE_CONSTANTS.PAGE_COMP_DETAIL]: '阵容详情',
    [PAGE_CONSTANTS.PAGE_HERO_DETAIL]: '英雄详情',
    [PAGE_CONSTANTS.PAGE_ITEM_DETAIL]: '装备详情',
  };
  return pageNames[currentPage.value] || '未知页面';
});

// 方法
const switchToHeroList = () => {
  appStore.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
};

const navigateToHeroDetail = () => {
  appStore.navigateToDetail(PAGE_CONSTANTS.PAGE_HERO_DETAIL, 'test_hero');
};

const goBack = () => {
  appStore.goBack();
};

const toggleWindow = () => {
  appStore.toggleWindow();
};

const preloadData = async () => {
  try {
    await preloadDataComposable();
    console.log('数据预加载完成');
  } catch (error) {
    console.error('数据预加载失败:', error);
  }
};

const testHeroQuery = () => {
  // 测试从缓存获取英雄信息
  const heroInfo = getHeroInfo('测试英雄');
  queryResult.value = heroInfo || { message: '未找到英雄信息，请先预加载数据' };
};

const clearCache = () => {
  dataStore.clearQueryCache();
  queryResult.value = null;
  console.log('缓存已清除');
};
</script>