import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { invoke } from '@tauri-apps/api/core';
import type { 
  GlobalDataCache, 
  QueryResult, 
  HeroInfo, 
  ItemInfo, 
  TraitInfo 
} from '@/types';

/**
 * 数据缓存状态管理
 * 复刻数据库操作.py中的缓存和预加载功能
 */
export const useDataStore = defineStore('data', () => {
  // 全局数据缓存
  const globalHeroInfoMap = ref<Record<string, HeroInfo>>({});
  const globalTraitIconMap = ref<Record<string, string>>({});
  const globalItemInfoMap = ref<Record<string, ItemInfo>>({});
  
  // 查询结果缓存
  const queryCache = ref<Record<string, any>>({});
  
  // 数据加载状态
  const isGlobalDataLoaded = ref<boolean>(false);
  const isPreloading = ref<boolean>(false);
  
  // 缓存统计
  const cacheStats = ref({
    heroCount: 0,
    itemCount: 0,
    traitCount: 0,
    queryCacheSize: 0
  });

  // 计算属性
  const isDataReady = computed(() => isGlobalDataLoaded.value);
  const cacheSize = computed(() => {
    return cacheStats.value.heroCount + 
           cacheStats.value.itemCount + 
           cacheStats.value.traitCount;
  });

  /**
   * 预加载全局数据
   * 复刻数据库操作.py中的preload_global_data方法
   */
  const preloadGlobalData = async (): Promise<void> => {
    if (isGlobalDataLoaded.value || isPreloading.value) {
      console.log('全局数据已加载或正在加载中');
      return;
    }

    console.log('开始预加载全局数据...');
    isPreloading.value = true;

    try {
      // 并行加载所有全局数据
      const [heroResult, itemResult, traitResult] = await Promise.all([
        loadHeroInfoMap(),
        loadItemInfoMap(),
        loadTraitIconMap()
      ]);

      if (heroResult && itemResult && traitResult) {
        isGlobalDataLoaded.value = true;
        console.log('全局数据预加载完成');
        updateCacheStats();
      } else {
        throw new Error('部分数据加载失败');
      }
    } catch (error) {
      console.error('预加载全局数据失败:', error);
      throw error;
    } finally {
      isPreloading.value = false;
    }
  };

  /**
   * 加载英雄信息映射
   */
  const loadHeroInfoMap = async (): Promise<boolean> => {
    try {
      console.log('加载英雄信息映射...');
      const result: QueryResult = await invoke('get_hero_list');
      
      if (result.error) {
        throw new Error(result.error);
      }

      // 构建英雄信息映射
      const heroMap: Record<string, HeroInfo> = {};
      result.data.forEach((hero: any) => {
        if (hero.cn_name) {
          heroMap[hero.cn_name] = hero;
        }
        if (hero.en_name) {
          heroMap[hero.en_name] = hero;
        }
      });

      globalHeroInfoMap.value = heroMap;
      console.log(`英雄信息映射加载完成，共${Object.keys(heroMap).length}个条目`);
      return true;
    } catch (error) {
      console.error('加载英雄信息映射失败:', error);
      return false;
    }
  };

  /**
   * 加载装备信息映射
   */
  const loadItemInfoMap = async (): Promise<boolean> => {
    try {
      console.log('加载装备信息映射...');
      const result: QueryResult = await invoke('get_item_list');
      
      if (result.error) {
        throw new Error(result.error);
      }

      // 构建装备信息映射
      const itemMap: Record<string, ItemInfo> = {};
      result.data.forEach((item: any) => {
        if (item.name) {
          itemMap[item.name] = item;
        }
        if (item.en_name) {
          itemMap[item.en_name] = item;
        }
      });

      globalItemInfoMap.value = itemMap;
      console.log(`装备信息映射加载完成，共${Object.keys(itemMap).length}个条目`);
      return true;
    } catch (error) {
      console.error('加载装备信息映射失败:', error);
      return false;
    }
  };

  /**
   * 加载羁绊图标映射
   */
  const loadTraitIconMap = async (): Promise<boolean> => {
    try {
      console.log('加载羁绊图标映射...');
      // 这里可能需要调用特定的羁绊数据查询命令
      // 暂时使用一个通用的查询方法
      const result: QueryResult = await invoke('execute_query', {
        sql: 'SELECT name, icon_path FROM traits',
        params: []
      });
      
      if (result.error) {
        throw new Error(result.error);
      }

      // 构建羁绊图标映射
      const traitMap: Record<string, string> = {};
      result.data.forEach((trait: any) => {
        if (trait.name && trait.icon_path) {
          traitMap[trait.name] = trait.icon_path;
        }
      });

      globalTraitIconMap.value = traitMap;
      console.log(`羁绊图标映射加载完成，共${Object.keys(traitMap).length}个条目`);
      return true;
    } catch (error) {
      console.error('加载羁绊图标映射失败:', error);
      return false;
    }
  };

  /**
   * 获取缓存的查询结果
   * 复刻数据库操作.py中的查询缓存逻辑
   * @param cacheKey 缓存键
   * @returns 缓存的结果或null
   */
  const getCachedQuery = (cacheKey: string): any | null => {
    const cached = queryCache.value[cacheKey];
    if (cached) {
      console.log(`命中查询缓存: ${cacheKey}`);
      return cached;
    }
    return null;
  };

  /**
   * 设置查询缓存
   * @param cacheKey 缓存键
   * @param data 要缓存的数据
   */
  const setCachedQuery = (cacheKey: string, data: any): void => {
    queryCache.value[cacheKey] = data;
    updateCacheStats();
    console.log(`设置查询缓存: ${cacheKey}`);
  };

  /**
   * 清除查询缓存
   * @param cacheKey 可选的特定缓存键，不提供则清除所有
   */
  const clearQueryCache = (cacheKey?: string): void => {
    if (cacheKey) {
      delete queryCache.value[cacheKey];
      console.log(`清除查询缓存: ${cacheKey}`);
    } else {
      queryCache.value = {};
      console.log('清除所有查询缓存');
    }
    updateCacheStats();
  };

  /**
   * 获取英雄信息
   * @param heroName 英雄名称（中文或英文）
   * @returns 英雄信息或null
   */
  const getHeroInfo = (heroName: string): HeroInfo | null => {
    return globalHeroInfoMap.value[heroName] || null;
  };

  /**
   * 获取装备信息
   * @param itemName 装备名称（中文或英文）
   * @returns 装备信息或null
   */
  const getItemInfo = (itemName: string): ItemInfo | null => {
    return globalItemInfoMap.value[itemName] || null;
  };

  /**
   * 获取羁绊图标路径
   * @param traitName 羁绊名称
   * @returns 图标路径或null
   */
  const getTraitIcon = (traitName: string): string | null => {
    return globalTraitIconMap.value[traitName] || null;
  };

  /**
   * 更新缓存统计信息
   */
  const updateCacheStats = (): void => {
    cacheStats.value = {
      heroCount: Object.keys(globalHeroInfoMap.value).length,
      itemCount: Object.keys(globalItemInfoMap.value).length,
      traitCount: Object.keys(globalTraitIconMap.value).length,
      queryCacheSize: Object.keys(queryCache.value).length
    };
  };

  /**
   * 执行带缓存的查询
   * @param cacheKey 缓存键
   * @param queryFn 查询函数
   * @returns 查询结果
   */
  const executeWithCache = async <T>(
    cacheKey: string, 
    queryFn: () => Promise<T>
  ): Promise<T> => {
    // 检查缓存
    const cached = getCachedQuery(cacheKey);
    if (cached) {
      return cached;
    }

    // 执行查询
    try {
      const result = await queryFn();
      setCachedQuery(cacheKey, result);
      return result;
    } catch (error) {
      console.error(`查询失败 [${cacheKey}]:`, error);
      throw error;
    }
  };

  /**
   * 重置所有数据缓存
   */
  const resetCache = (): void => {
    globalHeroInfoMap.value = {};
    globalTraitIconMap.value = {};
    globalItemInfoMap.value = {};
    queryCache.value = {};
    isGlobalDataLoaded.value = false;
    isPreloading.value = false;
    updateCacheStats();
    console.log('重置所有数据缓存');
  };

  /**
   * 获取缓存状态信息
   */
  const getCacheStatus = () => {
    return {
      isLoaded: isGlobalDataLoaded.value,
      isPreloading: isPreloading.value,
      stats: cacheStats.value,
      totalSize: cacheSize.value
    };
  };

  return {
    // 状态
    globalHeroInfoMap,
    globalTraitIconMap,
    globalItemInfoMap,
    queryCache,
    isGlobalDataLoaded,
    isPreloading,
    cacheStats,
    
    // 计算属性
    isDataReady,
    cacheSize,
    
    // 方法
    preloadGlobalData,
    getCachedQuery,
    setCachedQuery,
    clearQueryCache,
    getHeroInfo,
    getItemInfo,
    getTraitIcon,
    executeWithCache,
    resetCache,
    getCacheStatus,
    updateCacheStats
  };
});