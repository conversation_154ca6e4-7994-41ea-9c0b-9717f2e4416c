<template>
  <div
    :class="cn(
      'tier-label flex items-center justify-center font-bold text-white border-2 border-white/20',
      tierClasses[tier],
      sizeClasses[size],
      props.class
    )"
    :style="tierStyle"
  >
    {{ tier }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'

type TierType = 'S' | 'A' | 'B' | 'C' | 'D'
type SizeType = 'sm' | 'md' | 'lg'

interface Props {
  tier: TierType
  size?: SizeType
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  class: ''
})

// 评级颜色映射 - 基于UI风格指导
const tierClasses: Record<TierType, string> = {
  'S': 'bg-tier-s', // #FF5A5F - 红色
  'A': 'bg-tier-a', // #FFA756 - 橙色
  'B': 'bg-tier-b', // #FFD700 - 金色
  'C': 'bg-tier-c', // #F3FF59 - 黄色
  'D': 'bg-tier-d', // #8AFF40 - 绿色
}

// 备用内联样式，确保颜色正确显示
const tierColors: Record<TierType, string> = {
  'S': '#FF5A5F',
  'A': '#FFA756',
  'B': '#FFD700',
  'C': '#F3FF59',
  'D': '#8AFF40',
}

// 计算内联样式
const tierStyle = computed(() => ({
  backgroundColor: tierColors[props.tier],
  color: '#1A202C' // text-dark
}))

// 尺寸映射
const sizeClasses: Record<SizeType, string> = {
  'sm': 'w-8 h-8 text-xs rounded',
  'md': 'w-15 h-15 text-sm rounded-lg', // 60px × 60px
  'lg': 'w-20 h-20 text-lg rounded-xl',
}
</script>

<style scoped>
.tier-label {
  /* 确保正方形 */
  aspect-ratio: 1;
  
  /* 文字居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* 防止文字选择 */
  user-select: none;
  
  /* 平滑过渡 */
  transition: all 0.2s ease-in-out;
}

.tier-label:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
}
</style>
