# TFT助手完整应用Glassmorphism重构需求文档

## 介绍

本文档定义了将TFT常驻助手**完整应用**重构为现代化Glassmorphism（玻璃拟态）风格界面的需求。这是一个**完全重写**的项目，采用**框架与内容分离**的架构：App.vue负责应用框架（控制栏、导航栏、容器），各个页面采用独立的Vue组件（CompListView、HeroListView、ItemListView、HexListView等）。采用**由外到内、逐层构建**的渐进式开发方式，先建立完整的应用框架，再逐步完善各页面组件。

## 需求

### 需求1：完全重写应用架构并建立基础容器

**用户故事：** 作为开发者，我需要完全重写App.vue和整个应用架构，删除所有现有UI组件，建立全新的Glassmorphism应用框架。

#### 验收标准

1. WHEN 重构开始时 THEN 应删除所有现有UI组件（MainWindow.vue、DemoContent.vue等）
2. WHEN App.vue重写时 THEN 应在单一文件中包含完整的应用逻辑和UI
3. WHEN 页面加载时 THEN 应显示完整的Glassmorphism应用界面，包含控制栏、导航栏、内容区域
4. WHEN 查看背景时 THEN 应使用云顶之弈风格的紫蓝色渐变背景
5. WHEN 应用渲染时 THEN 应完全填充窗口，无边距无圆角

### 需求2：构建完整应用窗口框架

**用户故事：** 作为开发者，我需要在App.vue中构建完整的应用窗口框架，包含控制栏、导航栏、内容区域的完整结构。

#### 验收标准

1. WHEN 应用启动时 THEN 应显示完整填充窗口的应用界面
2. WHEN 查看界面时 THEN 应具有Glassmorphism效果（半透明、模糊背景）
3. WHEN 窗口渲染时 THEN 应保持初始尺寸（870x1050）且完全填充
4. WHEN 界面显示时 THEN 应包含控制栏、导航栏、内容区域的完整层级结构

### 需求3：添加顶部控制栏

**用户故事：** 作为开发者，我需要在窗口顶部添加控制栏，以便放置窗口控制按钮。

#### 验收标准

1. WHEN 窗口显示时 THEN 顶部应有一个控制栏区域
2. WHEN 查看控制栏时 THEN 应与窗口整体风格保持一致
3. WHEN 控制栏渲染时 THEN 高度应适中，为按钮预留空间

### 需求4：实现展开收起功能

**用户故事：** 作为用户，我需要能够控制窗口的展开和收起，以便调整界面显示状态。

#### 验收标准

1. WHEN 点击展开/收起按钮时 THEN 窗口应在展开和收起状态间切换
2. WHEN 窗口收起时 THEN 只显示控制栏
3. WHEN 窗口展开时 THEN 显示完整内容区域

### 需求5：添加关闭按钮

**用户故事：** 作为用户，我需要一个关闭按钮来退出应用程序。

#### 验收标准

1. WHEN 控制栏显示时 THEN 应有一个关闭按钮
2. WHEN 点击关闭按钮时 THEN 应用程序应关闭
3. WHEN 鼠标悬停时 THEN 按钮应有悬停效果

### 需求6：实现窗口拖拽功能

**用户故事：** 作为用户，我需要能够拖拽窗口来改变其位置。

#### 验收标准

1. WHEN 在控制栏按下鼠标时 THEN 窗口应进入拖拽模式
2. WHEN 拖拽时 THEN 窗口应跟随鼠标移动
3. WHEN 释放鼠标时 THEN 窗口应停在新位置

### 需求7：添加导航栏区域

**用户故事：** 作为开发者，我需要在窗口中添加导航栏区域，以便放置页面导航元素。

#### 验收标准

1. WHEN 窗口展开时 THEN 应显示导航栏区域
2. WHEN 查看导航栏时 THEN 应与整体Glassmorphism风格一致
3. WHEN 导航栏渲染时 THEN 应为导航按钮预留适当空间

### 需求8：添加"英雄"导航标识

**用户故事：** 作为用户，我需要在导航栏中看到"英雄"标识，以便知道当前页面功能。

#### 验收标准

1. WHEN 导航栏显示时 THEN 应显示"英雄"文字标识
2. WHEN 查看标识时 THEN 应采用Glassmorphism按钮样式
3. WHEN 标识激活时 THEN 应有明显的激活状态视觉效果

### 需求9：添加内容区域容器

**用户故事：** 作为开发者，我需要在导航栏下方添加内容区域容器，以便为后续的英雄列表内容提供空间。

#### 验收标准

1. WHEN 窗口展开时 THEN 导航栏下方应显示内容区域容器
2. WHEN 查看容器时 THEN 应具有Glassmorphism卡片样式
3. WHEN 容器渲染时 THEN 应占据剩余的可用空间

### 需求10：添加搜索框区域

**用户故事：** 作为用户，我需要在内容区域顶部有一个搜索框，以便搜索英雄。

#### 验收标准

1. WHEN 内容区域显示时 THEN 顶部应有搜索框区域
2. WHEN 查看搜索框时 THEN 应具有Glassmorphism输入框样式
3. WHEN 搜索框渲染时 THEN 应有"搜索英雄名称..."占位符

### 需求11：添加费用筛选区域

**用户故事：** 作为用户，我需要在搜索框下方有费用筛选按钮，以便按英雄费用筛选。

#### 验收标准

1. WHEN 搜索框下方显示时 THEN 应有费用筛选按钮组
2. WHEN 查看筛选按钮时 THEN 应显示"全部"、"1费"、"2费"、"3费"、"4费"、"5费"
3. WHEN 按钮渲染时 THEN 应采用Glassmorphism按钮样式

### 需求12：添加英雄列表容器

**用户故事：** 作为开发者，我需要在筛选区域下方添加英雄列表容器，以便为英雄卡片提供展示空间。

#### 验收标准

1. WHEN 筛选区域下方显示时 THEN 应有英雄列表容器
2. WHEN 查看容器时 THEN 应具有滚动能力和网格布局
3. WHEN 容器渲染时 THEN 应为英雄卡片预留适当的空间

### 需求13：添加英雄卡片组件

**用户故事：** 作为用户，我需要看到英雄卡片，以便查看英雄信息。

#### 验收标准

1. WHEN 英雄列表显示时 THEN 应显示英雄卡片
2. WHEN 查看卡片时 THEN 应包含英雄头像、名称、费用标识
3. WHEN 卡片渲染时 THEN 应采用Glassmorphism卡片样式

### 需求14：添加费用分组容器

**用户故事：** 作为开发者，我需要按费用对英雄进行分组显示，以便用户能够清晰地看到不同费用的英雄。

#### 验收标准

1. WHEN 英雄列表显示时 THEN 应按1费、2费、3费、4费、5费分组
2. WHEN 查看分组时 THEN 每个分组应有独立的容器
3. WHEN 分组渲染时 THEN 应采用水平布局（左侧费用标签 + 右侧英雄网格）

### 需求15：添加费用标签组件

**用户故事：** 作为用户，我需要看到费用标签，以便快速识别不同费用的英雄分组。

#### 验收标准

1. WHEN 费用分组显示时 THEN 左侧应有垂直的费用标签
2. WHEN 查看费用标签时 THEN 应显示对应的费用数字（1、2、3、4、5）
3. WHEN 标签渲染时 THEN 应使用对应费用的颜色（灰、绿、蓝、紫、金）

### 需求16：添加英雄网格布局

**用户故事：** 作为开发者，我需要在费用分组内创建网格布局，以便整齐地排列英雄卡片。

#### 验收标准

1. WHEN 费用分组内容显示时 THEN 应使用网格布局排列英雄
2. WHEN 查看网格时 THEN 每行应显示5个英雄卡片
3. WHEN 网格渲染时 THEN 卡片间应有适当的间距

### 需求17：创建英雄卡片基础结构

**用户故事：** 作为开发者，我需要创建英雄卡片的基础结构，以便为英雄信息提供容器。

#### 验收标准

1. WHEN 网格中显示英雄时 THEN 每个英雄应有独立的卡片容器
2. WHEN 查看卡片时 THEN 应采用Glassmorphism卡片样式
3. WHEN 卡片渲染时 THEN 应有固定的宽度和自适应的高度

### 需求18：添加英雄头像区域

**用户故事：** 作为用户，我需要看到英雄头像，以便快速识别英雄。

#### 验收标准

1. WHEN 英雄卡片显示时 THEN 顶部应显示英雄头像
2. WHEN 查看头像时 THEN 应为60x60像素的圆角图片
3. WHEN 头像渲染时 THEN 应居中显示且有适当的占位符

### 需求19：添加英雄名称显示

**用户故事：** 作为用户，我需要看到英雄名称，以便确认英雄身份。

#### 验收标准

1. WHEN 英雄头像下方显示时 THEN 应显示英雄的中文名称
2. WHEN 查看名称时 THEN 应使用白色文字且居中对齐
3. WHEN 名称过长时 THEN 应支持自动换行

### 需求20：添加统计信息显示

**用户故事：** 作为用户，我需要看到英雄的统计信息，以便了解英雄的表现数据。

#### 验收标准

1. WHEN 英雄名称下方显示时 THEN 应显示"出场率: X.XX"
2. WHEN 出场率下方显示时 THEN 应显示"均名: X.XX"
3. WHEN 统计信息渲染时 THEN 应使用较小的灰色文字

### 需求21：添加费用标识徽章

**用户故事：** 作为用户，我需要在英雄头像上看到费用标识，以便快速识别英雄费用。

#### 验收标准

1. WHEN 英雄头像显示时 THEN 右上角应有费用标识徽章
2. WHEN 查看徽章时 THEN 应显示对应的费用数字
3. WHEN 徽章渲染时 THEN 应使用对应费用的背景颜色

### 需求22：实现卡片交互效果

**用户故事：** 作为用户，我需要英雄卡片有交互反馈，以便知道可以点击查看详情。

#### 验收标准

1. WHEN 鼠标悬停在卡片上时 THEN 应有悬停效果（轻微放大或阴影变化）
2. WHEN 点击卡片时 THEN 应有点击反馈效果
3. WHEN 交互时 THEN 鼠标指针应变为手型

### 需求23：填充真实英雄数据

**用户故事：** 作为用户，我需要看到真实的英雄数据，以便获取有用的信息。

#### 验收标准

1. WHEN 页面加载时 THEN 应从数据库加载真实英雄数据
2. WHEN 英雄卡片显示时 THEN 应显示正确的头像、名称、统计信息
3. WHEN 数据加载时 THEN 应有适当的加载状态指示

### 需求24：实现搜索和筛选功能

**用户故事：** 作为用户，我需要搜索和筛选功能正常工作，以便快速找到目标英雄。

#### 验收标准

1. WHEN 在搜索框输入时 THEN 应实时筛选显示匹配的英雄
2. WHEN 选择费用筛选时 THEN 应只显示对应费用的英雄
3. WHEN 筛选条件改变时 THEN 界面应平滑更新显示结果