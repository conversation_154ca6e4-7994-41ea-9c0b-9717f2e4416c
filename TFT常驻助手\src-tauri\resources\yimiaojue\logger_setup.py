import os
import sys
import logging
from logging.handlers import RotatingFileHandler
import config

# --- 自定义过滤器 ---
class SpecificNameFilter(logging.Filter):
    """只允许特定名称的日志记录通过。"""
    def __init__(self, name_to_allow):
        super().__init__()
        self.name_to_allow = name_to_allow

    def filter(self, record):
        return record.name == self.name_to_allow

class NoSpecificNameFilter(logging.Filter):
    """阻止特定名称的日志记录通过。"""
    def __init__(self, name_to_block):
        super().__init__()
        self.name_to_block = name_to_block

    def filter(self, record):
        return not record.name.startswith(self.name_to_block)

# --- 日志去重过滤器 ---
class DuplicateFilter(logging.Filter):
    """过滤重复的日志消息，减少日志噪音"""
    
    def __init__(self, max_duplicates=3, time_window=10):
        super().__init__()
        self.max_duplicates = max_duplicates  # 允许的最大重复次数
        self.time_window = time_window  # 时间窗口（秒）
        self.message_counts = {}  # 消息计数器
        self.last_logged = {}  # 最后记录时间
    
    def filter(self, record):
        import time
        import hashlib
        
        # 创建消息的唯一标识
        message_key = f"{record.levelname}:{record.funcName}:{record.getMessage()}"
        message_hash = hashlib.md5(message_key.encode()).hexdigest()
        
        current_time = time.time()
        
        # 检查是否在时间窗口内
        if message_hash in self.last_logged:
            time_diff = current_time - self.last_logged[message_hash]
            
            # 如果超过时间窗口，重置计数器
            if time_diff > self.time_window:
                self.message_counts[message_hash] = 0
                self.last_logged[message_hash] = current_time
                return True
            
            # 在时间窗口内，检查重复次数
            count = self.message_counts.get(message_hash, 0)
            if count >= self.max_duplicates:
                return False  # 过滤掉重复消息
            
            self.message_counts[message_hash] = count + 1
        else:
            # 首次出现的消息
            self.message_counts[message_hash] = 1
            self.last_logged[message_hash] = current_time
        
        return True

# --- 特定消息过滤器 ---
class SpecificMessageFilter(logging.Filter):
    """过滤特定的重复消息模式"""
    
    def __init__(self):
        super().__init__()
        self.last_timer_extend = 0
        self.last_equip_count = None
        self.last_equip_count_time = 0
        self.last_performance_log = {}
        self.timer_extend_interval = 3.0  # 计时器延长日志最小间隔（秒）
        self.equip_count_interval = 2.0   # 装备计数日志最小间隔（秒）
        self.performance_interval = 1.0   # 性能日志最小间隔（秒）
    
    def filter(self, record):
        import time
        current_time = time.time()
        message = record.getMessage()
        
        # 过滤重复的计时器延长消息（除了首次检测）
        if "检测到有效结果，延长计时器" in message and "首次检测" not in message:
            if current_time - self.last_timer_extend < self.timer_extend_interval:
                return False
            self.last_timer_extend = current_time
        
        # 过滤重复的装备计数结果（只保留变化的结果）
        if "[Equip Counter] 检测到" in message and "个装备" in message:
            try:
                # 提取数量信息
                count_part = message.split("检测到 ")[1].split(" 个装备")[0]
                current_count = int(count_part)
                
                # 如果数量相同且在时间间隔内，过滤掉
                if (self.last_equip_count == current_count and 
                    current_time - self.last_equip_count_time < self.equip_count_interval):
                    return False
                
                self.last_equip_count = current_count
                self.last_equip_count_time = current_time
            except (ValueError, IndexError):
                pass  # 解析失败，允许通过
        
        # 过滤重复的性能日志
        if "Perf]" in message and ("Total:" in message or "耗时:" in message):
            perf_type = "unknown"
            if "[Hex Perf]" in message:
                perf_type = "hex"
            elif "[Equip Perf]" in message:
                perf_type = "equip"
            
            last_time = self.last_performance_log.get(perf_type, 0)
            if current_time - last_time < self.performance_interval:
                return False
            self.last_performance_log[perf_type] = current_time
        
        return True

# --- 增强的日志系统 ---
class EnhancedLoggingSystem:
    """增强的日志系统，明确分离主日志和OCR调试日志的职责"""
    
    def __init__(self):
        self.log_dir = os.path.join(config.APP_PATH, 'logs')
        self.main_logger = None
        self.ocr_logger = None
        self._ensure_log_directory()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def setup_main_logger(self):
        """
        配置主日志记录器 - 专门记录应用启动和配置信息
        职责：
        - 应用启动信息和版本信息
        - 模块加载路径和版本
        - 配置文件路径和关键配置项
        - 数据库连接信息
        - 管理器初始化状态
        - 触发器启动/停止事件
        - 系统错误和异常
        """
        yimiaojue_log_path = os.path.join(self.log_dir, 'yimiaojue.log')
        
        # 创建主日志处理器
        main_handler = RotatingFileHandler(
            yimiaojue_log_path, 
            maxBytes=2 * 1024 * 1024, 
            backupCount=3, 
            encoding='utf-8'
        )
        main_handler.setLevel(config.LOG_LEVEL_FILE)
        
        # 设置主日志格式 - 包含更多上下文信息
        main_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(name)s:%(funcName)s:%(lineno)d] - %(message)s'
        )
        main_handler.setFormatter(main_formatter)
        
        # 添加过滤器，排除OCR调试日志
        main_handler.addFilter(NoSpecificNameFilter('OCR_DEBUG'))
        
        # 添加去重过滤器，减少重复日志
        main_handler.addFilter(SpecificMessageFilter())
        main_handler.addFilter(DuplicateFilter(max_duplicates=2, time_window=5))
        
        return main_handler, yimiaojue_log_path
    
    def setup_ocr_logger(self):
        """
        配置OCR调试日志记录器 - 专门记录OCR处理详情
        职责：
        - OCR引擎启动和配置
        - 图像截取和预处理详情
        - 识别结果和置信度分数
        - 哈希计算和比较结果
        - 性能计时信息
        - OCR错误和重试逻辑
        """
        ocr_log_path = os.path.join(self.log_dir, 'ocr_debug.log')
        
        # 创建OCR调试日志处理器
        ocr_handler = RotatingFileHandler(
            ocr_log_path, 
            maxBytes=5 * 1024 * 1024, 
            backupCount=3, 
            encoding='utf-8'
        )
        ocr_handler.setLevel(config.LOG_LEVEL_OCR)
        
        # 设置OCR日志格式 - 专注于性能和结果
        ocr_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        ocr_handler.setFormatter(ocr_formatter)
        
        # 添加过滤器，只记录OCR调试日志
        ocr_handler.addFilter(SpecificNameFilter('OCR_DEBUG'))
        
        # 添加去重过滤器，减少重复的OCR调试日志
        ocr_handler.addFilter(SpecificMessageFilter())
        ocr_handler.addFilter(DuplicateFilter(max_duplicates=1, time_window=3))
        
        return ocr_handler, ocr_log_path
    
    def log_application_startup(self):
        """记录应用程序启动信息到主日志"""
        main_logger = logging.getLogger('MAIN')
        
        main_logger.info("=" * 60)
        main_logger.info("弈秒决应用程序启动")
        main_logger.info("=" * 60)
        
        # 记录版本信息
        try:
            version_file = os.path.join(config.BASE_PATH, 'modules', 'local_version.json')
            if os.path.exists(version_file):
                import json
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)
                main_logger.info(f"应用版本: {version_data.get('version', '未知')}")
                main_logger.info(f"构建时间: {version_data.get('build_time', '未知')}")
            else:
                main_logger.info("版本信息文件未找到")
        except Exception as e:
            main_logger.warning(f"读取版本信息失败: {e}")
        
        # 记录路径信息
        main_logger.info(f"应用可写目录 (APP_PATH): {config.APP_PATH}")
        main_logger.info(f"应用资源目录 (BASE_PATH): {config.BASE_PATH}")
        main_logger.info(f"是否为打包环境: {getattr(sys, 'frozen', False)}")
        
        # 记录配置文件信息
        main_logger.info(f"配置文件路径: {config.CONFIG_INI_PATH}")
        main_logger.info(f"调试模式: {config.DEBUG_MODE}")
        main_logger.info(f"日志级别 - 控制台: {config.LOG_LEVEL_CONSOLE}")
        main_logger.info(f"日志级别 - 文件: {config.LOG_LEVEL_FILE}")
        main_logger.info(f"日志级别 - OCR: {config.LOG_LEVEL_OCR}")
        
        # 记录数据库路径
        main_logger.info(f"海克斯数据库路径: {config.HEX_DB_PATH}")
        main_logger.info(f"装备数据库路径: {config.EQUIP_DB_PATH}")
        
        # 验证关键文件存在性
        critical_files = [
            config.HEX_DB_PATH,
            config.EQUIP_DB_PATH,
            config.HASH_BASES_PATH
        ]
        
        for file_path in critical_files:
            if os.path.exists(file_path):
                main_logger.info(f"关键文件存在: {file_path}")
            else:
                main_logger.error(f"关键文件缺失: {file_path}")
    
    def log_module_loading(self, module_name, module_path=None, version=None):
        """记录模块加载信息"""
        main_logger = logging.getLogger('MAIN')
        
        if module_path:
            main_logger.info(f"加载模块: {module_name} - 路径: {module_path}")
        else:
            main_logger.info(f"加载模块: {module_name}")
        
        if version:
            main_logger.info(f"模块版本: {module_name} - {version}")
    
    def log_manager_initialization(self, manager_name, status, details=None):
        """记录管理器初始化状态"""
        main_logger = logging.getLogger('MAIN')
        
        if status == 'success':
            main_logger.info(f"管理器初始化成功: {manager_name}")
        elif status == 'failed':
            main_logger.error(f"管理器初始化失败: {manager_name}")
        elif status == 'starting':
            main_logger.info(f"正在初始化管理器: {manager_name}")
        
        if details:
            main_logger.info(f"管理器详情 [{manager_name}]: {details}")
    
    def log_database_connection(self, db_path, status, details=None):
        """记录数据库连接状态"""
        main_logger = logging.getLogger('MAIN')
        
        if status == 'success':
            main_logger.info(f"数据库连接成功: {db_path}")
        elif status == 'failed':
            main_logger.error(f"数据库连接失败: {db_path}")
        
        if details:
            main_logger.info(f"数据库详情: {details}")
    
    def log_ocr_engine_initialization(self, engine_name, status, details=None):
        """记录OCR引擎初始化状态"""
        ocr_logger = logging.getLogger('OCR_DEBUG')
        
        if status == 'success':
            ocr_logger.info(f"OCR引擎初始化成功: {engine_name}")
        elif status == 'failed':
            ocr_logger.error(f"OCR引擎初始化失败: {engine_name}")
        elif status == 'starting':
            ocr_logger.info(f"正在初始化OCR引擎: {engine_name}")
        
        if details:
            ocr_logger.info(f"OCR引擎详情 [{engine_name}]: {details}")
    
    def log_ocr_processing(self, operation, image_info=None, result=None, duration=None, confidence=None):
        """记录OCR处理过程详情"""
        ocr_logger = logging.getLogger('OCR_DEBUG')
        
        if operation == 'screenshot':
            if image_info:
                ocr_logger.debug(f"截图操作 - 区域: {image_info.get('region', '未知')}, 尺寸: {image_info.get('size', '未知')}")
        
        elif operation == 'preprocess':
            if image_info:
                strategy = image_info.get('strategy', 'default')
                ocr_logger.debug(f"图像预处理 - 策略: {strategy}")
        
        elif operation == 'recognize':
            if result and duration:
                text = result.get('text', '')
                strategy = result.get('strategy', 'default')
                ocr_logger.info(f"OCR识别完成 - 策略: {strategy}, 结果: [{text}], 耗时: {duration:.3f}s")
                
                if confidence:
                    ocr_logger.info(f"OCR置信度 - 分数: {confidence}")
        
        elif operation == 'hash_calculation':
            if result:
                hash_type = result.get('type', 'unknown')
                hash_value = result.get('value', '')
                ocr_logger.debug(f"哈希计算 - 类型: {hash_type}, 值: {hash_value}")
        
        elif operation == 'hash_comparison':
            if result:
                similarity = result.get('similarity', 0)
                threshold = result.get('threshold', 0)
                match = result.get('match', False)
                ocr_logger.debug(f"哈希比较 - 相似度: {similarity}, 阈值: {threshold}, 匹配: {match}")
    
    def log_ocr_performance(self, operation, metrics):
        """记录OCR性能指标"""
        ocr_logger = logging.getLogger('OCR_DEBUG')
        
        if operation == 'batch_processing':
            total_time = metrics.get('total_time', 0)
            image_count = metrics.get('image_count', 0)
            success_count = metrics.get('success_count', 0)
            avg_time = total_time / image_count if image_count > 0 else 0
            
            ocr_logger.info(f"批量OCR性能 - 总耗时: {total_time:.3f}s, 图像数: {image_count}, 成功数: {success_count}, 平均耗时: {avg_time:.3f}s")
        
        elif operation == 'strategy_comparison':
            strategies = metrics.get('strategies', {})
            for strategy, data in strategies.items():
                success_rate = data.get('success_rate', 0)
                avg_time = data.get('avg_time', 0)
                ocr_logger.info(f"策略性能 [{strategy}] - 成功率: {success_rate:.1%}, 平均耗时: {avg_time:.3f}s")
    
    def log_ocr_error(self, operation, error, context=None):
        """记录OCR错误和重试逻辑"""
        ocr_logger = logging.getLogger('OCR_DEBUG')
        
        error_msg = f"OCR错误 [{operation}]: {str(error)}"
        if context:
            error_msg += f" - 上下文: {context}"
        
        ocr_logger.error(error_msg)
        
        # 如果是重试相关的错误，记录重试信息
        if context and 'retry' in context:
            retry_count = context.get('retry_count', 0)
            max_retries = context.get('max_retries', 0)
            ocr_logger.warning(f"OCR重试 [{operation}] - 第{retry_count}/{max_retries}次重试")

# 全局日志系统实例
_logging_system = None

def get_logging_system():
    """获取全局日志系统实例"""
    global _logging_system
    if _logging_system is None:
        _logging_system = EnhancedLoggingSystem()
    return _logging_system

def setup_logging():
    """
    [v6.0 增强版] 配置全局日志记录器，明确分离职责
    - 使用增强的日志系统，明确分离主日志和OCR调试日志
    - 主日志专注于应用启动、配置和管理器状态
    - OCR日志专注于OCR处理详情和性能指标
    """
    # 获取增强日志系统实例
    logging_system = get_logging_system()
    
    # 1. 获取根记录器，并设置最低处理级别为DEBUG
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    # 清理掉所有可能存在的旧处理器，确保从干净的状态开始
    root_logger.handlers = []

    # 2. 配置主日志处理器 (yimiaojue.log)
    main_handler, yimiaojue_log_path = logging_system.setup_main_logger()
    
    # 3. 配置OCR调试日志处理器 (ocr_debug.log)
    ocr_handler, ocr_log_path = logging_system.setup_ocr_logger()

    # 4. 配置控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(config.LOG_LEVEL_CONSOLE)
    console_formatter = logging.Formatter('%(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    # 控制台也排除OCR调试日志，避免过多输出
    console_handler.addFilter(NoSpecificNameFilter('OCR_DEBUG'))
    # 控制台也应用去重过滤器，减少重复输出
    console_handler.addFilter(SpecificMessageFilter())
    console_handler.addFilter(DuplicateFilter(max_duplicates=1, time_window=2))

    # 5. 将所有处理器添加到根记录器
    root_logger.addHandler(main_handler)
    root_logger.addHandler(ocr_handler)
    root_logger.addHandler(console_handler)

    # 6. 记录日志系统启动信息
    main_logger = logging.getLogger('MAIN')
    main_logger.info("增强日志系统已启动")
    main_logger.info(f"主日志文件: {os.path.abspath(yimiaojue_log_path)}")
    main_logger.info(f"OCR调试日志文件: {os.path.abspath(ocr_log_path)}")
    
    # 7. 记录应用程序启动信息
    logging_system.log_application_startup()
    
    return logging_system
