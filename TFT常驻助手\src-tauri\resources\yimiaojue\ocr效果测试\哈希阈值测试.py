# -*- coding: utf-8 -*-
"""
哈希阈值实时监控测试工具 (v2)

功能:
- 实时、独立地监控三个海克斯区域和装备区域的图像变化。
- 计算每个区域的 phash 值差异，并在屏幕上分别实时显示。
- 用于直观地观察不同游戏场景下的哈希变化，以帮助确定最佳阈值。
"""
import tkinter as tk
from tkinter import ttk
import threading
import time
from PIL import Image, ImageGrab
import imagehash
import sys
import os

# --- 路径设置，确保可以导入项目模块 ---
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

try:
    import config
    import utils
    import ocr_handler
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保此脚本位于 'yimiaojue/ocr效果测试' 目录下，且项目结构完整。")
    sys.exit(1)

class HashMonitorApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("哈希差异监控 v2")
        self.geometry("300x200+100+100") # 增加高度以容纳更多标签
        self.attributes("-topmost", True)
        self.attributes("-alpha", 0.8)
        
        # 为每个海克斯区域创建一个变量
        self.hex_diff_vars = [tk.StringVar(value=f"海克斯 {i+1} 差异: N/A") for i in range(3)]
        self.equip_diff_var = tk.StringVar(value="装备差异: N/A")
        
        self._create_widgets()
        
        self.is_running = True
        self.monitor_threads = []
        
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.start_monitoring()

    def _create_widgets(self):
        style = ttk.Style(self)
        style.configure("TFrame", background="#333")
        style.configure("TLabel", font=("微软雅黑", 12), padding=5, background="#333", foreground="#FFF")
        
        main_frame = ttk.Frame(self, style="TFrame")
        main_frame.pack(expand=True, fill=tk.BOTH)
        
        # 创建3个海克斯标签
        for i in range(3):
            ttk.Label(main_frame, textvariable=self.hex_diff_vars[i], style="TLabel").pack(pady=2)
        
        # 创建装备标签
        ttk.Label(main_frame, textvariable=self.equip_diff_var, style="TLabel").pack(pady=10)

    def start_monitoring(self):
        # 海克斯监控线程 (独立监控3个区域)
        hex_regions = config.SCREENSHOT_REGIONS_RELATIVE
        hex_thread = threading.Thread(target=self.monitor_hex_loop, 
                                      args=(hex_regions, 0.1, self.hex_diff_vars),
                                      daemon=True)
        
        # 装备监控线程 (监控单个轮廓区域)
        equip_region = config.EQUIP_CONTOUR_REGION_RELATIVE
        equip_thread = threading.Thread(target=self.monitor_single_area_loop,
                                        args=("装备", equip_region, 0.2, self.equip_diff_var),
                                        daemon=True)
        
        self.monitor_threads.extend([hex_thread, equip_thread])
        hex_thread.start()
        equip_thread.start()

    def monitor_hex_loop(self, regions, sleep_time, diff_vars):
        """独立监控多个区域的循环 (专用于海克斯)。"""
        last_hashes = [None] * len(regions)
        print(f"[海克斯] 监控启动，独立监控 {len(regions)} 个区域, 刷新间隔: {sleep_time}s")

        while self.is_running:
            try:
                game_rect = utils.get_game_window_rect(use_client_area=True)
                if not game_rect or game_rect[2] == 0:
                    time.sleep(1)
                    continue
                
                for i, rel_region in enumerate(regions):
                    abs_coords = utils.convert_relative_to_absolute(rel_region, game_rect)
                    img = ocr_handler.capture_screen(abs_coords)
                    if not img:
                        continue
                    
                    current_hash = imagehash.phash(img)
                    
                    if last_hashes[i] is not None:
                        diff = current_hash - last_hashes[i]
                        # 使用 lambda 捕获每次循环的变量值
                        self.after(0, lambda i=i, d=diff: diff_vars[i].set(f"海克斯 {i+1} 差异: {d}"))
                    
                    last_hashes[i] = current_hash
                
                time.sleep(sleep_time)

            except Exception as e:
                if "screen grab failed" in str(e):
                    print("[海克斯] 屏幕暂时无法截图，1秒后重试...")
                else:
                    print(f"[海克斯] 监控循环出错: {e}")
                time.sleep(1)

    def monitor_single_area_loop(self, name, region, sleep_time, diff_var):
        """监控单个区域的循环 (用于装备)。"""
        last_hash = None
        print(f"[{name}] 监控启动, 刷新间隔: {sleep_time}s")

        while self.is_running:
            try:
                game_rect = utils.get_game_window_rect(use_client_area=True)
                if not game_rect or game_rect[2] == 0:
                    time.sleep(1)
                    continue

                abs_coords = utils.convert_relative_to_absolute(region, game_rect)
                img = ocr_handler.capture_screen(abs_coords)
                if not img:
                    time.sleep(sleep_time)
                    continue
                
                current_hash = imagehash.phash(img)
                
                if last_hash is not None:
                    diff = current_hash - last_hash
                    self.after(0, lambda: diff_var.set(f"{name}差异: {diff}"))
                
                last_hash = current_hash
                
                time.sleep(sleep_time)

            except Exception as e:
                if "screen grab failed" in str(e):
                    print(f"[{name}] 屏幕暂时无法截图，1秒后重试...")
                else:
                    print(f"[{name}] 监控循环出错: {e}")
                time.sleep(1)

    def on_closing(self):
        print("正在关闭监控...")
        self.is_running = False
        for t in self.monitor_threads:
            t.join(timeout=0.5)
        self.destroy()

if __name__ == "__main__":
    app = HashMonitorApp()
    app.mainloop()