# 
实现计划

## 概述

本实现计划将TFT助手应用程序的优化需求转换为具体的编码任务。任务按照优先级和依赖关系排序，确保每个步骤都能在前一步的基础上构建，最终实现完整的系统优化。

## 任务列表

### 阶段1：装备窗口管理优化

- [x] 1. 实现增强的装备窗口清理机制





  - 创建WindowCleanupHandler类，实现延迟清理和强制清理功能
  - 修改EquipManager的clear_equip_results方法，确保窗口完全销毁
  - 添加窗口状态跟踪，防止重复创建和清理
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5_

- [x] 1.1 创建窗口清理处理器




  - 实现WindowCleanupHandler类，包含schedule_cleanup和force_cleanup方法
  - 添加cleanup_timer机制，支持200ms延迟清理避免闪烁
  - 实现force_cleanup_flag标志，确保强制清理的可靠性
  - _需求: 1.1, 1.4_

- [x] 1.2 实现窗口状态跟踪系统





  - 创建WindowStateTracker类，跟踪每个窗口的可见性和内容状态
  - 添加窗口ID管理，确保窗口的唯一性和正确清理
  - 实现状态变化监听，自动触发清理操作
  - _需求: 1.3, 1.5_


- [x] 1.3 修改装备管理器的窗口生命周期

  - 更新update_or_create_equip_windows方法，集成新的清理机制
  - 修改装备数量变化检测逻辑，立即触发窗口清理
  - 确保destroy方法正确清理所有资源
  - _需求: 1.1, 1.2, 1.5_

### 阶段2：动态OCR时序系统

- [x] 2. 实现动态OCR时序控制系统






  - 创建DynamicOCRTimer类，替换固定50秒的运行时长
  - 实现5秒动态延长机制，基于有效结果自动延长
  - 区分手动触发(10秒)和自动触发(5秒)的超时时间
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_- [x] 2.1 创建动态时序控制器
  - 实现DynamicOCRTimer类，包含start_timer、extend_timer和should_continue方法
  - 添加initial_timeout、manual_timeout和extension_timeout配置
  - 实现last_result_time跟踪机制
  - _需求: 2.1, 2.2, 2.4_

- [x] 2.2 集成时序控制到海克斯管理器


  - 修改HexManager的run_hex_ocr_loop方法，使用DynamicOCRTimer
  - 实现有效结果检测逻辑，自动调用extend_timer
  - 添加超时日志记录和资源清理
  - _需求: 2.3, 2.6_

- [x] 2.3 集成时序控制到装备管理器


  - 修改EquipManager的run_equip_ocr_loop方法，使用DynamicOCRTimer
  - 实现装备识别结果的有效性判断
  - 确保超时后正确停止OCR并清理资源
  - _需求: 2.3, 2.6_

### 阶段3：日志系统重构

- [x] 3. 优化日志系统，明确分离职责







  - 重构logger_setup.py，明确yimiaojue.log和ocr_debug.log的职责
  - yimiaojue.log记录配置和启动信息，ocr_debug.log记录OCR处理详情
  - 优化日志内容，减少不必要的输出
  - _需求: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8_

- [x] 3.1 重构主日志记录器配置


  - 修改setup_main_logger方法，专门记录应用启动和配置信息
  - 添加模块加载路径记录功能
  - 实现配置文件路径和关键设置的记录
  - _需求: 3.1, 3.3, 3.4_

- [x] 3.2 重构OCR调试日志记录器


  - 修改setup_ocr_logger方法，专门记录OCR处理详情
  - 添加OCR引擎初始化和图像处理过程记录
  - 实现识别结果、置信度和性能指标记录
  - _需求: 3.2, 3.5_

- [x] 3.3 更新应用程序启动日志


  - 修改主程序启动代码，记录所有工具路径到yimiaojue.log
  - 添加版本信息、数据库连接状态记录
  - 实现管理器初始化状态记录
  - _需求: 3.1, 3.3, 3.4_

### 阶段4：标准更新器系统

- [ ] 4. 实现标准化的更新器解耦系统
  - 创建StandardUpdater类，遵循软件更新最佳实践
  - 实现UpdateManifestHandler处理更新清单
  - 创建CompatibilityLayer处理版本兼容性
  - _需求: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6, 4.7_-
 [ ] 4.1 创建标准更新器核心
  - 实现StandardUpdater类，包含execute_update_cycle方法
  - 实现标准更新周期：预检查→下载→验证→兼容性检查→备份→安装→验证→清理
  - 添加UpdateException异常处理机制
  - _需求: 4.1, 4.2_

- [ ] 4.2 实现更新清单处理器
  - 创建UpdateManifestHandler类，处理update_manifest.json
  - 实现load_update_manifest和generate_legacy_manifest方法
  - 添加清单验证和操作解析功能
  - _需求: 4.3, 4.4, 4.6_

- [ ] 4.3 创建兼容性层
  - 实现CompatibilityLayer类，处理不同版本间的兼容性
  - 添加版本类型检测：单文件版、旧目录版、脚本版等
  - 实现自动迁移策略：migrate_single_to_directory等
  - _需求: 4.4, 4.5, 4.7_

- [ ] 4.4 实现回滚管理器
  - 创建RollbackManager类，处理更新失败时的回滚
  - 实现create_rollback_point和execute_rollback方法
  - 添加回滚日志记录和验证机制
  - _需求: 5.6, 5.7_

### 阶段5：增强的更新流程

- [ ] 5. 实现手动重启和错误恢复机制
  - 创建ManualRestartHandler，替换自动重启为手动重启
  - 实现UpdateErrorRecovery，处理更新过程中的各种错误
  - 优化backup_old_version清理逻辑
  - _需求: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6, 5.7, 5.8_

- [ ] 5.1 创建手动重启处理器
  - 实现ManualRestartHandler类，显示手动重启按钮
  - 添加handle_manual_restart方法，处理用户点击重启
  - 实现旧版本清理逻辑，在重启时执行cleanup_old_version
  - _需求: 5.1, 5.2_

- [ ] 5.2 实现更新错误恢复系统
  - 创建UpdateErrorRecovery类，处理下载、安装、兼容性错误
  - 实现create_recovery_point和restore_from_backup方法
  - 添加verify_update_integrity文件完整性验证
  - _需求: 5.4, 5.5, 5.6_

- [ ] 5.3 优化更新器主程序
  - 修改updater_main.py，集成新的标准更新器
  - 更新UI显示逻辑，支持手动重启按钮
  - 改进错误处理和用户提示
  - _需求: 5.1, 5.7, 5.8_

### 阶段6：版本回档系统

- [ ] 6. 实现版本历史管理和回档功能
  - 创建VersionHistoryManager，管理服务器端版本历史
  - 实现RollbackHandler，支持客户端版本回档
  - 支持Beta版和正式版各自保留2个历史版本
  - _需求: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7_- [ ] 
6.1 创建版本历史管理器
  - 实现VersionHistoryManager类，管理release_history和beta_history
  - 添加add_new_version方法，自动管理版本历史
  - 实现cleanup_old_versions，保留最新2个版本
  - _需求: 8.1, 8.2, 8.3_

- [ ] 6.2 实现客户端回档处理器
  - 创建RollbackHandler类，提供版本回档功能
  - 实现show_rollback_options，显示可回档版本
  - 添加perform_rollback方法，执行版本回档操作
  - _需求: 8.4, 8.5_

- [ ] 6.3 更新打包上传工具
  - 修改upload_tool.py，支持版本历史管理
  - 实现自动版本清理和历史记录更新
  - 添加Beta版和正式版的独立管理
  - _需求: 8.6, 8.7_

### 阶段7：性能优化和资源管理

- [ ] 7. 实现性能优化和资源管理改进
  - 优化哈希缓存和窗口坐标缓存机制
  - 减少不必要的线程和资源消耗
  - 改进图像处理和OCR调用效率
  - _需求: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [ ] 7.1 优化哈希缓存机制
  - 改进图像哈希计算和缓存逻辑
  - 实现哈希命中时跳过OCR处理
  - 添加缓存失效和清理机制
  - _需求: 7.1, 7.5_

- [ ] 7.2 优化窗口坐标缓存
  - 改进_get_game_window_rect_cached方法
  - 减少频繁的窗口坐标获取调用
  - 实现智能缓存更新策略
  - _需求: 7.6_

- [ ] 7.3 优化资源清理和线程管理
  - 改进OCR操作完成后的资源释放
  - 优化线程创建和销毁逻辑
  - 实现空闲时的资源最小化使用
  - _需求: 7.2, 7.3, 7.4_

### 阶段8：废弃文件清理和代码重构

- [ ] 8. 清理废弃文件和重构代码
  - 删除tesseract相关的废弃文件和代码
  - 清理managers备份.py等不再使用的文件
  - 重构代码结构，提高可维护性
  - _需求: 6.1, 6.2, 6.3_

- [ ] 8.1 清理tesseract废弃文件
  - 删除modules/tesseract1/目录及其所有内容
  - 移除代码中对tesseract的引用和依赖
  - 更新requirements.txt，移除pytesseract依赖
  - _需求: 6.1, 6.2_

- [ ] 8.2 清理其他废弃文件
  - 删除managers备份.py文件
  - 清理__pycache__目录和.pyc文件
  - 移除不再使用的测试文件和临时文件
  - _需求: 6.3_

- [ ] 8.3 代码结构重构
  - 重构导入语句，移除对废弃模块的引用
  - 优化代码组织结构，提高可读性
  - 更新文档和注释，反映最新的代码结构
  - _需求: 6.1, 6.2, 6.3_

### 阶段9：测试和验证

- [ ] 9. 实现全面的测试和验证
  - 创建单元测试，验证各个组件的功能
  - 实现集成测试，验证整体系统的协作
  - 进行性能测试，确保优化效果
  - _需求: 所有需求的验证_- [ 
] 9.1 创建单元测试套件
  - 为DynamicOCRTimer创建单元测试，验证时序逻辑
  - 为WindowCleanupHandler创建测试，验证窗口清理机制
  - 为CompatibilityLayer创建测试，验证版本兼容性处理
  - _需求: 所有核心组件的功能验证_

- [ ] 9.2 实现集成测试
  - 创建完整更新流程的集成测试
  - 测试OCR时序系统与管理器的协作
  - 验证日志系统的正确分离和记录
  - _需求: 系统整体协作验证_

- [ ] 9.3 进行性能测试和优化验证
  - 测试资源使用情况，验证性能优化效果
  - 验证窗口清理的及时性和完整性
  - 测试更新器的兼容性和稳定性
  - _需求: 性能和稳定性验证_

### 阶段10：文档更新和部署准备

- [ ] 10. 更新文档和准备部署
  - 更新README.md，反映最新的功能和架构
  - 创建更新器使用文档和配置说明
  - 准备发布包和部署脚本
  - _需求: 文档完整性和部署就绪_

- [ ] 10.1 更新项目文档
  - 更新README.md，描述新的功能和改进
  - 创建update_manifest.json的配置文档
  - 编写版本回档功能的使用说明
  - _需求: 文档完整性_

- [ ] 10.2 准备部署配置
  - 创建标准的update_manifest.json模板
  - 准备app_structure.json配置文件
  - 更新打包脚本，支持新的文件结构
  - _需求: 部署就绪_

- [ ] 10.3 最终验证和发布准备
  - 进行完整的端到端测试
  - 验证所有功能的正确性和稳定性
  - 准备发布说明和变更日志
  - _需求: 发布就绪_

## 任务执行说明

### 优先级
1. **高优先级**：阶段1-3（装备窗口管理、OCR时序、日志系统）- 直接影响用户体验
2. **中优先级**：阶段4-6（更新器系统、版本管理）- 影响维护和部署
3. **低优先级**：阶段7-10（性能优化、清理、测试、文档）- 代码质量和长期维护

### 依赖关系
- 阶段1可以独立开始
- 阶段2依赖阶段1的窗口管理改进
- 阶段3可以与阶段1-2并行进行
- 阶段4-6需要在前面阶段稳定后开始
- 阶段7-10在主要功能完成后进行

### 测试策略
- 每个阶段完成后进行单元测试
- 每2-3个阶段完成后进行集成测试
- 最终进行完整的系统测试和性能验证

### 回滚计划
- 每个主要阶段开始前创建代码备份
- 保持向后兼容性，确保可以回滚到稳定版本
- 使用版本控制系统管理代码变更