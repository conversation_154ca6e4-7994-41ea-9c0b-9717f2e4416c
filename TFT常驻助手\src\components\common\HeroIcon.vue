<template>
  <div 
    class="hero-icon-container"
    :style="{ width: `${size}px`, height: `${size}px` }"
    @click="handleClick"
  >
    <!-- 图标图片 -->
    <img 
      v-if="iconSrc && !imageError"
      :src="iconSrc"
      :alt="heroName"
      class="hero-icon-image"
      @load="onImageLoad"
      @error="onImageError"
    />
    
    <!-- 占位符 -->
    <div 
      v-else
      class="hero-icon-placeholder"
      :style="{ fontSize: `${size * 0.4}px` }"
    >
      {{ placeholder }}
    </div>
    
    <!-- 费用徽章已移除 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { getSmartIconPath, getHeroPlaceholder } from '@/utils/iconUtils'

// Props
interface Props {
  heroName: string
  iconPath?: string | null
  cost?: number
  size?: number
  showCostBadge?: boolean // 已废弃，保留兼容性
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 60,
  showCostBadge: false, // 默认不显示
  clickable: true
})

// Emits
const emit = defineEmits<{
  click: [heroName: string]
}>()

// 响应式数据
const iconSrc = ref<string | null>(null)
const imageError = ref(false)
const isLoading = ref(false)

// 计算属性
const placeholder = computed(() => getHeroPlaceholder(props.heroName))

// 费用徽章样式类
const getCostBadgeClass = (cost: number) => {
  const costClasses = {
    1: 'cost-1-badge',
    2: 'cost-2-badge',
    3: 'cost-3-badge',
    4: 'cost-4-badge',
    5: 'cost-5-badge'
  }
  return costClasses[cost as keyof typeof costClasses] || 'cost-0-badge'
}

// 加载图标
const loadIcon = async () => {
  if (!props.iconPath) {
    iconSrc.value = null
    return
  }
  
  isLoading.value = true
  imageError.value = false
  
  try {
    const smartPath = await getSmartIconPath(props.iconPath)
    iconSrc.value = smartPath
    
    if (!smartPath) {
      console.warn(`未找到英雄 ${props.heroName} 的图标: ${props.iconPath}`)
    }
  } catch (error) {
    console.error(`加载英雄 ${props.heroName} 图标失败:`, error)
    iconSrc.value = null
  } finally {
    isLoading.value = false
  }
}

// 图片加载成功
const onImageLoad = () => {
  imageError.value = false
}

// 图片加载失败
const onImageError = () => {
  console.warn(`英雄 ${props.heroName} 图标加载失败: ${iconSrc.value}`)
  imageError.value = true
}

// 点击处理
const handleClick = () => {
  if (props.clickable) {
    emit('click', props.heroName)
  }
}

// 监听iconPath变化
watch(() => props.iconPath, loadIcon, { immediate: true })

// 组件挂载时加载图标
onMounted(() => {
  loadIcon()
})
</script>

<style scoped>
.hero-icon-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-icon-container:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.hero-icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.hero-icon-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(255, 255, 255, 0.2);
}

.cost-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.cost-1-badge { background: linear-gradient(135deg, #d3d3d3, #a8a8a8); }
.cost-2-badge { background: linear-gradient(135deg, #32cd32, #228b22); }
.cost-3-badge { background: linear-gradient(135deg, #00bfff, #0080ff); }
.cost-4-badge { background: linear-gradient(135deg, #bf00ff, #8b00ff); }
.cost-5-badge { background: linear-gradient(135deg, #ffd700, #ffb347); }
.cost-0-badge { background: linear-gradient(135deg, #666666, #444444); }
</style>