<template>
  <div class="cost-filter flex gap-1">
    <Button
      v-for="cost in costs"
      :key="cost.value"
      :variant="isSelected(cost.value) ? 'default' : 'outline'"
      :class="cn(
        'cost-button h-8 px-3 text-xs font-medium transition-all duration-200',
        getCostClasses(cost.value, isSelected(cost.value)),
        props.class
      )"
      @click="toggleCost(cost.value)"
    >
      {{ cost.label }}
    </Button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui'

type CostValue = 'all' | 1 | 2 | 3 | 4 | 5

interface CostOption {
  value: CostValue
  label: string
}

interface Props {
  selectedCosts: CostValue[]
  class?: string
}

interface Emits {
  (e: 'cost-change', costs: CostValue[]): void
}

const props = withDefaults(defineProps<Props>(), {
  class: ''
})

const emit = defineEmits<Emits>()

const costs: CostOption[] = [
  { value: 'all', label: '全部' },
  { value: 1, label: '1费' },
  { value: 2, label: '2费' },
  { value: 3, label: '3费' },
  { value: 4, label: '4费' },
  { value: 5, label: '5费' },
]

const isSelected = (cost: CostValue): boolean => {
  return props.selectedCosts.includes(cost)
}

const toggleCost = (cost: CostValue) => {
  let newCosts: CostValue[]
  
  if (cost === 'all') {
    // 如果点击"全部"，清空其他选择或选择全部
    newCosts = isSelected('all') ? [] : ['all']
  } else {
    // 如果点击具体费用
    if (isSelected(cost)) {
      // 取消选择
      newCosts = props.selectedCosts.filter(c => c !== cost && c !== 'all')
    } else {
      // 添加选择，并移除"全部"
      newCosts = [...props.selectedCosts.filter(c => c !== 'all'), cost]
    }
  }
  
  emit('cost-change', newCosts)
}

const getCostClasses = (cost: CostValue, selected: boolean): string => {
  if (cost === 'all') {
    return selected 
      ? 'bg-primary text-primary-foreground border-primary' 
      : 'bg-transparent border-border text-foreground hover:bg-accent'
  }
  
  const costClasses: Record<number, string> = {
    1: selected 
      ? 'bg-cost-1 text-text-dark border-cost-1' 
      : 'bg-cost-1/20 border-cost-1 text-cost-1 hover:bg-cost-1/40',
    2: selected 
      ? 'bg-cost-2 text-text-dark border-cost-2' 
      : 'bg-cost-2/20 border-cost-2 text-cost-2 hover:bg-cost-2/40',
    3: selected 
      ? 'bg-cost-3 text-text-dark border-cost-3' 
      : 'bg-cost-3/20 border-cost-3 text-cost-3 hover:bg-cost-3/40',
    4: selected 
      ? 'bg-cost-4 text-text-light border-cost-4' 
      : 'bg-cost-4/20 border-cost-4 text-cost-4 hover:bg-cost-4/40',
    5: selected 
      ? 'bg-cost-5 text-text-dark border-cost-5' 
      : 'bg-cost-5/20 border-cost-5 text-cost-5 hover:bg-cost-5/40',
  }
  
  return costClasses[cost as number] || ''
}
</script>

<style scoped>
.cost-button {
  /* 固定按钮尺寸 - 基于UI风格指导 */
  min-width: 48px;
  height: 32px;
}

.cost-button:hover {
  transform: translateY(-1px);
}

.cost-button:active {
  transform: translateY(0);
}
</style>
