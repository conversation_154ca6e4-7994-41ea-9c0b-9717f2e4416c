# 状态管理说明

本项目使用 Pinia 进行状态管理，主要包含两个核心 store：

## 1. App Store (`useAppStore`)

主应用状态管理，复刻原 `主窗口.py` 中的 `MainWindow` 类功能。

### 主要状态

- `currentPage`: 当前页面索引
- `isMinimized`: 窗口是否最小化
- `historyStack`: 导航历史记录栈
- `viewsLoaded`: 视图加载状态记录

### 主要方法

- `switchMainPage(pageIndex)`: 切换主页面
- `navigateToDetail(detailPageIndex, dataKey)`: 导航到详情页面
- `goBack()`: 返回上一页
- `toggleWindow()`: 切换窗口最小化状态
- `resetState()`: 重置应用状态

### 使用示例

```typescript
import { useAppStore } from '@/stores/app';
import { PAGE_CONSTANTS } from '@/types';

const appStore = useAppStore();

// 切换到英雄列表页面
appStore.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);

// 导航到英雄详情页面
appStore.navigateToDetail(PAGE_CONSTANTS.PAGE_HERO_DETAIL, 'hero_name');

// 返回上一页
if (appStore.hasHistory) {
  appStore.goBack();
}
```

## 2. Data Store (`useDataStore`)

数据缓存状态管理，复刻原 `数据库操作.py` 中的缓存和预加载功能。

### 主要状态

- `globalHeroInfoMap`: 全局英雄信息映射
- `globalTraitIconMap`: 全局羁绊图标映射
- `globalItemInfoMap`: 全局装备信息映射
- `queryCache`: 查询结果缓存
- `isGlobalDataLoaded`: 全局数据是否已加载
- `isPreloading`: 是否正在预加载

### 主要方法

- `preloadGlobalData()`: 预加载全局数据
- `getCachedQuery(cacheKey)`: 获取缓存的查询结果
- `setCachedQuery(cacheKey, data)`: 设置查询缓存
- `getHeroInfo(heroName)`: 获取英雄信息
- `getItemInfo(itemName)`: 获取装备信息
- `getTraitIcon(traitName)`: 获取羁绊图标
- `executeWithCache(cacheKey, queryFn)`: 执行带缓存的查询

### 使用示例

```typescript
import { useDataStore } from '@/stores/data';

const dataStore = useDataStore();

// 预加载全局数据
await dataStore.preloadGlobalData();

// 获取英雄信息
const heroInfo = dataStore.getHeroInfo('阿卡丽');

// 执行带缓存的查询
const result = await dataStore.executeWithCache('hero_list', async () => {
  return await invoke('get_hero_list');
});
```

## 3. 组合式函数 (`useData`)

提供便捷的数据查询和缓存操作接口。

### 主要功能

- 封装常用的数据查询方法
- 自动处理缓存逻辑
- 提供统一的错误处理

### 使用示例

```typescript
import { useData } from '@/composables/useData';

const { 
  getHeroList, 
  getCompDetail, 
  getHeroInfo, 
  preloadData 
} = useData();

// 获取英雄列表（自动缓存）
const heroes = await getHeroList();

// 获取阵容详情（自动缓存）
const compDetail = await getCompDetail('阵容名称');

// 从缓存获取英雄信息
const heroInfo = getHeroInfo('英雄名称');

// 预加载数据
await preloadData();
```

## 4. 数据加载器 (`DataLoader`)

单例模式的数据加载器，确保应用启动时只执行一次数据预加载。

### 使用示例

```typescript
import { dataLoader } from '@/utils/dataLoader';

// 在应用启动时初始化
await dataLoader.initialize();

// 检查数据是否就绪
if (dataLoader.isReady()) {
  // 数据已准备就绪
}

// 重新加载数据
await dataLoader.reload();
```

## 5. 类型定义

所有相关的 TypeScript 类型定义都在 `@/types/index.ts` 中：

- `PageType`: 页面类型
- `NavigationHistoryItem`: 导航历史记录项
- `MainAppState`: 主应用状态接口
- `GlobalDataCache`: 全局数据缓存接口
- `QueryResult`: 数据查询结果接口
- `HeroInfo`, `ItemInfo`, `TraitInfo`, `CompInfo`, `HexInfo`: 各种数据实体接口

## 6. 最佳实践

### 在组件中使用

```vue
<template>
  <div>
    <button @click="switchPage">切换页面</button>
    <div v-if="isDataReady">数据已就绪</div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useAppStore } from '@/stores/app';
import { useData } from '@/composables/useData';
import { PAGE_CONSTANTS } from '@/types';

const appStore = useAppStore();
const { isDataReady, preloadData } = useData();

const switchPage = () => {
  appStore.switchMainPage(PAGE_CONSTANTS.PAGE_HERO_LIST);
};

onMounted(async () => {
  // 组件挂载时预加载数据
  await preloadData();
});
</script>
```

### 错误处理

```typescript
try {
  await dataStore.preloadGlobalData();
} catch (error) {
  console.error('数据加载失败:', error);
  // 处理错误，可能显示错误提示给用户
}
```

### 性能优化

- 使用 `computed` 来响应状态变化
- 合理使用缓存，避免重复查询
- 在适当的时机清理缓存以释放内存
- 使用 `executeWithCache` 来自动管理查询缓存

## 7. 测试

状态管理功能包含完整的单元测试，位于：

- `src/stores/__tests__/app.spec.ts`: App Store 测试
- `src/stores/__tests__/data.spec.ts`: Data Store 测试

运行测试：

```bash
npm run test:unit
```

## 8. 示例组件

查看 `src/examples/StateManagementExample.vue` 了解完整的使用示例。