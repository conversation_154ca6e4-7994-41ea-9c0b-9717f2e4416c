import os
from qiniu import Auth, put_file_v2

# --- 1. 请在这里填入您的个人信息 ---
# 从七牛云控制台的“密钥管理”中获取
# 从环境变量中获取七牛云密钥，确保安全性
access_key = os.getenv('QINIU_ACCESS_KEY')
secret_key = os.getenv('QINIU_SECRET_KEY')

# 检查环境变量是否设置
if not access_key or not secret_key:
    print("错误：请先设置环境变量 QINIU_ACCESS_KEY 和 QINIU_SECRET_KEY")
    print("设置方法：")
    print("  Windows: set QINIU_ACCESS_KEY=你的密钥")
    print("  或在系统环境变量中设置")
    exit()

print(f"Access Key: {access_key[:10]}...")
print(f"Secret Key: {secret_key[:10]}...")

# 您在七牛云上创建的存储空间名称
bucket_name = 'yimiaojue'
# ------------------------------------


# --- 2. 脚本自动创建一个简单的测试文件 ---
# 定义要在七牛云上保存的文件名 (Key)
key = 'test/upload_test.txt'
# 定义本地临时文件的路径和名称
local_file_path = 'upload_test.txt'
# 创建文件并写入内容
try:
    with open(local_file_path, 'w', encoding='utf-8') as f:
        f.write('你好，七牛云！这是一个来自Python脚本的上传测试。')
    print(f"成功创建测试文件: {local_file_path}")
except Exception as e:
    print(f"创建测试文件失败: {e}")
    exit() # 如果文件创建失败，则退出脚本


# --- 3. 核心上传逻辑 ---
# 构建鉴权对象
q = Auth(access_key, secret_key)

# 生成上传凭证(Upload Token)，有效期设置为1小时（3600秒）
token = q.upload_token(bucket_name, key, 3600)

print(f"\n正在尝试将'{local_file_path}'上传到存储空间'{bucket_name}'...")
print(f"文件在云端的名称将是: '{key}'")

try:
    # 调用sdk的put_file_v2函数进行上传
    ret, info = put_file_v2(token, key, local_file_path)

    # --- 4. 打印结果并清理 ---
    if info.status_code == 200:
        print("\n--- 上传成功！ ---")
        print("文件信息:")
        print(f"  Hash: {ret.get('hash')}")
        print(f"  Key: {ret.get('key')}")
        # 成功后可以去七牛云控制台对应的存储空间查看文件
    else:
        print("\n--- 上传失败！ ---")
        print("服务器返回信息:")
        print(info.text_body)

except Exception as e:
    print(f"\n--- 上传过程中发生异常 ---")
    print(e)

finally:
    # 删除本地的临时测试文件
    if os.path.exists(local_file_path):
        os.remove(local_file_path)
        print(f"\n已清理本地测试文件: {local_file_path}")