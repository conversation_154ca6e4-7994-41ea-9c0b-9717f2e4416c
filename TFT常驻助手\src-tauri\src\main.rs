// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;
mod window;
mod python_caller;

use database::{
    clear_query_cache, execute_query_command, get_comp_detail, get_comp_item_overall_stats,
    get_comp_item_unit_stats, get_comp_level_recommendations, get_comp_list, get_comp_trait_stats,
    get_comp_unit_stats, get_database_stats, get_equipment_hero_stats, get_hero_detail,
    get_hero_item_stats, get_hero_list, get_hex_list, get_hex_detail, get_item_categories, get_item_detail,
    get_item_icon_path, get_item_list, get_items_by_category, get_trait_levels, get_trait_list, test_database_connection,
    check_icon_exists, get_icon_base64,
};
use window::{
    center_window, close_app, get_window_position, get_window_size, get_window_state,
    is_window_minimized, restore_window_state, save_original_size, save_window_state, set_always_on_top, set_max_size,
    set_min_size, set_window_position, start_drag, toggle_window_size,
};
use python_caller::{
    check_python_environment, start_yimiaojue, check_yimiaojue_status, test_python_script, get_python_details, run_python_script_direct,
    get_ocr_status, manual_scan_hex, manual_scan_equipment, toggle_auto_mode,
};

fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            // 数据库操作命令
            execute_query_command,
            clear_query_cache,
            test_database_connection,
            // 核心数据查询命令
            get_comp_list,
            get_hero_list,
            get_item_list,
            get_hex_list,
            // 详情数据查询命令
            get_comp_detail,
            get_hero_detail,
            get_item_detail,
            get_hex_detail,
            // 羁绊相关查询命令
            get_trait_list,
            get_trait_levels,
            // 阵容相关详细查询命令
            get_comp_unit_stats,
            get_comp_trait_stats,
            get_comp_item_overall_stats,
            get_comp_item_unit_stats,
            get_comp_level_recommendations,
            // 英雄相关详细查询命令
            get_hero_item_stats,
            // 装备相关详细查询命令
            get_item_icon_path,
            get_equipment_hero_stats,
            get_item_categories,
            get_items_by_category,
            // 通用统计查询命令
            get_database_stats,
            // 文件系统相关命令
            check_icon_exists,
            get_icon_base64,
            // 窗口管理命令
            toggle_window_size,
            save_original_size,
            set_window_position,
            get_window_position,
            get_window_size,
            start_drag,
            close_app,
            is_window_minimized,
            set_always_on_top,
            get_window_state,
            save_window_state,
            restore_window_state,
            center_window,
            set_min_size,
            set_max_size,
            // Python调用命令
            check_python_environment,
            start_yimiaojue,
            check_yimiaojue_status,
            test_python_script,
            get_python_details,
            run_python_script_direct,
            // OCR API命令
            get_ocr_status,
            manual_scan_hex,
            manual_scan_equipment,
            toggle_auto_mode
        ])
        .run(tauri::generate_context!())
        .expect("启动Tauri应用失败");
}
