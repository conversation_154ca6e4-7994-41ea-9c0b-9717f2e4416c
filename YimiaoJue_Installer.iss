﻿; -- 弈秒决 Inno Setup 安装脚本 (v3.2 - 更新器模式，增加卸载逻辑) --

#define MyAppName "弈秒决"
#define MyAppPublisher "弈秒决"
#define MyAppExeName "updater.exe"
#define MyAppNameEN "YimiaoJue"

; [v3.1 优化] 版本号不再硬编码，由 build.bat 构建时传入
; #define MyAppVersion "0.2.3"

; [v3.2 修复] 增加默认版本号，使得脚本可独立编译
#ifndef MyAppVersion
  #define MyAppVersion "0.0.0-dev"
#endif

[Setup]
AppId={{E6A4B3A8-1B7C-4B0D-9C2A-8F8D6E1A2B3C}}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
DefaultDirName={autopf}\{#MyAppNameEN}
DefaultGroupName={#MyAppName}
OutputBaseFilename=YimiaoJue_Setup_v{#MyAppVersion}
OutputDir=dist
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Languages\Chinese.isl"

[Tasks]
Name: "desktopicon"; Description: "创建桌面快捷方式"; GroupDescription: "附加任务:";

[Files]
; [v3.1 优化] 安装包只包含更新器和其配置文件。
; 主程序将由更新器在首次运行时自动下载。
Source: "dist\{#MyAppExeName}"; DestDir: "{app}"
Source: "yimiaojue\config.ini"; DestDir: "{app}"

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\卸载 {#MyAppName}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "运行 {#MyAppName}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}\*"
Type: dirifempty; Name: "{app}"

[Code]
var
  UninstallOldVersion: Boolean;

// [新增] 检查是否存在旧版本，并获取其卸载程序路径
function GetUninstallString(): String;
var
  sUninstPath: String;
  sUninstallString: String;
begin
  Result := '';
  sUninstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#SetupSetting("AppId")}_is1');
  // 检查是为所有用户安装(HKLM)还是为当前用户安装(HKCU)
  if not RegQueryStringValue(HKA, sUninstPath, 'UninstallString', sUninstallString) then
    RegQueryStringValue(HKCU, sUninstPath, 'UninstallString', sUninstallString);
  Result := sUninstallString;
end;

// [新增] 在安装向导初始化时，检查是否需要卸载旧版本
function InitializeSetup(): Boolean;
begin
  // 如果找到了旧版本的卸载程序，则标记为需要卸载
  if GetUninstallString() <> '' then
  begin
    UninstallOldVersion := True;
  end
  else
  begin
    UninstallOldVersion := False;
  end;
  Result := True;
end;

// 这是你原来就有的代码，作用是赋予安装文件夹权限，需要保留
procedure CurStepChanged(CurStep: TSetupStep);
var
  ErrorCode: Integer;
  AppDir: String;
  UninstallPath: String;
  ResultCode: integer;
begin
  // [新增] 如果标记了需要卸-载，则在安装开始时静默执行
  if (CurStep = ssInstall) and (UninstallOldVersion) then
  begin
    UninstallPath := GetUninstallString();
    if UninstallPath <> '' then
    begin
      // 使用 /SILENT 参数静默卸载，ewWaitUntilTerminated确保卸载完成后再继续
      Exec(RemoveQuotes(UninstallPath), '/SILENT', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    end;
  end;

  if CurStep = ssPostInstall then
  begin
    AppDir := ExpandConstant('{app}');
    Exec('cmd.exe', '/c icacls "' + AppDir + '" /grant Everyone:(OI)(CI)F /T', '', SW_HIDE, ewWaitUntilTerminated, ErrorCode);
  end;
end;

// [保留] 检查路径是否只包含ASCII字符（即纯英文/数字/符号）
function IsASCII(const S: String): Boolean;
var
  I: Integer;
begin
  Result := True;
  for I := 1 to Length(S) do
  begin
    if Ord(S[I]) > 127 then
    begin
      Result := False;
      Exit;
    end;
  end;
end;

// [v3.2 移除] 检查安装路径的合法性
// 由于已迁移至RapidOCR，不再有中文路径问题，此检查已无需保留。
{
function NextButtonClick(CurPageID: Integer): Boolean;
begin
  // 默认为True，允许进入下一步
  Result := True;
  
  // 仅当用户在"选择目录"页面操作时才进行检查
  if CurPageID = wpSelectDir then
  begin
    // 如果路径中包含非英文字符
    if not IsASCII(WizardForm.DirEdit.Text) then
    begin
      // 弹出错误提示框
      MsgBox('安装路径中包含了非英文字符。' + #13#10#13#10 + '为了确保OCR引擎能正常工作，请选择一个纯英文的安装路径（例如 C:\Games\YimiaoJue）。', mbError, MB_OK);
      // 阻止进入下一步
      Result := False;
    end;
  end;
end;
}