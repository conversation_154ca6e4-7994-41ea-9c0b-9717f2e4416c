import sqlite3
import json
import os
import traceback
import time
from pathlib import Path

# --- 配置 ---
# 获取当前脚本所在的目录作为基础目录
# 假设所有数据文件夹 (映射文件, 海克斯数据等) 都在这个目录下
BASE_DIR = Path(__file__).parent.resolve()
DB_FILE = BASE_DIR / "tft_data.db"  # 数据库文件名

# 各个数据文件的相对路径 (相对于 BASE_DIR)
HEX_MAPPING_FILE = BASE_DIR / "映射文件" / "海克斯映射.json"
TRAIT_MAPPING_FILE = BASE_DIR / "映射文件" / "羁绊映射.json"
# 移除装备图标映射文件 - 现在统一使用装备分类映射
# ITEM_MAPPING_FILE = BASE_DIR / "映射文件" / "装备图标映射.json"  # 已废弃
HERO_MAPPING_FILE = BASE_DIR / "映射文件" / "英雄装备映射.json"
COMP_BASE_DATA_FILE = BASE_DIR / "阵容数据" / "阵容基础数据.json"
COMP_MAPPING_FILE = BASE_DIR / "映射文件" / "阵容数据映射.json"
COMP_DETAIL_DIR = BASE_DIR / "阵容数据"
HERO_DETAIL_DIR = BASE_DIR # 英雄详细数据路径需要从映射文件中读取，这里设为基础
# 装备分类映射文件路径 - 现在是装备数据的唯一来源
ITEM_CATEGORY_FILE = BASE_DIR / "映射文件" / "装备分类映射.json"
# 海克斯说明数据文件路径
HEX_DESCRIPTIONS_FILE = BASE_DIR / "海克斯数据" / "海克斯说明.json"
HEX_DESCRIPTIONS_MAPPING_FILE = BASE_DIR / "映射文件" / "海克斯说明映射.json"

# --- 辅助函数 ---

def standardize_hero_id(hero_id):
    """标准化英雄ID，处理不同格式的英雄名称（如'TFT14_Ekko'和'ekko'）
    返回标准化后的ID，用于数据库匹配"""
    if not hero_id:
        return None
    
    # 移除前缀如'TFT14_'、'TFT9_'等
    if '_' in hero_id and hero_id.startswith('TFT'):
        parts = hero_id.split('_', 1)
        if len(parts) > 1:
            hero_id = parts[1]
    
    # 转为小写以保证一致性
    return hero_id.lower()

def standardize_trait_id(trait_id):
    """标准化羁绊ID，移除前缀 (如 'TFT14_') 并转为小写。"""
    if not trait_id:
        return None
    
    # 移除前缀如 'TFT14_'、'TFT9_' 等
    if '_' in trait_id and trait_id.startswith('TFT'):
        parts = trait_id.split('_', 1)
        if len(parts) > 1:
            trait_id = parts[1]
            
    # 转为小写
    return trait_id.lower()

def standardize_item_id(item_id):
    """标准化装备ID
    处理如 'TFT14_Item_ArmorcladEmblemItem', 'tft14_mordekaisercyberneticitem_radiant'
    """
    if not item_id:
        return None

    standardized = str(item_id).lower()

    # 移除 TFT 前缀
    if standardized.startswith(('tft6_', 'tft7_', 'tft8_', 'tft9_', 'tft10_', 'tft11_', 'tft12_', 'tft13_', 'tft14_', 'tft15_', 'tftset10_', 'tftset11_', 'tftset12_', 'tftset13_', 'tftset14_', 'tftset15_')):
        parts = standardized.split('_', 1)
        if len(parts) > 1:
            standardized = parts[1]

    # 移除可能的 _item 后缀 (包括处理 _itemitem)
    if standardized.endswith('_itemitem'):
        standardized = standardized[:-9]
    elif standardized.endswith('_item'):
        standardized = standardized[:-5]

    # 移除特定后缀 (如 _radiant, _ornn, _support, _emblem)
    suffixes_to_remove = ['_radiant', '_ornn', '_support', '_emblem', '_shimmerscale', '_augment', '_darkin', '_stage1', '_stage2', '_stage3']
    for suffix in suffixes_to_remove:
        if standardized.endswith(suffix):
            standardized = standardized[:-len(suffix)]
            break # 假设只有一个后缀

    # 尝试移除英雄名称（如果存在） e.g., mordekaisercyberneticitem
    # 这个比较复杂，可能需要英雄列表，暂时不做，看是否需要
    # 简单的规则：如果包含 'cyberneticitem' 或类似结构，尝试移除
    if 'cyberneticitem' in standardized:
         standardized = standardized.replace('cyberneticitem', '')
    # 可以在这里添加更多英雄特定装备的清理规则

    # 移除数字后缀 (如 _1, _2)
    if standardized[-2] == '_' and standardized[-1].isdigit():
        standardized = standardized[:-2]

    # 特殊处理：如果名称包含'item_'，尝试移除它（可能由前缀清理引入）
    if 'item_' in standardized:
        standardized = standardized.replace('item_', '')

    # 清理完成后可能还需手动映射一些特殊情况，这里先返回通用清理结果
    return standardized.strip()

def standardize_heroes_in_data(data):
    """递归处理数据结构中的所有英雄ID
    可以处理列表、字典和嵌套结构
    - 直接的英雄ID字符串会被标准化
    - 包含'unit_id'键的字典会标准化该键的值
    - 对于列表和字典，会递归处理其中的所有元素
    """
    if isinstance(data, str) and (data.startswith('TFT') or '_' in data):
        # 可能是英雄ID字符串
        return standardize_hero_id(data)
    
    elif isinstance(data, dict):
        result = {}
        for key, value in data.items():
            if key == 'unit_id' or key == 'unitId':
                # 直接标准化这些特定键的值
                result[key] = standardize_hero_id(value)
            else:
                # 递归处理其他键值
                result[key] = standardize_heroes_in_data(value)
        return result
    
    elif isinstance(data, list):
        # 递归处理列表的每个元素
        return [standardize_heroes_in_data(item) for item in data]
    
    else:
        # 其他类型的数据原样返回
        return data

def delete_database():
    """如果数据库文件存在，则删除"""
    if DB_FILE.exists():
        try:
            os.remove(DB_FILE)
            print(f"已删除旧数据库: {DB_FILE}")
        except OSError as e:
            print(f"删除数据库失败: {e}")
            exit(1) # 如果无法删除旧库，则停止执行

def create_connection():
    """创建数据库连接"""
    conn = None
    try:
        conn = sqlite3.connect(DB_FILE)
        print(f"数据库连接已建立: {DB_FILE}")
        # 启用外键约束 (如果需要严格的关联关系)
        conn.execute("PRAGMA foreign_keys = ON;")
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接错误: {e}")
        exit(1) # 连接失败则停止

def create_table(conn, create_table_sql):
    """创建数据库表"""
    try:
        cursor = conn.cursor()
        cursor.execute(create_table_sql)
    except sqlite3.Error as e:
        print(f"创建表失败: {e}\nSQL:\n{create_table_sql}")
        # 不退出，可能只是表已存在，但最好检查错误

def load_json(file_path: Path):
    """安全地加载 JSON 文件"""
    if not file_path.exists():
        print(f"警告: JSON 文件未找到: {file_path}")
        return None
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        print(f"错误: 解析 JSON 文件失败: {file_path} - {e}")
        return None
    except Exception as e:
        print(f"错误: 读取 JSON 文件时发生未知错误: {file_path} - {e}")
        return None

def sanitize_text(text):
    """清理可能存在的None或非字符串类型"""
    return str(text) if text is not None else ""

def sanitize_float(value):
    """尝试将值转换为浮点数，失败则返回 None"""
    try:
        # 移除百分号等非数字字符
        cleaned_value = str(value).replace('%', '').strip()
        return float(cleaned_value) if cleaned_value else None
    except (ValueError, TypeError):
        return None

def sanitize_int(value):
    """尝试将值转换为整数，失败则返回 None"""
    try:
        # 移除可能存在的逗号等
        cleaned_value = str(value).replace(',', '').strip()
        return int(cleaned_value) if cleaned_value else None
    except (ValueError, TypeError):
        return None

def sanitize_play_rate(value):
    """特殊处理出场率值，提取格式如"22,758 2.3"中的出场率百分比"""
    try:
        if not value:
            return None
        
        # 将值转换为字符串并检查是否包含空格
        str_value = str(value).strip()
        if ' ' in str_value:
            # 拆分为场次和出场率两部分
            parts = str_value.split(' ', 1)
            if len(parts) == 2:
                # 提取第二部分作为出场率
                rate_part = parts[1].strip()
                return float(rate_part) if rate_part else None
        
        # 如果不是特殊格式，则按正常方式处理
        return sanitize_float(value)
    except (ValueError, TypeError, IndexError):
        return None

# --- 数据库表创建语句 ---

# 1. 海克斯表
SQL_CREATE_HEXES_TABLE = """
CREATE TABLE IF NOT EXISTS hexes (
    name TEXT PRIMARY KEY NOT NULL,  -- 海克斯中文名 (主键)
    tier TEXT,                       -- 评级 (S, A, B, C, D, 未知)
    icon_url TEXT,                   -- 图标网络地址
    icon_path TEXT,                  -- 图标本地相对路径
    description TEXT,                -- 海克斯详细说明
    level TEXT                       -- 海克斯强化等级 (一级强化符文, 二级强化符文, 三级强化符文)
);
"""

# 2. 羁绊表
SQL_CREATE_TRAITS_TABLE = """
CREATE TABLE IF NOT EXISTS traits (
    name TEXT PRIMARY KEY NOT NULL,  -- 羁绊中文名 (主键)
    en_name TEXT,                    -- 羁绊英文名
    center_icon_url TEXT,            -- 中心图标网络地址
    base_icon_url TEXT,              -- 外框图标网络地址
    center_icon_path TEXT,           -- 中心图标本地相对路径
    base_icon_path TEXT              -- 外框图标本地相对路径
);
"""

# 3. 装备表
SQL_CREATE_ITEMS_TABLE = """
CREATE TABLE IF NOT EXISTS items (
    name TEXT PRIMARY KEY NOT NULL,  -- 装备中文名 (主键)
    en_name TEXT,                    -- 装备英文名 (来自URL)
    tier TEXT,                       -- 评级 (S, A, B, C, D, 未知)
    icon_url TEXT,                   -- 图标网络地址
    icon_path TEXT,                  -- 图标本地相对路径
    avg_placement REAL,              -- 平均排名
    top1_rate REAL,                  -- 登顶率
    play_rate REAL                   -- 出场率
);
"""

# 4. 英雄表
SQL_CREATE_HEROES_TABLE = """
CREATE TABLE IF NOT EXISTS heroes (
    cn_name TEXT PRIMARY KEY NOT NULL, -- 英雄中文名 (主键)
    en_name TEXT,                      -- 英雄英文名
    cost INTEGER,                      -- 费用
    icon_url TEXT,                     -- 图标网络地址
    icon_path TEXT,                    -- 图标本地相对路径
    data_path TEXT,                    -- 详细数据文件相对路径
    play_rate REAL,                    -- 基础出场率 (来自详细数据)
    avg_place REAL,                    -- 基础平均排名 (来自详细数据)
    top4_rate REAL,                    -- 基础前四率 (来自详细数据)
    top1_rate REAL                     -- 基础登顶率 (来自详细数据)
);
"""

# 5. 英雄装备统计表
SQL_CREATE_HERO_ITEM_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS hero_item_stats (
    hero_cn_name TEXT NOT NULL,      -- 英雄中文名
    item_count INTEGER NOT NULL,     -- 装备数量 (1, 2, or 3)
    item_names TEXT NOT NULL,        -- 装备名称组合 (例如 "珠光护手|无尽之刃")
    play_rate REAL,                  -- 该组合的出场率
    avg_place REAL,                  -- 该组合的平均排名
    top4_rate REAL,                  -- 该组合的前四率
    top1_rate REAL,                  -- 该组合的登顶率
    PRIMARY KEY (hero_cn_name, item_names), -- 联合主键
    FOREIGN KEY (hero_cn_name) REFERENCES heroes (cn_name) -- 外键关联英雄表
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 6. 阵容基础信息表
SQL_CREATE_COMPS_BASE_TABLE = """
CREATE TABLE IF NOT EXISTS comps_base (
    name TEXT PRIMARY KEY NOT NULL, -- 阵容中文名 (主键)
    comp_id TEXT,                   -- 阵容API ID
    row_id TEXT,                    -- 阵容网页行ID
    tier TEXT,                      -- 评级 (S, A, B, C, D, 未知)
    avg_placement REAL,             -- 基础平均排名 (来自基础数据)
    frequency REAL,                 -- 基础出场频率 (来自基础数据)
    win_rate REAL,                  -- 基础登顶率 (来自基础数据)
    top4_rate REAL                  -- 基础前四率 (来自基础数据)
);
"""

# 7. 阵容英雄关联表 (多对多) - 添加 hero_order
SQL_CREATE_COMP_HEROES_TABLE = """
CREATE TABLE IF NOT EXISTS comp_heroes (
    comp_name TEXT NOT NULL,        -- 阵容中文名
    hero_cn_name TEXT NOT NULL,     -- 英雄中文名
    hero_order INTEGER NOT NULL,    -- 英雄在该阵容中的显示顺序 (从 0 开始)
    PRIMARY KEY (comp_name, hero_cn_name), -- 保持联合主键，确保唯一性
    -- 可以考虑将主键改为 (comp_name, hero_order)，如果一个英雄不可能在一个阵容出现多次的话
    -- 但当前主键更安全，防止意外重复英雄名
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键关联阵容基础表
        ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (hero_cn_name) REFERENCES heroes (cn_name) -- 外键关联英雄表
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 8. 阵容详细 - 整体统计表
SQL_CREATE_COMP_DETAIL_OVERALL_TABLE = """
CREATE TABLE IF NOT EXISTS comps_detail_overall (
    comp_name TEXT PRIMARY KEY NOT NULL, -- 阵容中文名 (主键)
    total_games INTEGER,                 -- 总对局数
    avg_placement REAL,                  -- 平均排名 (来自API, 可能更精确)
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 9. 阵容详细 - 单位统计表
SQL_CREATE_COMP_DETAIL_UNIT_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS comp_detail_unit_stats (
    comp_name TEXT NOT NULL,             -- 阵容中文名
    unit_id TEXT NOT NULL,               -- 单位ID/英文名?
    appearance_rate REAL,                -- 出场率
    avg_placement REAL,                  -- 平均排名
    total_count INTEGER,                 -- 使用次数
    PRIMARY KEY (comp_name, unit_id),    -- 联合主键
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 10. 阵容详细 - 装备整体统计表
SQL_CREATE_COMP_DETAIL_ITEM_OVERALL_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS comp_detail_item_overall_stats (
    comp_name TEXT NOT NULL,             -- 阵容中文名
    item_id TEXT NOT NULL,               -- 装备ID/名称?
    overall_appearance_rate REAL,        -- 整体出场率
    overall_avg_placement REAL,          -- 整体平均排名
    overall_count INTEGER,               -- 整体使用次数
    PRIMARY KEY (comp_name, item_id),    -- 联合主键
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 11. 阵容详细 - 装备在特定单位统计表
SQL_CREATE_COMP_DETAIL_ITEM_UNIT_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS comp_detail_item_unit_stats (
    comp_name TEXT NOT NULL,             -- 阵容中文名
    item_id TEXT NOT NULL,               -- 装备ID/名称?
    unit_id TEXT NOT NULL,               -- 单位ID/英文名?
    avg_placement_on_unit REAL,          -- 在该单位上时的平均排名
    count_on_unit INTEGER,               -- 在该单位上的使用次数
    unit_pick_rate REAL,                 -- 单位选择率(?) - 需核对数据含义
    item_pick_rate REAL,                 -- 装备选择率(?) - 需核对数据含义
    PRIMARY KEY (comp_name, item_id, unit_id), -- 联合主键
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 12. 阵容详细 - 分等级推荐站位表
SQL_CREATE_COMP_DETAIL_LEVEL_RECS_TABLE = """
CREATE TABLE IF NOT EXISTS comp_detail_level_recs (
    comp_name TEXT NOT NULL,             -- 阵容中文名
    level INTEGER NOT NULL,              -- 等级
    recommendation_index INTEGER NOT NULL, -- 同一阵容同等级的推荐索引 (从0开始)
    units TEXT,                          -- 推荐英雄列表 (存储为 JSON 字符串)
    traits TEXT,                         -- 推荐羁绊列表 (存储为 JSON 字符串)
    avg_placement REAL,                  -- 该推荐的平均排名
    count INTEGER,                       -- 该推荐的出现次数
    PRIMARY KEY (comp_name, level, recommendation_index), -- 联合主键
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) -- 外键
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 13. 装备英雄统计表 - 记录装备在不同英雄上的效果
SQL_CREATE_EQUIPMENT_HERO_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS equipment_hero_stats (
    item_name TEXT NOT NULL,           -- 装备中文名
    hero_cn_name TEXT NOT NULL,        -- 英雄中文名
    play_rate REAL,                    -- 该装备在该英雄上的出场率
    avg_place REAL,                    -- 该装备在该英雄上的平均排名
    top4_rate REAL,                    -- 该装备在该英雄上的前四率 
    top1_rate REAL,                    -- 该装备在该英雄上的登顶率
    PRIMARY KEY (item_name, hero_cn_name), -- 联合主键
    FOREIGN KEY (item_name) REFERENCES items (name) -- 外键关联装备表
        ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (hero_cn_name) REFERENCES heroes (cn_name) -- 外键关联英雄表
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 14. 装备分类表 - 存储装备类别名称
SQL_CREATE_ITEM_CATEGORIES_TABLE = """
CREATE TABLE IF NOT EXISTS item_categories (
    category_name TEXT PRIMARY KEY NOT NULL  -- 装备分类名称 (主键)
);
"""

# 15. 装备分类关联表 - 记录装备与分类的多对多关系
SQL_CREATE_ITEM_CATEGORY_RELATIONS_TABLE = """
CREATE TABLE IF NOT EXISTS item_category_relations (
    item_name TEXT NOT NULL,             -- 装备中文名
    category_name TEXT NOT NULL,         -- 装备分类名称
    PRIMARY KEY (item_name, category_name),  -- 联合主键
    FOREIGN KEY (item_name) REFERENCES items (name)  -- 外键关联装备表
        ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (category_name) REFERENCES item_categories (category_name)  -- 外键关联装备分类表
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# 添加阵容羁绊统计表SQL定义
SQL_CREATE_COMP_DETAIL_TRAIT_STATS_TABLE = """
CREATE TABLE IF NOT EXISTS comp_detail_trait_stats (
    comp_name TEXT NOT NULL,
    trait_id TEXT NOT NULL,
    appearance_rate REAL,
    avg_placement REAL,
    total_count INTEGER,
    tier INTEGER,
    PRIMARY KEY (comp_name, trait_id),
    FOREIGN KEY (comp_name) REFERENCES comps_base (name) ON DELETE CASCADE
)
"""

# 添加羁绊等级表
SQL_CREATE_TRAIT_LEVELS_TABLE = """
CREATE TABLE IF NOT EXISTS trait_levels (
    trait_name TEXT NOT NULL,          -- 羁绊中文名
    level INTEGER NOT NULL,            -- 等级序号 (1, 2, 3, 4)
    level_name TEXT,                   -- 等级名称 (bronze, silver, gold, plat, unique)
    level_number INTEGER,              -- 实际激活所需的羁绊数字
    PRIMARY KEY (trait_name, level),
    FOREIGN KEY (trait_name) REFERENCES traits(name)
        ON DELETE CASCADE ON UPDATE CASCADE
);
"""

# --- 数据导入函数 ---

def import_hexes(conn, data, descriptions_data=None):
    """导入海克斯数据，包括评级和说明"""
    if not data: return
    print("开始导入海克斯数据...")
    cursor = conn.cursor()
    sql = "INSERT OR REPLACE INTO hexes (name, tier, icon_url, icon_path, description, level) VALUES (?, ?, ?, ?, ?, ?)"
    count = 0

    # 创建说明映射字典，便于快速查找
    description_map = {}
    if descriptions_data:
        for level_name, hex_list in descriptions_data.items():
            if isinstance(hex_list, list):
                for hex_item in hex_list:
                    if isinstance(hex_item, dict):
                        hex_name = hex_item.get('name')
                        if hex_name:
                            description_map[hex_name] = {
                                'description': hex_item.get('description', ''),
                                'level': hex_item.get('level', level_name)
                            }

    for name, details in data.items():
        try:
            # 从说明数据中获取描述和等级信息
            desc_info = description_map.get(name, {})
            description = desc_info.get('description', '')
            level = desc_info.get('level', '')

            cursor.execute(sql, (
                sanitize_text(name),
                sanitize_text(details.get('tier_rank')),
                sanitize_text(details.get('icon_url')),
                sanitize_text(details.get('icon_path')),
                sanitize_text(description),
                sanitize_text(level)
            ))
            count += 1
        except sqlite3.Error as e:
            print(f"导入海克斯 {name} 失败: {e}")
        except Exception as e:
             print(f"处理海克斯 {name} 时发生意外错误: {e}")
    conn.commit()
    print(f"成功导入 {count} 条海克斯数据。")

def import_traits(conn, data):
    """导入羁绊数据"""
    if not data: return
    print("开始导入羁绊数据...")
    cursor = conn.cursor()
    sql = """
    INSERT OR REPLACE INTO traits
    (name, en_name, center_icon_url, base_icon_url, center_icon_path, base_icon_path)
    VALUES (?, ?, ?, ?, ?, ?)
    """
    
    # 新增：羁绊等级SQL
    level_sql = """
    INSERT OR REPLACE INTO trait_levels
    (trait_name, level, level_name, level_number)
    VALUES (?, ?, ?, ?)
    """
    
    count = 0
    level_count = 0
    
    for name, details in data.items():
        try:
            cursor.execute(sql, (
                sanitize_text(name),
                sanitize_text(details.get('en_name')),
                sanitize_text(details.get('center_icon_url')),
                sanitize_text(details.get('base_icon_url')),
                sanitize_text(details.get('center_icon_path')),
                sanitize_text(details.get('base_icon_path'))
            ))
            count += 1
            
            # 新增：处理羁绊等级数据
            levels_data = details.get('levels', [])
            if isinstance(levels_data, list):
                for level_data in levels_data:
                    if isinstance(level_data, dict):
                        try:
                            cursor.execute(level_sql, (
                                sanitize_text(name),
                                sanitize_int(level_data.get('level')),
                                sanitize_text(level_data.get('level_name')),
                                sanitize_int(level_data.get('level_number'))
                            ))
                            level_count += 1
                        except sqlite3.Error as e:
                            print(f"导入羁绊 {name} 的等级 {level_data.get('level')} 失败: {e}")
                    
        except sqlite3.Error as e:
            print(f"导入羁绊 {name} 失败: {e}")
        except Exception as e:
             print(f"处理羁绊 {name} 时发生意外错误: {e}")
    conn.commit()
    print(f"成功导入 {count} 条羁绊数据和 {level_count} 条羁绊等级数据。")

def import_items(conn, category_data=None):
    """导入装备数据 (仅使用装备分类映射中的processed_equipment)"""
    if not category_data or not isinstance(category_data, dict) or 'processed_equipment' not in category_data:
        print("错误: 未找到装备分类映射数据或processed_equipment字段")
        print("请确保装备分类映射.json文件存在且包含processed_equipment字段")
        return

    equipment_source = category_data['processed_equipment']
    print("使用装备分类映射的processed_equipment作为装备数据源...")
    print(f"开始导入装备数据...")

    cursor = conn.cursor()
    sql = "INSERT OR REPLACE INTO items (name, en_name, tier, icon_url, icon_path, avg_placement, top1_rate, play_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
    count = 0

    for name, details in equipment_source.items():
        try:
            # --- 标准化 en_name ---
            original_en_name = details.get('en_name')
            standardized_en_name = standardize_item_id(original_en_name)
            # --- 标准化结束 ---

            cursor.execute(sql, (
                sanitize_text(name),
                sanitize_text(standardized_en_name), # <-- 使用标准化后的 en_name
                sanitize_text(details.get('tier')),
                sanitize_text(details.get('icon_url')),
                sanitize_text(details.get('icon_path')),
                sanitize_float(details.get('avg_placement')),
                sanitize_float(details.get('top_1_rate')),
                sanitize_play_rate(details.get('play_rate'))
            ))
            count += 1
        except sqlite3.Error as e:
            print(f"导入装备 {name} 失败: {e}")
        except Exception as e:
             print(f"处理装备 {name} 时发生意外错误: {e}")
    conn.commit()
    print(f"成功导入 {count} 条装备数据。")

    # 导入装备分类数据
    import_item_categories(conn, category_data)

def import_item_categories(conn, category_data):
    """导入装备分类数据"""
    if not category_data: return
    
    # 获取分类数据
    categories = category_data.get('categories', {})
    if not categories:
        print("警告: 装备分类数据为空或格式不正确")
        return
    
    print("开始导入装备分类数据...")
    cursor = conn.cursor()
    
    # 导入分类表
    category_sql = "INSERT OR IGNORE INTO item_categories (category_name) VALUES (?)"
    # 导入分类关联表
    relation_sql = "INSERT OR IGNORE INTO item_category_relations (item_name, category_name) VALUES (?, ?)"
    
    category_count = 0
    relation_count = 0
    
    # 处理每个分类
    for category_name, items in categories.items():
        try:
            # 插入分类
            cursor.execute(category_sql, (sanitize_text(category_name),))
            category_count += 1
            
            # 插入该分类中的所有装备关联
            if isinstance(items, list):
                for item in items:
                    if isinstance(item, dict) and 'name' in item:
                        item_name = sanitize_text(item['name'])
                        try:
                            cursor.execute(relation_sql, (item_name, sanitize_text(category_name)))
                            relation_count += 1
                        except sqlite3.Error as e:
                            print(f"关联装备 {item_name} 到分类 {category_name} 失败: {e}")
        except sqlite3.Error as e:
            print(f"导入装备分类 {category_name} 失败: {e}")
        except Exception as e:
            print(f"处理装备分类 {category_name} 时发生意外错误: {e}")
    
    conn.commit()
    print(f"成功导入 {category_count} 个装备分类和 {relation_count} 条装备分类关联。")

def import_heroes_and_stats(conn, hero_mapping_data):
    """导入英雄基础数据和装备统计数据"""
    if not hero_mapping_data: return
    print("开始导入英雄数据及装备统计...")
    cursor = conn.cursor()
    hero_sql = """
    INSERT OR REPLACE INTO heroes
    (cn_name, en_name, cost, icon_url, icon_path, data_path,
     play_rate, avg_place, top4_rate, top1_rate)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    item_stats_sql = """
    INSERT OR REPLACE INTO hero_item_stats
    (hero_cn_name, item_count, item_names, play_rate, avg_place, top4_rate, top1_rate)
    VALUES (?, ?, ?, ?, ?, ?, ?)
    """
    # 新增: 装备英雄统计数据SQL
    equipment_hero_stats_sql = """
    INSERT OR REPLACE INTO equipment_hero_stats
    (item_name, hero_cn_name, play_rate, avg_place, top4_rate, top1_rate)
    VALUES (?, ?, ?, ?, ?, ?)
    """
    
    hero_count = 0
    stats_count = 0
    equipment_hero_stats_count = 0

    for cn_name, mapping_details in hero_mapping_data.items():
        en_name = mapping_details.get('en_name')
        # 标准化英文名，确保与阵容详情中的ID格式一致
        standardized_en_name = standardize_hero_id(en_name)
        cost = mapping_details.get('cost')
        icon_url = mapping_details.get('icon_url')
        icon_path = mapping_details.get('img_path') # 注意这里是 img_path
        data_path_relative = mapping_details.get('data_path')

        if not data_path_relative:
            print(f"警告: 英雄 {cn_name} 缺少详细数据路径 (data_path)，无法导入基础统计和装备统计。")
            # 仍然可以尝试导入基础信息，如果需要的话
            try:
                 cursor.execute(hero_sql, (
                     sanitize_text(cn_name), sanitize_text(standardized_en_name), sanitize_int(cost),
                     sanitize_text(icon_url), sanitize_text(icon_path), None,
                     None, None, None, None # 基础统计为 None
                 ))
                 hero_count +=1
            except sqlite3.Error as e:
                 print(f"导入英雄 {cn_name} (无详细数据) 基础信息失败: {e}")
            continue

        # 构建详细数据文件的完整路径
        # data_path_full = HERO_DETAIL_DIR / data_path_relative # 这种方式可能出错
        # 正确的方式应该是基于 BASE_DIR
        data_path_full = BASE_DIR / data_path_relative

        hero_detail_data = load_json(data_path_full)

        if not hero_detail_data:
            print(f"警告: 无法加载英雄 {cn_name} 的详细数据文件: {data_path_full}")
            # 仍然可以尝试导入基础信息
            try:
                 cursor.execute(hero_sql, (
                     sanitize_text(cn_name), sanitize_text(standardized_en_name), sanitize_int(cost),
                     sanitize_text(icon_url), sanitize_text(icon_path), sanitize_text(data_path_relative),
                     None, None, None, None # 基础统计为 None
                 ))
                 hero_count +=1
            except sqlite3.Error as e:
                 print(f"导入英雄 {cn_name} (无详细数据) 基础信息失败: {e}")
            continue

        # 提取基础统计数据
        play_rate = sanitize_float(hero_detail_data.get('play_rate'))
        avg_place = sanitize_float(hero_detail_data.get('avg_place'))
        top4_rate = sanitize_float(hero_detail_data.get('top4_rate'))
        top1_rate = sanitize_float(hero_detail_data.get('top1_rate'))

        # 插入英雄基础数据
        try:
            cursor.execute(hero_sql, (
                sanitize_text(cn_name), sanitize_text(standardized_en_name), sanitize_int(cost),
                sanitize_text(icon_url), sanitize_text(icon_path), sanitize_text(data_path_relative),
                play_rate, avg_place, top4_rate, top1_rate
            ))
            hero_count += 1
        except sqlite3.Error as e:
            print(f"导入英雄 {cn_name} 基础数据失败: {e}")
            # 如果英雄基础数据插入失败，后续的装备统计也无法关联，跳过此英雄
            continue
        except Exception as e:
             print(f"处理英雄 {cn_name} 基础数据时发生意外错误: {e}")
             continue


        # 导入装备统计数据
        equip_lists = {
            1: hero_detail_data.get('single_equips', []),
            2: hero_detail_data.get('double_equips', []),
            3: hero_detail_data.get('triple_equips', [])
        }

        for item_count, equips in equip_lists.items():
            if not isinstance(equips, list):
                 print(f"警告: 英雄 {cn_name} 的 {item_count}件套数据格式不正确 (不是列表)。")
                 continue

            for equip_stat in equips:
                if not isinstance(equip_stat, dict):
                    print(f"警告: 英雄 {cn_name} 的 {item_count}件套统计条目格式不正确 (不是字典): {equip_stat}")
                    continue

                item_names_list = []
                if item_count == 1:
                    # 新增: 同时提取单件装备的统计数据用于装备英雄统计表
                    item_name = sanitize_text(equip_stat.get('equip_name'))
                    if item_name:
                        try:
                            cursor.execute(equipment_hero_stats_sql, (
                                item_name,
                                sanitize_text(cn_name),
                                sanitize_float(equip_stat.get('Play rate')),
                                sanitize_float(equip_stat.get('Avg place')),
                                sanitize_float(equip_stat.get('Top 4%')),
                                sanitize_float(equip_stat.get('Top 1%'))
                            ))
                            equipment_hero_stats_count += 1
                        except sqlite3.Error as e:
                            print(f"导入装备 {item_name} 在英雄 {cn_name} 上的统计数据失败: {e}")
                        except Exception as e:
                            print(f"处理装备 {item_name} 在英雄 {cn_name} 上的统计数据时发生意外错误: {e}")
                    
                    item_names_list = [item_name] if item_name else []
                else:
                    names_raw = equip_stat.get('equip_names')
                    if isinstance(names_raw, list):
                         item_names_list = [sanitize_text(name) for name in names_raw]
                    elif isinstance(names_raw, str): # 兼容可能的字符串格式
                         item_names_list = [sanitize_text(name.strip()) for name in names_raw.split('|')] # 假设用 | 分隔

                # 确保列表非空且过滤掉空字符串
                item_names_list = [name for name in item_names_list if name]
                if not item_names_list:
                    print(f"警告: 英雄 {cn_name} 的 {item_count}件套统计缺少装备名称: {equip_stat}")
                    continue

                item_names_str = "|".join(sorted(item_names_list)) # 使用 | 分隔符并排序保证唯一性

                stat_play_rate = sanitize_float(equip_stat.get('Play rate')) # 注意 JSON 里的键名
                stat_avg_place = sanitize_float(equip_stat.get('Avg place'))
                stat_top4_rate = sanitize_float(equip_stat.get('Top 4%'))
                stat_top1_rate = sanitize_float(equip_stat.get('Top 1%'))

                try:
                    cursor.execute(item_stats_sql, (
                        sanitize_text(cn_name),
                        item_count,
                        item_names_str,
                        stat_play_rate,
                        stat_avg_place,
                        stat_top4_rate,
                        stat_top1_rate
                    ))
                    stats_count += 1
                except sqlite3.Error as e:
                    print(f"导入英雄 {cn_name} 装备统计 ({item_names_str}) 失败: {e}")
                except Exception as e:
                     print(f"处理英雄 {cn_name} 装备统计 ({item_names_str}) 时发生意外错误: {e}")


    conn.commit()
    print(f"成功导入 {hero_count} 条英雄基础数据和 {stats_count} 条英雄装备统计数据。")
    print(f"成功导入 {equipment_hero_stats_count} 条装备英雄统计数据。")


def import_comps(conn, comp_base_data, comp_mapping_data):
    """导入阵容基础数据、英雄关联和详细数据 (标准化 item_id)"""
    if not comp_base_data: return
    print("开始导入阵容数据...")
    cursor = conn.cursor()

    # --- 1. 导入阵容基础数据 (Comps_Base) 和 英雄关联 (Comp_Heroes) ---
    comps_base_sql = """
    INSERT OR REPLACE INTO comps_base
    (name, comp_id, row_id, tier, avg_placement, frequency, win_rate, top4_rate)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """
    # 更新插入语句以包含 hero_order
    comp_heroes_sql = "INSERT OR IGNORE INTO comp_heroes (comp_name, hero_cn_name, hero_order) VALUES (?, ?, ?)"
    base_count = 0
    hero_rel_count = 0

    comp_name_to_id_map = {name: details.get('comp_id') for name, details in comp_mapping_data.items()} if comp_mapping_data else {}


    for comp_base in comp_base_data:
         if not isinstance(comp_base, dict):
              print(f"警告: 阵容基础数据条目格式不正确(不是字典): {comp_base}")
              continue
         comp_name = comp_base.get('name')
         if not comp_name:
              print(f"警告: 阵容基础数据缺少名称: {comp_base}")
              continue

         comp_id = comp_base.get('comp_id')
         row_id = comp_base.get('id')
         tier = comp_base.get('tier')
         stats = comp_base.get('stats', {})
         avg_placement = sanitize_float(stats.get('avg_placement'))
         frequency = sanitize_float(stats.get('frequency')) # 通常是百分比
         win_rate = sanitize_float(stats.get('win_rate')) # 登顶率
         top4_rate = sanitize_float(stats.get('top4_rate'))

         # 插入基础表
         try:
              cursor.execute(comps_base_sql, (
                   sanitize_text(comp_name), sanitize_text(comp_id), sanitize_text(row_id), sanitize_text(tier),
                   avg_placement, frequency, win_rate, top4_rate
              ))
              base_count += 1
         except sqlite3.Error as e:
              print(f"导入阵容基础 {comp_name} 失败: {e}")
              continue # 基础信息失败，后续关联也无法进行
         except Exception as e:
              print(f"处理阵容基础 {comp_name} 时发生意外错误: {e}")
              continue

         # 插入英雄关联表 - 使用 enumerate 获取顺序
         heroes = comp_base.get('heroes', [])
         if isinstance(heroes, list):
             for index, hero_cn_name in enumerate(heroes): # 使用 enumerate 获取索引 (顺序)
                 try:
                     # 使用 IGNORE 避免因英雄不存在（虽然理论上不应发生）或重复插入导致错误
                     # 插入 comp_name, hero_cn_name, index (作为 hero_order)
                     cursor.execute(comp_heroes_sql, (sanitize_text(comp_name), sanitize_text(hero_cn_name), index))
                     # 这里无法精确统计插入了多少，因为 IGNORE 会跳过错误
                 except sqlite3.Error as e:
                     # 即使使用 IGNORE，仍可能因外键约束（如果英雄不存在）失败
                     print(f"关联阵容 {comp_name} 和英雄 {hero_cn_name} (顺序 {index}) 失败: {e}")
                 except Exception as e:
                     print(f"处理阵容 {comp_name} 英雄关联 {hero_cn_name} 时发生意外错误: {e}")
             hero_rel_count += len(heroes) # 记录尝试关联的数量

    conn.commit()
    print(f"成功导入 {base_count} 条阵容基础数据，尝试关联 {hero_rel_count} 个英雄。")

    # --- 2. 导入阵容详细数据 (需要读取每个阵容的详细 JSON 文件) ---
    print("开始导入阵容详细数据...")
    comp_detail_overall_sql = "INSERT OR REPLACE INTO comps_detail_overall (comp_name, total_games, avg_placement) VALUES (?, ?, ?)"
    comp_detail_unit_sql = "INSERT OR REPLACE INTO comp_detail_unit_stats (comp_name, unit_id, appearance_rate, avg_placement, total_count) VALUES (?, ?, ?, ?, ?)"
    comp_detail_item_overall_sql = "INSERT OR REPLACE INTO comp_detail_item_overall_stats (comp_name, item_id, overall_appearance_rate, overall_avg_placement, overall_count) VALUES (?, ?, ?, ?, ?)"
    comp_detail_item_unit_sql = "INSERT OR REPLACE INTO comp_detail_item_unit_stats (comp_name, item_id, unit_id, avg_placement_on_unit, count_on_unit, unit_pick_rate, item_pick_rate) VALUES (?, ?, ?, ?, ?, ?, ?)"
    comp_detail_level_recs_sql = "INSERT OR REPLACE INTO comp_detail_level_recs (comp_name, level, recommendation_index, units, traits, avg_placement, count) VALUES (?, ?, ?, ?, ?, ?, ?)"
    # 新增羁绊统计SQL
    comp_detail_trait_sql = "INSERT OR REPLACE INTO comp_detail_trait_stats (comp_name, trait_id, appearance_rate, avg_placement, total_count, tier) VALUES (?, ?, ?, ?, ?, ?)"

    detail_overall_count = 0
    detail_unit_count = 0
    detail_item_overall_count = 0
    detail_item_unit_count = 0
    detail_level_recs_count = 0
    detail_trait_count = 0  # 新增羁绊统计计数器

    processed_comp_names = set() # 避免重复处理

    # 优先使用 comp_base_data 中的名称列表，因为这代表实际爬取到的基础阵容
    comp_names_to_process = [comp.get('name') for comp in comp_base_data if isinstance(comp, dict) and comp.get('name')]

    for comp_name in comp_names_to_process:
        if comp_name in processed_comp_names:
             continue
        processed_comp_names.add(comp_name)

        # 构建详细数据文件的路径
        # 需要对 comp_name 进行文件名清理
        safe_comp_name = "".join([c if c.isalnum() or c in [' ', '.', '_', '-'] else '_' for c in comp_name])
        detail_file_path = COMP_DETAIL_DIR / f"{safe_comp_name}.json"

        comp_detail = load_json(detail_file_path)
        if not comp_detail:
            print(f"警告: 未找到或无法加载阵容 {comp_name} 的详细数据文件: {detail_file_path}")
            continue

        try:
            # 导入整体统计
            overall_stats = comp_detail.get('overall_comp_stats', {})
            if overall_stats:
                cursor.execute(comp_detail_overall_sql, (
                    sanitize_text(comp_name),
                    sanitize_int(overall_stats.get('total_games')),
                    sanitize_float(overall_stats.get('avg_placement'))
                ))
                detail_overall_count += 1

            # 导入单位统计
            unit_stats = comp_detail.get('unit_overall_stats', [])
            if isinstance(unit_stats, list):
                 for unit_stat in unit_stats:
                      if isinstance(unit_stat, dict):
                           # 使用新的标准化函数处理整个单位统计数据
                           standardized_unit_stat = standardize_heroes_in_data(unit_stat)
                           unit_id = standardized_unit_stat.get('unit_id')
                           cursor.execute(comp_detail_unit_sql, (
                                sanitize_text(comp_name),
                                sanitize_text(unit_id),
                                sanitize_float(standardized_unit_stat.get('appearance_rate')),
                                sanitize_float(standardized_unit_stat.get('avg_placement')),
                                sanitize_int(standardized_unit_stat.get('total_count'))
                           ))
                           detail_unit_count += 1
            
            # 新增：导入羁绊统计
            trait_stats = comp_detail.get('trait_overall_stats', [])
            if isinstance(trait_stats, list):
                for trait_stat in trait_stats:
                    if isinstance(trait_stat, dict):
                        # 标准化羁绊ID
                        original_trait_id = trait_stat.get('trait_id')
                        standardized_trait_id = standardize_trait_id(original_trait_id)
                        
                        if not standardized_trait_id:
                            print(f"警告: 阵容 {comp_name} 的羁绊统计中发现无效或空的 trait_id: {original_trait_id}")
                            continue
                            
                        cursor.execute(comp_detail_trait_sql, (
                            sanitize_text(comp_name),
                            standardized_trait_id, # 使用标准化后的ID
                            sanitize_float(trait_stat.get('appearance_rate')),
                            sanitize_float(trait_stat.get('avg_placement')),
                            sanitize_int(trait_stat.get('total_count')),
                            sanitize_int(trait_stat.get('tier'))
                        ))
                        detail_trait_count += 1

            # 导入装备整体统计 和 装备在单位上的统计
            item_stats = comp_detail.get('item_overall_stats', [])
            if isinstance(item_stats, list):
                for item_stat in item_stats:
                    if isinstance(item_stat, dict):
                        # --- 标准化 item_id --- 
                        original_item_id = item_stat.get('item_id')
                        standardized_item_id = standardize_item_id(original_item_id)
                        if not standardized_item_id:
                             print(f"警告: 阵容 {comp_name} 装备整体统计中发现无效 item_id: {original_item_id}")
                             continue # 跳过无效 ID
                        # --- 标准化结束 --- 

                        cursor.execute(comp_detail_item_overall_sql, (
                            sanitize_text(comp_name),
                            standardized_item_id, # <-- 使用标准化后的 item_id
                            sanitize_float(item_stat.get('overall_appearance_rate')),
                            sanitize_float(item_stat.get('overall_avg_placement')),
                            sanitize_int(item_stat.get('overall_count'))
                        ))
                        detail_item_overall_count += 1

                        # 导入装备在单位上的统计
                        unit_specific_stats = item_stat.get('unit_specific_stats', [])
                        if isinstance(unit_specific_stats, list):
                            for unit_spec_stat in unit_specific_stats:
                                if isinstance(unit_spec_stat, dict):
                                    standardized_unit_spec = standardize_heroes_in_data(unit_spec_stat)
                                    unit_id = standardized_unit_spec.get('unit_id')
                                    # 同样需要使用标准化的 item_id 进行插入
                                    cursor.execute(comp_detail_item_unit_sql, (
                                        sanitize_text(comp_name),
                                        standardized_item_id, # <-- 使用标准化后的 item_id
                                        sanitize_text(unit_id),
                                        sanitize_float(standardized_unit_spec.get('avg_placement_on_unit')),
                                        sanitize_int(standardized_unit_spec.get('count_on_unit')),
                                        sanitize_float(standardized_unit_spec.get('unit_pick_rate')),
                                        sanitize_float(standardized_unit_spec.get('item_pick_rate'))
                                    ))
                                    detail_item_unit_count += 1

            # 导入分等级推荐站位
            level_recs = comp_detail.get('recommended_compositions_by_level', {})
            if isinstance(level_recs, dict):
                for level_str, recs_list in level_recs.items():
                    try:
                        level_int = int(level_str)
                    except ValueError:
                        print(f"警告: 阵容 {comp_name} 推荐站位的等级 '{level_str}' 不是有效数字，跳过。")
                        continue

                    if isinstance(recs_list, list):
                        for index, rec in enumerate(recs_list):
                             if isinstance(rec, dict):
                                  # 使用新的递归函数处理整个推荐数据结构
                                  standardized_rec = standardize_heroes_in_data(rec)
                                  units_list = standardized_rec.get('units', [])
                                  traits_list = standardized_rec.get('traits', [])

                                  # --- 新增：标准化推荐中的羁绊ID ---
                                  standardized_traits_list = []
                                  if isinstance(traits_list, list):
                                      for trait_info in traits_list:
                                          if isinstance(trait_info, dict):
                                              original_trait_id = trait_info.get('trait_id')
                                              standardized_trait_id = standardize_trait_id(original_trait_id)
                                              if standardized_trait_id:
                                                  # 复制字典并更新trait_id
                                                  updated_trait_info = trait_info.copy()
                                                  updated_trait_info['trait_id'] = standardized_trait_id
                                                  standardized_traits_list.append(updated_trait_info)
                                              else:
                                                  print(f"警告: 阵容 {comp_name} 等级 {level_int} 推荐 {index} 中发现无效羁绊ID: {original_trait_id}")
                                                  standardized_traits_list.append(trait_info) # 保留原始信息以防万一
                                          else:
                                               standardized_traits_list.append(trait_info) # 保留非字典项
                                  else:
                                      standardized_traits_list = traits_list # 如果不是列表，保留原样
                                  # --- 标准化结束 ---
                                  
                                  # 将列表转换为 JSON 字符串存储
                                  units_json = json.dumps(units_list, ensure_ascii=False)
                                  # 使用标准化后的羁绊列表
                                  traits_json = json.dumps(standardized_traits_list, ensure_ascii=False)

                                  cursor.execute(comp_detail_level_recs_sql, (
                                       sanitize_text(comp_name),
                                       level_int,
                                       index, # 使用列表索引作为 recommendation_index
                                       units_json,
                                       traits_json,
                                       sanitize_float(rec.get('avg_placement')),
                                       sanitize_int(rec.get('count'))
                                  ))
                                  detail_level_recs_count += 1

        except sqlite3.Error as e:
            print(f"导入阵容详细数据 {comp_name} 失败: {e}")
        except Exception as e:
            print(f"处理阵容详细数据 {comp_name} 时发生意外错误: {e}")
            traceback.print_exc() # 打印详细堆栈

    conn.commit()
    print(f"成功导入 {detail_overall_count} 条阵容整体统计。")
    print(f"成功导入 {detail_unit_count} 条阵容单位统计。")
    print(f"成功导入 {detail_trait_count} 条阵容羁绊统计。")  # 新增羁绊统计输出
    print(f"成功导入 {detail_item_overall_count} 条阵容装备整体统计。")
    print(f"成功导入 {detail_item_unit_count} 条阵容装备单位统计。")
    print(f"成功导入 {detail_level_recs_count} 条阵容分等级推荐。")


# --- 主函数 ---

def build_database():
    """构建完整的数据库"""
    print("开始构建数据库...")
    start_time = time.time()

    # 1. 删除旧数据库
    delete_database()

    # 2. 创建连接
    conn = create_connection()

    # 3. 创建所有表
    print("开始创建数据库表...")
    create_table(conn, SQL_CREATE_HEXES_TABLE)
    create_table(conn, SQL_CREATE_TRAITS_TABLE)
    create_table(conn, SQL_CREATE_TRAIT_LEVELS_TABLE)  # 添加这一行
    create_table(conn, SQL_CREATE_ITEMS_TABLE)
    create_table(conn, SQL_CREATE_HEROES_TABLE) # 必须先创建英雄表
    create_table(conn, SQL_CREATE_HERO_ITEM_STATS_TABLE)
    create_table(conn, SQL_CREATE_COMPS_BASE_TABLE) # 必须先创建阵容基础表
    create_table(conn, SQL_CREATE_COMP_HEROES_TABLE)
    create_table(conn, SQL_CREATE_COMP_DETAIL_OVERALL_TABLE)
    create_table(conn, SQL_CREATE_COMP_DETAIL_UNIT_STATS_TABLE)
    create_table(conn, SQL_CREATE_COMP_DETAIL_TRAIT_STATS_TABLE)  # 新增羁绊统计表
    create_table(conn, SQL_CREATE_COMP_DETAIL_ITEM_OVERALL_STATS_TABLE)
    create_table(conn, SQL_CREATE_COMP_DETAIL_ITEM_UNIT_STATS_TABLE)
    create_table(conn, SQL_CREATE_COMP_DETAIL_LEVEL_RECS_TABLE)
    create_table(conn, SQL_CREATE_EQUIPMENT_HERO_STATS_TABLE) # 新增: 创建装备英雄统计表
    create_table(conn, SQL_CREATE_ITEM_CATEGORIES_TABLE) # 新增: 创建装备分类表
    create_table(conn, SQL_CREATE_ITEM_CATEGORY_RELATIONS_TABLE) # 新增: 创建装备分类关联表
    print("数据库表创建完成。")

    # --- 3.1 创建索引 (提升查询性能) ---
    print("开始创建/优化索引...")
    cursor = conn.cursor()
    index_commands = [
        # comps_base: tier 用于分组和排序
        "DROP INDEX IF EXISTS idx_comps_base_tier;",
        "CREATE INDEX IF NOT EXISTS idx_comps_base_tier_freq ON comps_base (tier, frequency DESC);",
        # comp_heroes: comp_name 用于 JOIN 和查找, hero_order 用于排序
        "CREATE INDEX IF NOT EXISTS idx_comp_heroes_comp_name ON comp_heroes (comp_name);",
        "CREATE INDEX IF NOT EXISTS idx_comp_heroes_comp_order ON comp_heroes (comp_name, hero_order);",
        # heroes: en_name 可能用于 JOIN
        "CREATE INDEX IF NOT EXISTS idx_heroes_en_name ON heroes (en_name);",
        # items: en_name 可能用于 JOIN
        "CREATE INDEX IF NOT EXISTS idx_items_en_name ON items (en_name);",
        # comp_detail_level_recs: comp_name 和 level 常用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_level_recs_comp_level ON comp_detail_level_recs (comp_name, level);",
        # comp_detail_unit_stats: comp_name 常用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_unit_stats_comp ON comp_detail_unit_stats (comp_name);",
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_unit_stats_unit ON comp_detail_unit_stats (unit_id);", # unit_id 也可能被查
        # comp_detail_item_overall_stats: comp_name 常用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_item_overall_stats_comp ON comp_detail_item_overall_stats (comp_name);",
        # comp_detail_item_unit_stats: comp_name, item_id 常用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_item_unit_stats_comp_item ON comp_detail_item_unit_stats (comp_name, item_id);",
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_item_unit_stats_comp_unit ON comp_detail_item_unit_stats (comp_name, unit_id);", # comp_name 和 unit_id 也可能一起查
        # comp_detail_trait_stats: comp_name 用于 WHERE, trait_id 用于 JOIN
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_trait_stats_comp ON comp_detail_trait_stats (comp_name);",
        "CREATE INDEX IF NOT EXISTS idx_comp_detail_trait_stats_trait ON comp_detail_trait_stats (trait_id);",
        # equipment_hero_stats: item_name 和 hero_cn_name 都可能用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_equipment_hero_stats_item ON equipment_hero_stats (item_name);",
        "CREATE INDEX IF NOT EXISTS idx_equipment_hero_stats_hero ON equipment_hero_stats (hero_cn_name);",
        # hero_item_stats: hero_cn_name 常用于 WHERE
        "CREATE INDEX IF NOT EXISTS idx_hero_item_stats_hero ON hero_item_stats (hero_cn_name);",
        # item_category_relations: item_name 和 category_name 都可能用于 JOIN 或 WHERE
        "CREATE INDEX IF NOT EXISTS idx_item_category_relations_item ON item_category_relations (item_name);",
        "CREATE INDEX IF NOT EXISTS idx_item_category_relations_category ON item_category_relations (category_name);",
        # 新增 heroes cost+cn_name 索引
        "CREATE INDEX IF NOT EXISTS idx_heroes_cost_cn_name ON heroes (cost, cn_name);",
        # 新增 items 排序索引
        "CREATE INDEX IF NOT EXISTS idx_items_tier ON items (tier);",
        "CREATE INDEX IF NOT EXISTS idx_items_play_rate ON items (play_rate);",
        "CREATE INDEX IF NOT EXISTS idx_items_avg_placement ON items (avg_placement);",
        "CREATE INDEX IF NOT EXISTS idx_items_top1_rate ON items (top1_rate);",
        # 新增 hexes tier 索引
        "CREATE INDEX IF NOT EXISTS idx_hexes_tier ON hexes (tier);",
        # 新增 trait_levels 索引
        "CREATE INDEX IF NOT EXISTS idx_trait_levels_trait_name ON trait_levels (trait_name);",
        "CREATE INDEX IF NOT EXISTS idx_trait_levels_level_name ON trait_levels (level_name);",
    ]
    index_count = 0
    for sql in index_commands:
        try:
            cursor.execute(sql)
            index_count += 1
        except sqlite3.Error as e:
            print(f"创建索引失败: {e}\nSQL: {sql}")
    conn.commit()
    print(f"成功创建 {index_count} 个索引。")

    # 4. 加载映射和基础数据文件
    print("开始加载 JSON 数据文件...")
    hex_mapping = load_json(HEX_MAPPING_FILE)
    hex_descriptions = load_json(HEX_DESCRIPTIONS_FILE) # 加载海克斯说明数据
    if not hex_descriptions:
        print("警告: 海克斯说明数据文件未找到，将使用空的说明数据")
        hex_descriptions = {}
    trait_mapping = load_json(TRAIT_MAPPING_FILE)
    # 移除装备图标映射文件加载 - 现在统一使用装备分类映射
    # item_mapping = load_json(ITEM_MAPPING_FILE)  # 已废弃
    hero_mapping = load_json(HERO_MAPPING_FILE)
    comp_base_data = load_json(COMP_BASE_DATA_FILE)
    comp_mapping = load_json(COMP_MAPPING_FILE)
    item_category_data = load_json(ITEM_CATEGORY_FILE) # 装备分类数据 - 现在是装备数据的唯一来源
    print("JSON 数据文件加载完成。")

    # 5. 导入数据到数据库
    # 注意导入顺序，有外键关联的表需要后导入
    import_hexes(conn, hex_mapping, hex_descriptions) # 传入海克斯说明数据
    import_traits(conn, trait_mapping)
    import_items(conn, item_category_data) # 仅使用装备分类数据
    # 英雄数据必须在阵容英雄关联之前导入
    import_heroes_and_stats(conn, hero_mapping)
    # 阵容基础和详细数据
    import_comps(conn, comp_base_data, comp_mapping)


    # 6. 关闭连接
    if conn:
        conn.close()
        print("数据库连接已关闭。")

    end_time = time.time()
    print(f"\n数据库构建完成！总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    build_database()