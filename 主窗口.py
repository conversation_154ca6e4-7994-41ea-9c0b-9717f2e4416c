# 主窗口.py

import sys
import traceback
import gc
import subprocess
import os
from pathlib import Path
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QFrame, QPushButton, QStackedWidget, QLabel, QButtonGroup,
                               QSizePolicy)
from PySide6.QtGui import QIcon, QCursor, QMouseEvent, QPainter, QColor, QPen, QPixmap
from PySide6.QtCore import Qt, QSize, QPoint, Slot, QRectF, QTimer

# --- 导入自定义模块 ---
from 常量与配置 import (WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT, CONTROL_BAR_HEIGHT, NAV_BAR_HEIGHT,
                       WINDOW_BORDER_COLOR, WINDOW_BG_COLOR, CONTROL_BAR_BG, BORDER_COLOR,
                       BUTTON_STYLE_BASE, NAV_BUTTON_STYLE, TEXT_COLOR_MEDIUM, TIER_COLORS,
                       TEXT_COLOR_LIGHT, TOOLTIP_STYLE,
                       GLOBAL_HERO_INFO_MAP, GLOBAL_TRAIT_ICON_MAP, GLOBAL_HERO_BASE_STATS, GLOBAL_ITEM_INFO_MAP,
                       MEMORY_INTENSIVE_VIEWS, ENABLE_VIEW_UNLOADING, VIEW_UNLOAD_DELAY_MS,
                       RESOURCE_CLEANUP_INTERVAL_MS)
# 导入所有视图模块
from 视图模块.阵容列表视图 import 阵容列表视图
from 视图模块.英雄列表视图 import 英雄列表视图
from 视图模块.装备列表视图 import 装备列表视图
from 视图模块.海克斯列表视图 import 海克斯列表视图
from 视图模块.阵容详情视图 import 阵容详情视图
from 视图模块.英雄详情视图 import 英雄详情视图
from 视图模块.装备详情视图 import 装备详情视图
# 导入数据库检查
from 数据库操作 import DB_FILE, execute_query_async

# --- 自定义指示器 Widget (修正箭头方向) ---
class ToggleIndicator(QWidget):
    """用于显示展开/收起状态的指示器"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(20, 10)
        self._is_minimized = False

    def set_minimized(self, minimized):
        self._is_minimized = minimized
        self.update()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setPen(QPen(QColor(TEXT_COLOR_MEDIUM), 2, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap))
        center_x = self.width() / 2

        # --- 修正箭头方向 ---
        if self._is_minimized:
            # 收起状态，显示向上箭头 (展开)
            painter.drawLine(center_x - 5, self.height() - 2, center_x, 2)
            painter.drawLine(center_x, 2, center_x + 5, self.height() - 2)
        else:
            # 展开状态，显示向下箭头 (收起)
            painter.drawLine(center_x - 5, 2, center_x, self.height() - 2)
            painter.drawLine(center_x, self.height() - 2, center_x + 5, 2)


class MainWindow(QMainWindow):
    """应用程序主窗口"""
    # --- 页面索引常量 (保持不变) ---
    PAGE_COMP_LIST = 0
    PAGE_HERO_LIST = 1
    PAGE_ITEM_LIST = 2
    PAGE_HEX_LIST = 3
    PAGE_COMP_DETAIL = 4
    PAGE_HERO_DETAIL = 5
    PAGE_ITEM_DETAIL = 6

    # --- 新增：定义收起后的高度 ---
    MINIMIZED_HEIGHT = CONTROL_BAR_HEIGHT + 2 # 控制条高度再加一点点

    # --- 新增：定义返回按钮宽度 ---
    STANDARD_BACK_BUTTON_WIDTH = 30
    WIDE_BACK_BUTTON_WIDTH = 50 # 返回按钮可见时的宽度

    def __init__(self):
        super().__init__()
        self.setWindowTitle("云顶之弈助手 (TFT Helper)")
        # --- 修改：初始最小高度设为收起后的高度 ---
        self.setMinimumSize(WINDOW_DEFAULT_WIDTH, self.MINIMIZED_HEIGHT)
        self.resize(WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT)
        self._drag_pos = None
        self._press_pos = None
        self._is_dragging = False
        self.is_minimized = False
        self.history_stack = []
        self._views_loaded = { # 跟踪视图数据是否已加载
            self.PAGE_COMP_LIST: False,
            self.PAGE_HERO_LIST: False,
            self.PAGE_ITEM_LIST: False,
            self.PAGE_HEX_LIST: False,
        }
        
        # --- 新增：资源管理变量 ---
        self._last_visible_page = None  # 上一个可见页面
        self._resource_cleanup_timer = None  # 资源清理定时器
        self._view_unload_timers = {}  # 视图卸载定时器 {page_index: QTimer}
        
        # --- 新增：OCR查询进程变量 ---
        self.ocr_process = None

        if not DB_FILE.exists():
             print(f"错误: 数据库文件 {DB_FILE} 未找到！请先运行 数据库建立.py")
             error_widget = QLabel(f"错误: 数据库文件\n{DB_FILE}\n未找到！\n请先运行数据库建立脚本。", alignment=Qt.AlignmentFlag.AlignCenter)
             error_widget.setStyleSheet(f"background-color:{WINDOW_BG_COLOR}; color:red; font-size:14px; padding: 20px;")
             self.setCentralWidget(error_widget)
             self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)
             self.setStyleSheet(f"QMainWindow{{background-color:{WINDOW_BORDER_COLOR};}}")
             return

        self.init_ui()
        self._setup_resource_management()
        self._start_global_preloading()
        self.switch_main_page(self.PAGE_COMP_LIST)
        
        # 启动OCR查询
        self._start_ocr_query()

    def init_ui(self):
        """初始化主窗口界面"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setStyleSheet(f"QMainWindow{{background-color:{WINDOW_BORDER_COLOR};}}")

        container_widget = QWidget(self)
        container_widget.setObjectName("ContainerWidget")
        # --- 修改：主容器圆角只保留底部 ---
        container_widget.setStyleSheet(f"""
            #ContainerWidget {{
                background-color:{WINDOW_BG_COLOR};
                border-bottom-left-radius: 10px;
                border-bottom-right-radius: 10px;
                border-top-left-radius: 0px; /* 顶部无圆角 */
                border-top-right-radius: 0px;
            }}
        """)
        self.setCentralWidget(container_widget)

        self.overall_layout = QVBoxLayout(container_widget)
        # --- 修改：主容器顶部无边距，让控制条紧贴顶部 ---
        self.overall_layout.setContentsMargins(8, 0, 8, 8) # 上边距为0
        self.overall_layout.setSpacing(0)

        self.control_bar = self._create_control_bar()
        self.overall_layout.addWidget(self.control_bar)

        # --- 修改：导航栏现在是可选显示的 ---
        self.nav_bar = self._create_nav_bar()
        self.overall_layout.addWidget(self.nav_bar)

        # --- 修改：StackedWidget 现在也是可选显示的 ---
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setStyleSheet("background-color:transparent;")
        self.overall_layout.addWidget(self.stacked_widget, 1) # 仍然让它占据主要空间

        self._create_and_add_views()
        self._connect_view_signals()


    def _create_control_bar(self):
        """创建顶部控制条 (样式微调)"""
        control_bar = QFrame()
        control_bar.setFixedHeight(CONTROL_BAR_HEIGHT)
        # --- 修改：移除顶部圆角，因为主容器已经没有顶部圆角了 ---
        control_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {CONTROL_BAR_BG};
                border-bottom: 1px solid {BORDER_COLOR}; /* 保留底部分割线 */
            }}
        """)
        control_layout = QHBoxLayout(control_bar)
        control_layout.setContentsMargins(8, 0, 8, 0)
        control_layout.setSpacing(8)

        button_size = QSize(30, 28)
        self.back_button = QPushButton("<")
        # --- 修改：使用标准宽度初始化 ---
        self.back_button.setFixedWidth(self.STANDARD_BACK_BUTTON_WIDTH)
        self.back_button.setFixedHeight(button_size.height()) # 保持高度一致
        self.back_button.setToolTip("返回上一页")
        self.back_button.setStyleSheet(BUTTON_STYLE_BASE)
        self.back_button.setVisible(False)
        self.back_button.clicked.connect(self.go_back)
        control_layout.addWidget(self.back_button)

        control_layout.addStretch(1)
        self.toggle_indicator = ToggleIndicator(control_bar)
        self.toggle_indicator.setToolTip("展开/收起窗口")
        control_layout.addWidget(self.toggle_indicator, 0, Qt.AlignmentFlag.AlignCenter)
        control_layout.addStretch(1)

        self.close_button = QPushButton("X")
        self.close_button.setFixedSize(button_size)
        self.close_button.setToolTip("关闭")
        close_button_style = BUTTON_STYLE_BASE + f"""
            QPushButton {{ color: #FF7B72; }}
            QPushButton:hover {{ background-color: #C7463F; }}
        """
        self.close_button.setStyleSheet(close_button_style)
        self.close_button.clicked.connect(self.close)
        control_layout.addWidget(self.close_button)

        return control_bar

    # _create_nav_bar, _create_and_add_views, _connect_view_signals 方法保持不变...
    def _create_nav_bar(self):
        """创建主导航栏"""
        nav_bar = QFrame()
        nav_bar.setFixedHeight(NAV_BAR_HEIGHT)
        # 设置背景色和底部边框
        nav_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {CONTROL_BAR_BG};
                border-bottom: 1px solid {BORDER_COLOR};
            }}
        """)
        nav_layout = QHBoxLayout(nav_bar)
        nav_layout.setContentsMargins(10, 0, 10, 0) # 左右边距
        nav_layout.setSpacing(5) # 导航按钮间距

        self.nav_buttons = {} # 存储导航按钮 {page_index: button_widget}
        self.nav_button_group = QButtonGroup(self) # 使用按钮组确保单选
        self.nav_button_group.setExclusive(True)

        # 定义导航项 (名称, 页面索引)
        nav_items = [
            ("阵容", self.PAGE_COMP_LIST),
            ("英雄", self.PAGE_HERO_LIST),
            ("装备", self.PAGE_ITEM_LIST),
            ("海克斯", self.PAGE_HEX_LIST)
        ]

        for name, index in nav_items:
            btn = QPushButton(name)
            btn.setStyleSheet(NAV_BUTTON_STYLE) # 应用导航按钮样式
            btn.setCheckable(True) # 设置为可选中
            btn.clicked.connect(lambda checked=False, page_index=index: self.switch_main_page(page_index))
            nav_layout.addWidget(btn)
            self.nav_buttons[index] = btn
            self.nav_button_group.addButton(btn, index) # 添加到按钮组

        nav_layout.addStretch(1) # 将按钮推到左侧
        return nav_bar

    def _create_and_add_views(self):
        """创建所有视图实例并添加到 StackedWidget"""
        print("开始创建视图实例...")
        self.views = {
            self.PAGE_COMP_LIST: 阵容列表视图(),
            self.PAGE_HERO_LIST: 英雄列表视图(),
            self.PAGE_ITEM_LIST: 装备列表视图(),
            self.PAGE_HEX_LIST: 海克斯列表视图(),
            self.PAGE_COMP_DETAIL: 阵容详情视图(),
            self.PAGE_HERO_DETAIL: 英雄详情视图(),
            self.PAGE_ITEM_DETAIL: 装备详情视图(),
        }

        for index, view_widget in self.views.items():
            self.stacked_widget.addWidget(view_widget)
            print(f"视图已添加: Index={index}, Widget={type(view_widget).__name__}")

    def _connect_view_signals(self):
        """连接视图发出的信号到主窗口的槽函数"""
        # 列表页跳转详情页
        self.views[self.PAGE_COMP_LIST].comp_selected.connect(self.show_comp_detail)
        self.views[self.PAGE_HERO_LIST].hero_selected.connect(self.show_hero_detail)
        self.views[self.PAGE_ITEM_LIST].item_selected.connect(self.show_item_detail)
        # 海克斯列表无点击交互

        # 详情页内部跳转
        # 阵容详情 -> 英雄详情 / 装备详情
        self.views[self.PAGE_COMP_DETAIL].hero_selected_from_detail.connect(self.show_hero_detail)
        self.views[self.PAGE_COMP_DETAIL].item_selected_from_detail.connect(self.show_item_detail)
        # 英雄详情 -> 装备详情
        self.views[self.PAGE_HERO_DETAIL].item_selected.connect(self.show_item_detail)
        # 装备详情 -> 英雄详情
        self.views[self.PAGE_ITEM_DETAIL].hero_selected.connect(self.show_hero_detail)
        print("视图信号已连接。")


    # --- 导航逻辑 (switch_main_page, record_navigation, go_back, update_back_button, _navigate_to_detail) ---
    # 这些方法保持不变...
    @Slot(int) # 明确这是一个接收整数参数的槽函数
    def switch_main_page(self, index):
        """切换主导航页面"""
        print(f"切换到主页面: Index={index}")
        if index >= self.stacked_widget.count():
             print(f"错误：无效的页面索引 {index}")
             return
        
        # 获取当前页面以安排卸载
        current_page = self.stacked_widget.currentIndex()
        
        # 如果之前的页面是内存密集型页面，安排它的资源卸载
        if current_page in MEMORY_INTENSIVE_VIEWS and current_page != index:
            self._schedule_view_unload(current_page)

        self.stacked_widget.setCurrentIndex(index)
        self._last_visible_page = index  # 更新最后可见页面
        
        # 更新导航按钮选中状态
        button_to_check = self.nav_button_group.button(index)
        if button_to_check:
            button_to_check.setChecked(True)
        else:
             # 如果索引不在按钮组中（例如详情页），取消所有按钮选中
             current_checked = self.nav_button_group.checkedButton()
             if current_checked:
                  # 设置 autoExclusive 为 False 临时允许取消选中，然后再恢复
                  self.nav_button_group.setExclusive(False)
                  current_checked.setChecked(False)
                  self.nav_button_group.setExclusive(True)

        # 主页面切换时清空历史记录并更新返回按钮
        is_main_page = index in [self.PAGE_COMP_LIST, self.PAGE_HERO_LIST, self.PAGE_ITEM_LIST, self.PAGE_HEX_LIST]
        if is_main_page:
            self.history_stack = []
            
            # --- 新增：为主页面重置状态 ---
            target_view = self.views.get(index)
            if target_view and hasattr(target_view, 'reset_to_initial_state'):
                print(f"重置主页面 {index} 到初始状态")
                target_view.reset_to_initial_state()
            
        self.update_back_button()

        # 确保导航栏可见 (如果窗口未收起)
        if not self.is_minimized:
            self.nav_bar.setVisible(True)
            # self.toggle_button.setVisible(True) # 不再需要单独的按钮

        # --- 修改：延迟加载视图数据，优先调用 load_data_if_needed ---
        if index in self._views_loaded and not self._views_loaded[index]:
            view_widget = self.views.get(index)
            if view_widget:
                print(f"首次加载视图 {index} 的数据...")
                if hasattr(view_widget, 'load_data_if_needed'): # 优先调用新方法
                    print(f"调用视图 {index} 的 load_data_if_needed()...")
                    view_widget.load_data_if_needed()
                elif hasattr(view_widget, 'load_data'): # 否则调用旧方法
                    print(f"调用视图 {index} 的 load_data()...")
                    view_widget.load_data()
                else:
                    print(f"警告: 视图 {index} ({type(view_widget).__name__}) 没有找到 load_data 或 load_data_if_needed 方法。")
                self._views_loaded[index] = True # 标记为已尝试加载 (不一定是成功加载)
        # --- 延迟加载结束 ---
        
        # 切换页面后执行一次资源清理
        gc.collect()

    def record_navigation(self, page_index, data_key):
        """记录导航历史"""
        current_state = (page_index, data_key)
        # 避免连续记录完全相同的状态
        if not self.history_stack or self.history_stack[-1] != current_state:
            self.history_stack.append(current_state)
            print(f"导航历史记录: {current_state}, 当前栈深度: {len(self.history_stack)}")
        self.update_back_button()

    @Slot() # 返回按钮的槽函数
    def go_back(self):
        """返回上一级页面"""
        if not self.history_stack:
            print("历史记录为空，无法返回。")
            return

        # 获取当前页面信息，用于确定应该返回到哪个列表页
        current_page = self.stacked_widget.currentIndex()

        # 弹出当前页面状态 (实际要显示的是上一个状态)
        self.history_stack.pop()
        print(f"执行返回，当前栈深度: {len(self.history_stack)}")

        if not self.history_stack:
            # 如果弹出后历史为空，应该返回到对应的主列表页
            if current_page == self.PAGE_HERO_DETAIL:
                print("从英雄详情返回到英雄列表页")
                self.switch_main_page(self.PAGE_HERO_LIST)
            elif current_page == self.PAGE_ITEM_DETAIL:
                print("从装备详情返回到装备列表页")
                self.switch_main_page(self.PAGE_ITEM_LIST)
            elif current_page == self.PAGE_COMP_DETAIL:
                print("从阵容详情返回到阵容列表页")
                self.switch_main_page(self.PAGE_COMP_LIST)
            else:
                # 其他情况默认返回阵容列表
                print("默认返回到阵容列表页")
                self.switch_main_page(self.PAGE_COMP_LIST)
        else:
            # 加载上一个状态
            prev_page, prev_data = self.history_stack[-1]
            print(f"返回到: 页面索引={prev_page}, 数据={prev_data}")
            # 根据历史记录加载数据并切换页面 (不再次记录历史)
            if prev_page == self.PAGE_COMP_LIST: self.switch_main_page(self.PAGE_COMP_LIST)
            elif prev_page == self.PAGE_HERO_LIST: self.switch_main_page(self.PAGE_HERO_LIST)
            elif prev_page == self.PAGE_ITEM_LIST: self.switch_main_page(self.PAGE_ITEM_LIST)
            elif prev_page == self.PAGE_HEX_LIST: self.switch_main_page(self.PAGE_HEX_LIST)
            elif prev_page == self.PAGE_COMP_DETAIL: self._navigate_to_detail(self.PAGE_COMP_DETAIL, prev_data, is_going_back=True)
            elif prev_page == self.PAGE_HERO_DETAIL: self._navigate_to_detail(self.PAGE_HERO_DETAIL, prev_data, is_going_back=True)
            elif prev_page == self.PAGE_ITEM_DETAIL: self._navigate_to_detail(self.PAGE_ITEM_DETAIL, prev_data, is_going_back=True)
            else:
                 print(f"错误：未知的历史页面索引 {prev_page}")
                 self.switch_main_page(self.PAGE_COMP_LIST) # 保险返回

        self.update_back_button()


    def update_back_button(self):
        """更新返回按钮的可见性"""
        # --- 修改：收起时也可能需要显示返回按钮 ---
        # self.back_button.setVisible(bool(self.history_stack) and not self.is_minimized)
        # self.back_button.setVisible(bool(self.history_stack)) # 原逻辑只控制显隐
        has_history = bool(self.history_stack)
        self.back_button.setVisible(has_history)
        if has_history:
            # --- 修改：设置加宽宽度 ---
            self.back_button.setFixedWidth(self.WIDE_BACK_BUTTON_WIDTH)
        else:
            # --- 修改：恢复标准宽度 (虽然隐藏了) ---
            self.back_button.setFixedWidth(self.STANDARD_BACK_BUTTON_WIDTH)


    def _navigate_to_detail(self, page_index, data_key, is_going_back=False):
        """导航到详情页的通用逻辑"""
        print(f"导航到详情页: Index={page_index}, Key={data_key}, is_going_back={is_going_back}")
        if page_index not in self.views:
             print(f"错误: 尝试导航到无效的详情页索引 {page_index}")
             return

        view = self.views[page_index]
        load_method = None

        # 根据页面索引获取对应的加载数据方法
        if page_index == self.PAGE_COMP_DETAIL and hasattr(view, 'load_comp_data'):
            load_method = view.load_comp_data
        elif page_index == self.PAGE_HERO_DETAIL and hasattr(view, 'load_hero_data'):
            load_method = view.load_hero_data
        elif page_index == self.PAGE_ITEM_DETAIL and hasattr(view, 'load_item_data'):
            load_method = view.load_item_data

        if load_method:
            try:
                QApplication.setOverrideCursor(Qt.CursorShape.WaitCursor) # 设置等待光标
                load_method(data_key) # 调用视图的加载方法
                
                # --- 新增：在非返回导航时重置详情页状态 ---
                if not is_going_back and hasattr(view, 'reset_to_initial_state'):
                    print(f"重置详情页 {page_index} 到初始状态")
                    view.reset_to_initial_state()
                
                self.stacked_widget.setCurrentIndex(page_index)
                # 更新导航按钮状态（详情页时取消主导航选中）
                # self.switch_main_page(page_index) # 传入详情页索引以取消主导航选中
                
                # --- 修复：直接更新导航按钮状态，避免递归调用 ---
                current_checked = self.nav_button_group.checkedButton()
                if current_checked:
                    # 设置 autoExclusive 为 False 临时允许取消选中，然后再恢复
                    self.nav_button_group.setExclusive(False)
                    current_checked.setChecked(False)
                    self.nav_button_group.setExclusive(True)
                
                if not is_going_back:
                    self.record_navigation(page_index, data_key) # 记录导航历史
            except Exception as e:
                print(f"导航到详情页 {page_index} 时出错: {e}")
                traceback.print_exc()
                # 可以选择显示错误消息或回退
            finally:
                 QApplication.restoreOverrideCursor() # 恢复光标
        else:
            print(f"错误: 视图 {type(view).__name__} 没有找到加载数据的方法。")


    # --- 槽函数，响应视图信号 (show_comp_detail, show_hero_detail, show_item_detail) ---
    # 这些方法保持不变...
    @Slot(str)
    def show_comp_detail(self, comp_name):
        self._navigate_to_detail(self.PAGE_COMP_DETAIL, comp_name)

    @Slot(str)
    def show_hero_detail(self, hero_name):
        self._navigate_to_detail(self.PAGE_HERO_DETAIL, hero_name)

    @Slot(str)
    def show_item_detail(self, item_name):
        self._navigate_to_detail(self.PAGE_ITEM_DETAIL, item_name)


    # --- 窗口控制逻辑 ---
    @Slot()
    def toggle_window_size(self):
        """切换窗口展开/收起状态 (修改版)"""
        if self.is_minimized:
            # 展开
            print("展开窗口")
            # --- 修改：展开时恢复最小高度限制 ---
            self.setMinimumHeight(self.MINIMIZED_HEIGHT) # 允许窗口再次缩小回收起高度
            self.setMaximumHeight(16777215) # 移除最大高度限制 (Qt默认值)
            self.resize(self.width(), WINDOW_DEFAULT_HEIGHT)
            self.stacked_widget.setVisible(True)
            self.nav_bar.setVisible(True)
            self.is_minimized = False
            self.toggle_indicator.set_minimized(False)
            self.update_back_button()
        else:
            # 收起
            print("收起窗口")
            # --- 修改：固定高度为 MINIMIZED_HEIGHT ---
            self.setMinimumHeight(self.MINIMIZED_HEIGHT)
            self.setMaximumHeight(self.MINIMIZED_HEIGHT) # 固定高度
            # self.resize(self.width(), self.MINIMIZED_HEIGHT) # resize 可能不是必须的，固定高度即可
            self.stacked_widget.setVisible(False)
            self.nav_bar.setVisible(False)
            self.is_minimized = True
            self.toggle_indicator.set_minimized(True)
            # self.back_button.setVisible(False) # 返回按钮根据历史决定是否显示

    # --- 窗口拖动与点击切换 (保持不变) ---
    def mousePressEvent(self, event: QMouseEvent):
        self._is_dragging = False
        if event.button() == Qt.MouseButton.LeftButton and self.control_bar.geometry().contains(event.position().toPoint()):
            self._drag_pos = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            self._press_pos = event.position().toPoint()
            event.accept()
        else:
            self._drag_pos = None
            self._press_pos = None
            super().mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        if event.buttons() == Qt.MouseButton.LeftButton and self._drag_pos:
            self._is_dragging = True
            self.move(event.globalPosition().toPoint() - self._drag_pos)
            event.accept()
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        if event.button() == Qt.MouseButton.LeftButton and self._press_pos is not None:
            if not self._is_dragging:
                release_pos = event.position().toPoint()
                if self.control_bar.rect().contains(release_pos):
                    close_button_rect = self.close_button.geometry()
                    back_button_rect = self.back_button.geometry() if self.back_button.isVisible() else QRectF()
                    if not close_button_rect.contains(release_pos) and \
                       not (self.back_button.isVisible() and back_button_rect.contains(release_pos)):
                        self.toggle_window_size()
                        print("控制条点击，触发 toggle_window_size")
            event.accept()
        else:
            super().mouseReleaseEvent(event)
        self._drag_pos = None
        self._press_pos = None
        self._is_dragging = False

    # --- 新增：全局数据预加载逻辑 ---
    def _start_global_preloading(self):
        """启动异步任务以预加载全局数据 (优化版)"""
        print("开始后台预加载全局数据 (优化版)...")

        # 1. 加载核心全局数据 (高优先级)
        sql_hero_info = "SELECT en_name, cn_name, icon_path FROM heroes"
        execute_query_async(sql_hero_info, on_success=self._on_global_hero_info_loaded, query_key="preload_hero_info_map")

        sql_trait_icons = "SELECT name, center_icon_path, base_icon_path FROM traits"
        execute_query_async(sql_trait_icons, on_success=self._on_global_trait_icons_loaded, query_key="preload_trait_icons")

        sql_item_info = "SELECT name, en_name, icon_path FROM items"
        execute_query_async(sql_item_info, on_success=self._on_global_item_info_loaded, query_key="preload_item_info_map")

        # 2. 移除或注释掉低优先级全局数据加载
        # sql_hero_base_stats = "SELECT cn_name, avg_place FROM heroes"
        # execute_query_async(sql_hero_base_stats, on_success=self._on_global_hero_base_stats_loaded, query_key="preload_hero_base_stats")

        # 3. 立即开始异步加载阵容列表数据 (中优先级)
        # 需要确保 self.views 已经初始化
        comp_list_view = self.views.get(self.PAGE_COMP_LIST)
        if comp_list_view and hasattr(comp_list_view, 'load_data'):
             print("启动时异步加载阵容列表数据...")
             comp_list_view.load_data() # 这个方法内部会调用 execute_query_async
             self._views_loaded[self.PAGE_COMP_LIST] = True # 标记为已启动加载
        else:
             print("警告：无法在启动时获取阵容列表视图实例以加载数据。")

    def _on_global_hero_info_loaded(self, results):
        if results:
            for row in results:
                en_name = row.get('en_name')
                if en_name: # 必须有英文名
                    GLOBAL_HERO_INFO_MAP[en_name.lower()] = {'cn_name': row['cn_name'], 'icon_path': row['icon_path']}
            print(f"全局英雄信息预加载完成: {len(GLOBAL_HERO_INFO_MAP)} 条")

    def _on_global_trait_icons_loaded(self, results):
        if results:
            for row in results:
                GLOBAL_TRAIT_ICON_MAP[row['name']] = row.get('center_icon_path') or row.get('base_icon_path')
            print(f"全局羁绊图标预加载完成: {len(GLOBAL_TRAIT_ICON_MAP)} 条")

    def _on_global_item_info_loaded(self, results):
        if results:
            for row in results:
                GLOBAL_ITEM_INFO_MAP[row['name']] = {'en_name': row.get('en_name'), 'icon_path': row.get('icon_path')}
            print(f"全局装备信息预加载完成: {len(GLOBAL_ITEM_INFO_MAP)} 条")

    def _setup_resource_management(self):
        """设置资源管理和定期清理"""
        # 设置定期资源清理定时器 (每3分钟执行一次)
        self._resource_cleanup_timer = QTimer(self)
        self._resource_cleanup_timer.setInterval(RESOURCE_CLEANUP_INTERVAL_MS)  # 默认3分钟
        self._resource_cleanup_timer.timeout.connect(self._clean_resources)
        self._resource_cleanup_timer.start()
        
        print(f"资源管理初始化完成，将每 {RESOURCE_CLEANUP_INTERVAL_MS/1000} 秒执行一次清理")
        
    def _clean_resources(self):
        """执行资源清理操作，释放内存"""
        # 1. 清理Python对象缓存
        print("执行定期资源清理...")
        
        # 2. 触发Python垃圾回收
        collected = gc.collect()
        print(f"垃圾回收完成，释放了 {collected} 个对象")
        
        # 3. 如果需要，执行额外的资源清理逻辑...
        # 如果当前在使用详情页，不执行其他清理
        current_page = self.stacked_widget.currentIndex()
        if current_page in MEMORY_INTENSIVE_VIEWS:
            print(f"当前在内存密集型页面 ({current_page})，跳过额外清理")
            return
            
        # 刷新内存使用量
        import psutil
        memory_info = psutil.Process().memory_info()
        memory_mb = memory_info.rss / (1024 * 1024)
        print(f"当前内存使用量: {memory_mb:.1f} MB")
        
        # 如果内存使用超过某个阈值，执行更深度的清理
        if memory_mb > 500:  # 超过500MB时
            print("内存使用较高，执行深度清理...")
            # 从自定义组件模块导入缓存清理函数
            from 自定义组件 import clear_icon_cache
            from 数据库操作 import clear_query_cache
            
            # 仅在非详情页时执行此操作
            clear_icon_cache()
            clear_query_cache()

    def _schedule_view_unload(self, page_index):
        """为指定页面安排卸载任务"""
        if not ENABLE_VIEW_UNLOADING or page_index not in MEMORY_INTENSIVE_VIEWS:
            return
            
        # 取消之前的卸载定时器（如果存在）
        if page_index in self._view_unload_timers and self._view_unload_timers[page_index] is not None:
            self._view_unload_timers[page_index].stop()
            
        # 创建新的定时器
        unload_timer = QTimer(self)
        unload_timer.setSingleShot(True)
        unload_timer.setInterval(VIEW_UNLOAD_DELAY_MS)  # 默认15秒
        unload_timer.timeout.connect(lambda: self._unload_view_resources(page_index))
        unload_timer.start()
        
        self._view_unload_timers[page_index] = unload_timer
        print(f"已安排页面 {page_index} 的资源在 {VIEW_UNLOAD_DELAY_MS/1000} 秒后卸载")
        
    def _unload_view_resources(self, page_index):
        """卸载指定视图的资源以释放内存"""
        if self.stacked_widget.currentIndex() == page_index:
            # 如果这个页面当前可见，不执行卸载
            print(f"页面 {page_index} 当前可见，取消卸载")
            return
            
        # 获取视图对象
        view = self.views.get(page_index)
        if not view:
            return
            
        print(f"卸载页面 {page_index} 的资源")
        
        # 调用视图的资源释放方法（如果存在）
        if hasattr(view, 'release_resources'):
            view.release_resources()
        elif hasattr(view, 'clear_resources'):
            view.clear_resources()
        else:
            # 如果没有专门的释放方法，尝试清理常见的资源对象
            # 遍历所有IconLabel并清除pixmap
            self._clear_icon_labels_in_widget(view)
            
            # 标记为需要重新加载
            if page_index in [self.PAGE_COMP_LIST, self.PAGE_HERO_LIST, self.PAGE_ITEM_LIST, self.PAGE_HEX_LIST]:
                self._views_loaded[page_index] = False
                
        # 触发垃圾回收
        gc.collect()
            
    def _clear_icon_labels_in_widget(self, widget):
        """递归清理Widget中的所有IconLabel"""
        # 导入IconLabel类以便进行类型检查
        from 自定义组件 import IconLabel
        
        # 递归查找所有子控件
        for child in widget.findChildren(IconLabel):
            if isinstance(child, IconLabel) and hasattr(child, 'clearPixmap'):
                try:
                    child.clearPixmap()
                except Exception as e:
                    print(f"清理IconLabel时出错: {e}")

    def _start_ocr_query(self):
        """启动OCR查询脚本"""
        try:
            # 获取当前主窗口脚本所在的目录
            current_dir = Path(__file__).parent.absolute()
            # 构建到 yimiaojue 目录和 ocr查询.py 脚本的正确路径
            yimiaojue_dir = current_dir / "yimiaojue"
            ocr_script_path = yimiaojue_dir / "ocr查询.py"
            
            # 检查OCR脚本是否存在
            if not ocr_script_path.exists():
                print(f"错误: OCR查询脚本 {ocr_script_path} 未找到！")
                return
                
            print(f"正在启动OCR查询: {ocr_script_path}")
            
            # 使用Popen启动脚本，不等待其完成
            # 关键：将工作目录(cwd)设置为脚本所在的'yimiaojue'目录
            # 这样 ocr查询.py 才能正确找到它自己的相对路径文件（如数据库、配置等）
            self.ocr_process = subprocess.Popen(
                [sys.executable, str(ocr_script_path)],
                cwd=str(yimiaojue_dir),
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            print(f"OCR查询已启动，进程ID: {self.ocr_process.pid}")
        except Exception as e:
            print(f"启动OCR查询失败: {e}")
            traceback.print_exc()
        
    def closeEvent(self, event):
        """当窗口关闭时执行清理操作"""
        print("应用程序即将关闭，执行最终清理...")
        
        # 关闭OCR查询进程
        if self.ocr_process:
            try:
                print(f"正在终止OCR查询进程 (PID: {self.ocr_process.pid})...")
                self.ocr_process.terminate()
                # 等待最多1秒
                self.ocr_process.wait(timeout=1)
            except Exception as e:
                print(f"终止OCR查询进程时出错: {e}")
        
        # 停止所有计时器
        if self._resource_cleanup_timer:
            self._resource_cleanup_timer.stop()
            
        for timer in self._view_unload_timers.values():
            if timer:
                timer.stop()
                
        # 清空全局缓存
        try:
            from 自定义组件 import clear_icon_cache
            from 数据库操作 import clear_query_cache
            clear_icon_cache()
            clear_query_cache()
        except Exception as e:
            print(f"最终清理时发生错误: {e}")
            
        # 释放视图资源
        for view in self.views.values():
            if hasattr(view, 'release_resources'):
                try:
                    view.release_resources()
                except Exception as e:
                    print(f"释放视图资源时出错: {e}")
        
        # 强制垃圾回收
        gc.collect()
        
        # 接受关闭事件
        super().closeEvent(event)


# --- 程序入口 ---
if __name__ == "__main__":
    app = QApplication(sys.argv)
    # --- 修改：应用全局 Tooltip 样式 ---
    app.setStyleSheet(TOOLTIP_STYLE)
    window = MainWindow()
    if DB_FILE.exists():
        window.show()
    else:
        window.show()
    sys.exit(app.exec())