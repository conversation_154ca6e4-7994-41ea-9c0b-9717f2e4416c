import { computed } from 'vue';
import { useDataStore } from '@/stores/data';
import { invoke } from '@tauri-apps/api/core';
import type { QueryResult } from '@/types';
import { startPerf, endPerf } from '@/utils/performanceMonitor';

/**
 * 数据操作组合式函数
 * 提供便捷的数据查询和缓存操作
 */
export function useData() {
  const dataStore = useDataStore();

  // 数据状态
  const isDataReady = computed(() => dataStore.isDataReady);
  const isPreloading = computed(() => dataStore.isPreloading);
  const cacheStats = computed(() => dataStore.cacheStats);

  /**
   * 获取阵容列表
   */
  const getCompList = async () => {
    startPerf('阵容列表加载');

    // 先检查缓存
    const cached = dataStore.getCachedQuery('comp_list');
    if (cached) {
      endPerf('阵容列表加载', { dataSize: cached.length });
      return cached;
    }

    return dataStore.executeWithCache('comp_list', async () => {
      const result: QueryResult = await invoke('get_comp_list');
      if (result.error) {
        throw new Error(result.error);
      }
      endPerf('阵容列表加载', { dataSize: result.data.length });
      return result.data;
    });
  };

  /**
   * 获取英雄列表
   */
  const getHeroList = async () => {
    return dataStore.executeWithCache('hero_list', async () => {
      const result: QueryResult = await invoke('get_hero_list');
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 获取装备列表
   */
  const getItemList = async () => {
    return dataStore.executeWithCache('item_list', async () => {
      const result: QueryResult = await invoke('get_item_list');
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 获取海克斯列表
   */
  const getHexList = async () => {
    return dataStore.executeWithCache('hex_list_fast', async () => {
      const result: QueryResult = await invoke('get_hex_list');
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 获取阵容详情
   * @param compName 阵容名称
   */
  const getCompDetail = async (compName: string) => {
    const cacheKey = `comp_detail_${compName}`;
    return dataStore.executeWithCache(cacheKey, async () => {
      const result: QueryResult = await invoke('get_comp_detail', { compName });
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 获取英雄详情
   * @param heroName 英雄名称
   */
  const getHeroDetail = async (heroName: string) => {
    const cacheKey = `hero_detail_${heroName}`;
    return dataStore.executeWithCache(cacheKey, async () => {
      const result: QueryResult = await invoke('get_hero_detail', { heroName });
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 获取装备详情
   * @param itemName 装备名称
   */
  const getItemDetail = async (itemName: string) => {
    const cacheKey = `item_detail_${itemName}`;
    return dataStore.executeWithCache(cacheKey, async () => {
      const result: QueryResult = await invoke('get_item_detail', { itemName });
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    });
  };

  /**
   * 执行自定义查询
   * @param sql SQL查询语句
   * @param params 查询参数
   * @param cacheKey 可选的缓存键
   */
  const executeQuery = async (sql: string, params: string[] = [], cacheKey?: string) => {
    const queryFn = async () => {
      const result: QueryResult = await invoke('execute_query', { sql, params });
      if (result.error) {
        throw new Error(result.error);
      }
      return result.data;
    };

    if (cacheKey) {
      return dataStore.executeWithCache(cacheKey, queryFn);
    } else {
      return queryFn();
    }
  };

  /**
   * 获取英雄信息（从缓存）
   * @param heroName 英雄名称
   */
  const getHeroInfo = (heroName: string) => {
    return dataStore.getHeroInfo(heroName);
  };

  /**
   * 获取装备信息（从缓存）
   * @param itemName 装备名称
   */
  const getItemInfo = (itemName: string) => {
    return dataStore.getItemInfo(itemName);
  };

  /**
   * 获取羁绊图标（从缓存）
   * @param traitName 羁绊名称
   */
  const getTraitIcon = (traitName: string) => {
    return dataStore.getTraitIcon(traitName);
  };

  /**
   * 清除指定缓存
   * @param cacheKey 缓存键
   */
  const clearCache = (cacheKey?: string) => {
    dataStore.clearQueryCache(cacheKey);
  };

  /**
   * 预加载全局数据
   */
  const preloadData = async () => {
    await dataStore.preloadGlobalData();
  };

  return {
    // 状态
    isDataReady,
    isPreloading,
    cacheStats,
    
    // 列表查询方法
    getCompList,
    getHeroList,
    getItemList,
    getHexList,
    
    // 详情查询方法
    getCompDetail,
    getHeroDetail,
    getItemDetail,
    
    // 通用查询方法
    executeQuery,
    
    // 缓存查询方法
    getHeroInfo,
    getItemInfo,
    getTraitIcon,
    
    // 缓存管理方法
    clearCache,
    preloadData
  };
}