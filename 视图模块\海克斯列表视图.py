# 视图模块/海克斯列表视图.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QLabel, QScrollArea,
                               QGridLayout, QSpacerItem, QSizePolicy, QFrame, QHBoxLayout)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPainter, QColor

from 常量与配置 import (WINDOW_BG_COLOR, TEXT_COLOR_LIGHT, TEXT_COLOR_MEDIUM, TEXT_COLOR_DARK,
                       TIER_COLORS, SCROLL_AREA_STYLE_SIMPLE, BORDER_COLOR)
from 自定义组件 import IconLabel, SearchLineEdit # 引入自定义组件
from 数据库操作 import execute_query_async # 引入异步查询函数

# --- 新增：垂直评级标签 ---
class VerticalTierLabel(QWidget):
    """自定义垂直评级标签"""
    def __init__(self, tier_text, tier_color, parent=None):
        super().__init__(parent)
        self.tier_text = tier_text
        self.tier_color = QColor(tier_color)
        self.text_color = QColor(TEXT_COLOR_DARK) # 评级标签用深色文字
        # 增加固定宽度，移除固定高度，让其垂直扩展
        self.setFixedWidth(45) # 增加宽度到 45
        # self.setFixedHeight(60) # 移除固定高度
        self.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding) # 宽度固定，高度扩展

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景
        painter.setBrush(self.tier_color)
        painter.setPen(Qt.PenStyle.NoPen)
        # 绘制圆角矩形作为背景，覆盖整个控件区域
        painter.drawRoundedRect(self.rect(), 6, 6) # 可以稍微调整圆角半径

        # 绘制文字
        painter.setPen(self.text_color)
        font = QFont()
        font.setBold(True)
        font.setPointSize(14) # 也可以适当增大字体
        painter.setFont(font)

        # 在中心绘制文字
        painter.drawText(self.rect(), Qt.AlignmentFlag.AlignCenter, self.tier_text)

    def minimumSizeHint(self):
        # 提供一个最小尺寸提示，宽度为固定值，高度可以基于字体或其他因素计算，
        # 但由于是 Expanding 策略，实际高度由布局决定，这里给个较小的值即可
        return QSize(45, 50) # 更新宽度提示

class 海克斯列表视图(QWidget):
    """显示海克斯强化列表的视图"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.hex_data = {} # 按评级存储海克斯数据: {'S': [hex1, hex2], 'A': [...]}
        self.filtered_hex_data = {} # 存储筛选后的数据
        self.tier_order = ["S", "A", "B", "C", "D", "DEFAULT"] # 评级显示顺序
        self._is_loading = False # 新增：加载状态标志

        self.init_ui()
        # self.load_data() # 初始化时加载数据 -> 由主窗口触发

    def init_ui(self):
        """初始化用户界面"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 10)
        self.main_layout.setSpacing(8)
        self.setStyleSheet(f"background-color: {WINDOW_BG_COLOR};")

        # --- 搜索框 ---
        self.search_box = SearchLineEdit(placeholder_text="搜索海克斯名称...")
        self.search_box.searchChanged.connect(self.filter_hexes) # 连接搜索信号
        self.main_layout.addWidget(self.search_box)

        # --- 加载提示 QLabel ---
        self.loading_label = QLabel("正在加载海克斯数据...", alignment=Qt.AlignmentFlag.AlignCenter)
        self.loading_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 14px; padding: 30px;")
        self.main_layout.addWidget(self.loading_label)
        self.loading_label.hide() # 初始隐藏

        # --- 海克斯列表滚动区域 (初始隐藏) ---
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet(SCROLL_AREA_STYLE_SIMPLE)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.hide() # 初始隐藏

        # 用于容纳所有评级内容的容器 Widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(15) # 增加评级之间的间距

        self.scroll_area.setWidget(self.content_widget)
        self.main_layout.addWidget(self.scroll_area, 1) # 占据主要空间

    def load_data(self):
        """从数据库异步加载海克斯数据"""
        if self._is_loading or (hasattr(self, 'hex_data') and self.hex_data):
            if self._is_loading:
                print("海克斯数据正在加载中，请稍候...")
            else:
                print("海克斯数据已加载，跳过重复加载。")
                # 确保UI状态正确
                if not self.loading_label.isHidden() or self.scroll_area.isHidden():
                    self.loading_label.hide()
                    self.scroll_area.show()
                    self.filter_hexes(self.search_box.text())
            return

        print("开始异步加载海克斯数据...")
        self._is_loading = True
        self.loading_label.setText("正在加载海克斯数据...")
        self.loading_label.show()
        self.scroll_area.hide()
        # 清理旧的内容
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()

        sql = "SELECT name, tier, icon_path FROM hexes ORDER BY name" # 按名称排序方便查找
        query_key = "all_hexes_list" # 定义缓存键

        execute_query_async(
            sql,
            on_success=self.on_data_loaded,
            on_error=self.on_data_load_error,
            query_key=query_key
        )

    def on_data_loaded(self, results):
        """数据加载成功后的回调函数"""
        print(f"海克斯数据加载成功，共 {len(results)} 条。")
        self.hex_data = {} # 清空旧数据
        self._is_loading = False # 数据加载完成

        if not results:
            self.display_error("未能加载到任何海克斯数据。")
            return

        # 按评级分类数据
        for row in results:
            tier = row.get('tier') if row.get('tier') else "DEFAULT"
            if tier not in self.hex_data:
                self.hex_data[tier] = []
            self.hex_data[tier].append(row) # 存储整个字典

        self.loading_label.hide() # 隐藏加载提示
        self.scroll_area.show()   # 显示内容区域
        # 初始显示所有数据 (现在 filtered_hex_data 会在 filter_hexes 中被正确设置)
        self.filter_hexes(self.search_box.text()) # 使用当前搜索词触发UI更新

    def on_data_load_error(self, error_message):
        """数据加载失败后的回调函数"""
        print(f"海克斯数据加载失败: {error_message}")
        self._is_loading = False # 加载结束（虽然失败了）
        self.display_error(f"加载海克斯数据失败:\n{error_message}")

    def display_error(self, message):
        """在界面上显示错误信息"""
        # 清空 content_widget 内的内容
        while self.content_layout.count() > 0:
            item = self.content_layout.takeAt(0)
            widget = item.widget()
            if widget: widget.deleteLater()

        # 使用 main_layout 中的 loading_label 显示错误
        self.loading_label.setText(message)
        self.loading_label.setWordWrap(True)
        self.loading_label.show()
        self.scroll_area.hide()

    def reset_to_initial_state(self):
        """重置视图到初始状态（用于主导航切换时）"""
        print("重置海克斯列表视图到初始状态")
        
        # 重置搜索框
        if hasattr(self, 'search_box') and self.search_box:
            self.search_box.clear()  # 这会触发搜索信号，自动更新显示
        
        # 重置滚动位置（如果滚动区域可见）
        if hasattr(self, 'scroll_area') and self.scroll_area and self.scroll_area.isVisible():
            self.scroll_area.verticalScrollBar().setValue(0)

    def filter_hexes(self, search_term):
        """根据搜索框内容筛选海克斯"""
        if self._is_loading: # 如果正在加载，则不进行筛选
            print("filter_hexes 调用，但数据仍在加载中，跳过。")
            return

        search_term = search_term.strip().lower()
        self.filtered_hex_data = {}

        if not search_term:
            # 如果搜索词为空，显示所有数据
            self.filtered_hex_data = self.hex_data
        else:
            # 筛选包含搜索词的海克斯
            for tier, hex_list in self.hex_data.items():
                filtered_list = [
                    hex_item for hex_item in hex_list
                    if search_term in hex_item.get('name', '').lower()
                ]
                if filtered_list:
                    self.filtered_hex_data[tier] = filtered_list

        self.update_ui() # 更新界面显示筛选结果

    def update_ui(self):
        """使用 filtered_hex_data 更新界面显示"""
        if self._is_loading: # 如果正在加载，则不更新UI
            print("update_ui 调用，但数据仍在加载中，跳过UI更新。")
            return

        # 清空现有内容 (除了可能的错误标签)
        # 确保加载提示已隐藏，内容区已显示 (除非是错误状态)
        if self.hex_data or not self.filtered_hex_data: # 检查 hex_data 是否有数据
            self.loading_label.hide()
            self.scroll_area.show()
        else: # 如果 hex_data 为空 (可能加载失败或无数据), 保持 loading_label (可能显示错误)
            # display_error 已经处理了 scroll_area.hide() 和 loading_label.show()
            # 所以这里不需要额外操作，除非要确保 loading_label 显示的是最新的错误
            # 如果 display_error 没有被调用，但 hex_data 为空，这可能是一个未处理的初始状态
            if not self.loading_label.text().startswith("加载海克斯数据失败") and not self.loading_label.text().startswith("未能加载"):
                 self.loading_label.setText("无海克斯数据可显示")
                 self.loading_label.show()
                 self.scroll_area.hide()
            return # 不继续更新UI

        # 先获取 content_widget 的当前布局，如果不存在则创建一个
        layout_to_clear = self.content_layout
        if not layout_to_clear:
             layout_to_clear = QVBoxLayout(self.content_widget)
             self.content_layout = layout_to_clear # 保存新布局的引用
             self.content_layout.setContentsMargins(0, 0, 0, 0)
             self.content_layout.setSpacing(0) # 评级区域之间不留空隙

        while layout_to_clear.count():
            item = layout_to_clear.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()
            # 如果 item 是 QSpacerItem，也需要移除
            elif item.spacerItem():
                layout_to_clear.removeItem(item)


        has_results = False
        # 按指定顺序添加评级区域
        for tier in self.tier_order:
            if tier in self.filtered_hex_data and self.filtered_hex_data[tier]:
                self.add_tier_section(tier, self.filtered_hex_data[tier])
                has_results = True

        if not has_results:
            # 如果筛选后没有结果
            no_results_label = QLabel("未找到匹配的海克斯", alignment=Qt.AlignmentFlag.AlignCenter)
            no_results_label.setStyleSheet(f"color: {TEXT_COLOR_MEDIUM}; font-size: 12px; padding: 20px;")
            layout_to_clear.addWidget(no_results_label)

        # 添加一个弹性空间，将内容推到顶部
        layout_to_clear.addSpacerItem(QSpacerItem(20, 0, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding))


    def add_tier_section(self, tier, hex_list):
        """向布局中添加一个评级区域，评级标签在左侧"""
        tier_section_frame = QFrame()
        tier_section_frame.setStyleSheet(f"""
            QFrame {{
                border: 1px solid {BORDER_COLOR};
                border-radius: 6px;
                background-color: {WINDOW_BG_COLOR};
                margin-bottom: 8px;
            }}
        """)
        section_layout = QHBoxLayout(tier_section_frame)
        section_layout.setContentsMargins(0, 0, 0, 0)
        section_layout.setSpacing(0)

        tier_color = TIER_COLORS.get(tier, TIER_COLORS["DEFAULT"])
        tier_label_widget = VerticalTierLabel(tier if tier != "DEFAULT" else " ", tier_color)
        tier_label_widget.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)
        section_layout.addWidget(tier_label_widget, 0)

        grid_container = QWidget()
        grid_container_layout = QVBoxLayout(grid_container)
        grid_container_layout.setContentsMargins(10, 10, 10, 10)
        grid_container_layout.setSpacing(0)

        grid_widget = QWidget()
        grid_layout = QGridLayout(grid_widget)
        grid_layout.setContentsMargins(0, 0, 0, 0)
        grid_layout.setSpacing(10) # 网格间距增大一点

        # --- 修改：每行 8 个，增大图标 --- 
        items_per_row = 7 # 增加每行数量
        icon_size = 58 # 稍微增大图标
        name_font_size = 10.5 # 相应增大名称字体
        name_font_weight = "bold" # 加粗字体

        for index, hex_item in enumerate(hex_list):
            row = index // items_per_row
            col = index % items_per_row

            item_container = QWidget()
            # --- 修改：确保容器背景透明 ---
            item_container.setStyleSheet("background-color: transparent; border: none;")
            item_layout = QVBoxLayout(item_container)
            item_layout.setContentsMargins(0, 0, 0, 0)
            item_layout.setSpacing(3) # 图标和名称间距
            item_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

            hex_name = hex_item.get('name', '未知')
            icon_path = hex_item.get('icon_path')

            icon_label = IconLabel(icon_size=icon_size, icon_type='hex', placeholder_text='?')
            icon_label.set_icon(icon_path, hex_name)
            icon_label.setToolTip(hex_name)

            name_label = QLabel(hex_name)
            name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            # --- 修改：增大字号、加粗、移除边框/背景 ---
            name_label.setStyleSheet(f"color: {TEXT_COLOR_LIGHT}; font-size: {name_font_size}px; font-weight: {name_font_weight}; background-color: transparent; border: none;")
            name_label.setWordWrap(True)
            # --- 修改：根据新的图标大小调整最大宽度 ---
            name_label.setMaximumWidth(icon_size + 15) 

            item_layout.addWidget(icon_label)
            item_layout.addWidget(name_label)

            grid_layout.addWidget(item_container, row, col) # 默认填充对齐

        # --- 修改：确保行和列的伸展设置正确，以填充空间 ---
        grid_layout.setRowStretch(len(hex_list) // items_per_row + 1, 1)
        grid_layout.setColumnStretch(items_per_row, 1) # 在最后一列之后添加伸展

        grid_container_layout.addWidget(grid_widget)
        section_layout.addWidget(grid_container, 1)
        # 更正：应该是 tier_section_frame
        self.content_layout.addWidget(tier_section_frame)