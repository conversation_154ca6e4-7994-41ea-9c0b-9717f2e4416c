# -*- coding: utf-8 -*-
"""
装备框数量检测的独立测试脚本

本脚本用于可视化对比不同的图像预处理策略，以找到最稳定、最准确的装备框轮廓检测方法。
运行此脚本后，会弹出一个UI窗口，展示原图、多种策略处理后的效果图及各自的检测结果。
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import numpy as np

# --- 核心功能函数 ---

def count_contours(image_cv, min_area=2000):
    """在给定的（已预处理的）图像上查找并计数轮廓"""
    try:
        # findContours会修改原图，所以传入一个副本
        contours, _ = cv2.findContours(image_cv.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        valid_contours = 0
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                valid_contours += 1
        return valid_contours, contours
    except Exception as e:
        print(f"轮廓计数时出错: {e}")
        return 0, []

# --- 预处理策略定义 ---

def strategy_binary_threshold_closing(gray_image):
    """策略一：全局二值化 + 形态学闭合（当前使用的方法）"""
    _, thresh = cv2.threshold(gray_image, 100, 255, cv2.THRESH_BINARY)
    kernel = np.ones((5,5), np.uint8)
    closing = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
    return closing

def strategy_adaptive_threshold(gray_image):
    """策略二：自适应阈值"""
    # 使用高斯自适应阈值，对光照不均的情况可能更有效
    adaptive_thresh = cv2.adaptiveThreshold(gray_image, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2)
    return adaptive_thresh

def strategy_canny_edge(gray_image):
    """策略三：Canny边缘检测（之前失败的方法，用于对比）"""
    blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
    edged = cv2.Canny(blurred, 50, 150)
    return edged

def strategy_otsu_threshold(gray_image):
    """策略四：Otsu's二值化（一种自动寻找最优阈值的方法）"""
    _, thresh = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    return thresh
    
def strategy_hsv_color_mask(bgr_image):
    """策略五：HSV颜色空间掩码（尝试提取装备框的金色/棕色）"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    # 定义金色/棕色的HSV范围，这个范围可能需要微调
    lower_gold = np.array([15, 80, 80])
    upper_gold = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    # 对掩码进行形态学操作以清理噪点
    kernel = np.ones((5,5), np.uint8)
    closing = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    return closing

def strategy_hsv_wider_hue(bgr_image):
    """策略六 (HSV变种): 更宽的色相范围"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([10, 80, 80]) # 色相下限放宽
    upper_gold = np.array([40, 255, 255]) # 色相上限放宽
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((5,5), np.uint8)
    closing = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    return closing

def strategy_hsv_tighter_sv(bgr_image):
    """策略七 (HSV变种): 更严格的饱和度/亮度"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([15, 100, 100]) # 提高饱和度和亮度的下限
    upper_gold = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((5,5), np.uint8)
    closing = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    return closing

def strategy_hsv_more_closing(bgr_image):
    """策略八 (HSV变种): 更强的闭合操作"""
    hsv = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2HSV)
    lower_gold = np.array([15, 80, 80])
    upper_gold = np.array([35, 255, 255])
    mask = cv2.inRange(hsv, lower_gold, upper_gold)
    kernel = np.ones((7,7), np.uint8) # 使用更大的内核
    closing = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=3) # 增加迭代次数
    return closing

# --- UI界面类 ---

class App(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("装备框计数策略测试工具")
        self.geometry("1200x900") # 稍微增加窗口高度以容纳更多策略
        
        self.strategies = {
            "策略一: 全局阈值+闭合": strategy_binary_threshold_closing,
            "策略二: 自适应阈值": strategy_adaptive_threshold,
            "策略三: Canny边缘检测": strategy_canny_edge,
            "策略四: Otsu自动阈值": strategy_otsu_threshold,
            "策略五: HSV颜色掩码": strategy_hsv_color_mask,
            "策略六 (HSV变种): 更宽色相": strategy_hsv_wider_hue,
            "策略七 (HSV变种): 更严S/V": strategy_hsv_tighter_sv,
            "策略八 (HSV变种): 更强闭合": strategy_hsv_more_closing,
        }

        # --- UI布局 ---
        # 创建一个主框架，允许滚动
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=1)

        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 文件选择按钮
        self.load_button = ttk.Button(self.scrollable_frame, text="选择图片文件", command=self.load_image)
        self.load_button.pack(pady=10)
        
        self.image_path = None
        self.cv_image = None
        self.results_frame = None


    def load_image(self):
        path = filedialog.askopenfilename()
        if not path:
            return
        self.image_path = path
        
        # [修复] 使用np.fromfile和cv2.imdecode来正确处理包含非ASCII字符（如中文）的文件路径
        try:
            n = np.fromfile(path, np.uint8)
            self.cv_image = cv2.imdecode(n, cv2.IMREAD_COLOR)
        except Exception as e:
            messagebox.showerror("文件读取错误", f"无法读取图片文件：{e}")
            self.cv_image = None

        # 如果图片加载失败，则弹出错误提示并中止
        if self.cv_image is None:
            messagebox.showerror("加载失败", f"无法加载图片，请检查文件是否为有效的图片格式，或路径是否正确。\n路径: {path}")
            return

        self.cv_image_bgr = self.cv_image.copy() # 保留一份BGR格式用于HSV
        self.gray_image = cv2.cvtColor(self.cv_image, cv2.COLOR_BGR2GRAY)
        self.run_tests()


    def run_tests(self):
        # 清理旧的结果
        if self.results_frame:
            self.results_frame.destroy()
        
        self.results_frame = ttk.Frame(self.scrollable_frame)
        self.results_frame.pack(pady=10)

        # 调整原图尺寸以便显示
        height, width, _ = self.cv_image.shape
        display_width = 300
        scale = display_width / width
        display_height = int(height * scale)
        
        # 显示原图
        resized_orig = cv2.resize(self.cv_image, (display_width, display_height))
        img_pil = Image.fromarray(cv2.cvtColor(resized_orig, cv2.COLOR_BGR2RGB))
        img_tk = ImageTk.PhotoImage(img_pil)
        
        orig_frame = ttk.Labelframe(self.results_frame, text="原始图片")
        orig_frame.grid(row=0, column=0, padx=5, pady=5)
        orig_label = tk.Label(orig_frame, image=img_tk)
        orig_label.image = img_tk
        orig_label.pack()

        # 循环测试所有策略
        for i, (name, func) in enumerate(self.strategies.items()):
            
            frame = ttk.Labelframe(self.results_frame, text=f"{name}")
            frame.grid(row=i + 1, column=0, padx=5, pady=5, sticky="ew")

            # 根据策略需要传入不同格式的图片
            if "HSV" in name:
                processed_img = func(self.cv_image_bgr)
            else:
                processed_img = func(self.gray_image.copy())
            
            # 计数
            count, contours = count_contours(processed_img)

            # 绘制轮廓到原图上以便观察
            img_with_contours = self.cv_image.copy()
            cv2.drawContours(img_with_contours, contours, -1, (0, 255, 0), 2)


            # --- 显示UI ---
            # 调整处理后的图像尺寸
            resized_processed = cv2.resize(processed_img, (display_width, display_height))
            # 调整带轮廓的图像尺寸
            resized_contours = cv2.resize(img_with_contours, (display_width, display_height))

            # 转换成Tkinter格式
            processed_pil = Image.fromarray(resized_processed)
            processed_tk = ImageTk.PhotoImage(image=processed_pil)
            
            contours_pil = Image.fromarray(cv2.cvtColor(resized_contours, cv2.COLOR_BGR2RGB))
            contours_tk = ImageTk.PhotoImage(image=contours_pil)

            # 创建并放置控件
            img_label_processed = ttk.Label(frame, image=processed_tk)
            img_label_processed.image = processed_tk
            img_label_processed.grid(row=0, column=0, padx=5, pady=5)

            img_label_contours = ttk.Label(frame, image=contours_tk)
            img_label_contours.image = contours_tk
            img_label_contours.grid(row=0, column=1, padx=5, pady=5)

            result_label = ttk.Label(frame, text=f"检测到 {count} 个装备", font=("Helvetica", 14, "bold"))
            result_label.grid(row=0, column=2, padx=10)

if __name__ == "__main__":
    app = App()
    app.mainloop() 