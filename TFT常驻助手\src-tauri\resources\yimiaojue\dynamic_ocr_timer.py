# -*- coding: utf-8 -*-
"""
动态OCR时序控制系统

本模块实现了基于结果反馈的动态OCR时序控制，替换固定的运行时长。
主要功能：
- 根据触发方式设置不同的初始超时时间
- 检测到有效结果时自动延长运行时间
- 连续无结果时自动停止OCR
"""

import time
import logging
from typing import Optional


class DynamicOCRTimer:
    """
    动态OCR时序控制器
    
    实现基于结果反馈的动态时序控制机制：
    - 手动触发：10秒初始超时
    - 自动触发：5秒初始超时  
    - 有效结果检测：自动延长5秒
    - 窗口消失后延迟5秒结束
    """
    
    def __init__(self, initial_timeout: int = 5, manual_timeout: int = 10, extension_timeout: int = 5, window_disappear_delay: int = 5):
        """
        初始化动态OCR时序控制器
        
        Args:
            initial_timeout (int): 自动触发时的初始超时时间（秒）
            manual_timeout (int): 手动触发时的初始超时时间（秒）
            extension_timeout (int): 检测到有效结果时的延长时间（秒）
            window_disappear_delay (int): 窗口消失后的延迟结束时间（秒）
        """
        self.initial_timeout = initial_timeout
        self.manual_timeout = manual_timeout
        self.extension_timeout = extension_timeout
        self.window_disappear_delay = window_disappear_delay
        
        # 时间跟踪变量
        self.start_time: Optional[float] = None
        self.last_result_time: Optional[float] = None
        self.window_disappear_time: Optional[float] = None
        self.current_timeout: int = 0
        self.is_manual_trigger: bool = False
        
        # 状态标志
        self.is_running: bool = False
        self.has_shown_results: bool = False  # 是否已显示过结果
        self.windows_visible: bool = False    # 当前窗口是否可见
        
        logging.info(f"[DynamicOCRTimer] 初始化完成 - 自动超时: {initial_timeout}s, 手动超时: {manual_timeout}s, 延长时间: {extension_timeout}s, 窗口消失延迟: {window_disappear_delay}s")
    
    def start_timer(self, manual: bool = False) -> None:
        """
        启动计时器
        
        Args:
            manual (bool): True表示手动触发，False表示自动触发
        """
        current_time = time.time()
        self.start_time = current_time
        self.last_result_time = None
        self.window_disappear_time = None
        self.is_manual_trigger = manual
        self.is_running = True
        self.has_shown_results = False
        self.windows_visible = False
        
        # 根据触发方式设置超时时间
        self.current_timeout = self.manual_timeout if manual else self.initial_timeout
        
        trigger_type = "手动" if manual else "自动"
        logging.info(f"[DynamicOCRTimer] 启动计时器 - 触发方式: {trigger_type}, 超时时间: {self.current_timeout}s")
    
    def extend_timer(self) -> None:
        """
        检测到有效结果时延长计时器
        
        当OCR产生有效结果时调用此方法，会重置延长计时器
        """
        if not self.is_running:
            return
            
        current_time = time.time()
        self.last_result_time = current_time
        
        # 如果是首次检测到结果，记录首次结果时间
        if not self.has_shown_results:
            elapsed = current_time - self.start_time
            logging.info(f"[DynamicOCRTimer] 首次检测到有效结果，耗时: {elapsed:.2f}s")
            self.has_shown_results = True
            # 首次检测到结果时才记录延长计时器信息
            logging.info(f"[DynamicOCRTimer] 检测到有效结果，延长计时器 {self.extension_timeout}s")
        else:
            # 后续的延长操作只在调试模式下记录
            logging.debug(f"[DynamicOCRTimer] 检测到有效结果，延长计时器 {self.extension_timeout}s")
    
    def set_windows_visible(self, visible: bool) -> None:
        """
        设置窗口可见状态
        
        Args:
            visible (bool): True表示窗口可见，False表示窗口不可见
        """
        if not self.is_running:
            return
            
        current_time = time.time()
        
        if self.windows_visible and not visible:
            # 窗口从可见变为不可见
            self.window_disappear_time = current_time
            logging.info(f"[DynamicOCRTimer] 检测到窗口消失，开始{self.window_disappear_delay}s倒计时")
        elif not self.windows_visible and visible:
            # 窗口从不可见变为可见
            self.window_disappear_time = None
            logging.info(f"[DynamicOCRTimer] 检测到窗口显示，取消消失倒计时")
        
        self.windows_visible = visible
    
    def should_continue(self) -> bool:
        """
        检查是否应该继续OCR
        
        Returns:
            bool: True表示应该继续，False表示应该停止
        """
        if not self.is_running:
            return False
            
        current_time = time.time()
        
        # 如果从未检测到结果，使用初始超时时间判断
        if not self.has_shown_results:
            elapsed = current_time - self.start_time
            if elapsed >= self.current_timeout:
                trigger_type = "手动" if self.is_manual_trigger else "自动"
                logging.info(f"[DynamicOCRTimer] {trigger_type}触发超时 ({self.current_timeout}s)，无任何有效结果，停止OCR")
                self.stop_timer()
                return False
            return True
        
        # 如果已显示过结果，检查窗口状态
        if self.has_shown_results:
            # 如果窗口仍然可见，继续运行
            if self.windows_visible:
                return True
            
            # 如果窗口不可见，检查消失后的延迟时间
            if self.window_disappear_time is not None:
                time_since_disappear = current_time - self.window_disappear_time
                if time_since_disappear >= self.window_disappear_delay:
                    total_elapsed = current_time - self.start_time
                    logging.info(f"[DynamicOCRTimer] 窗口消失{self.window_disappear_delay}s后停止，总运行时间: {total_elapsed:.2f}s")
                    self.stop_timer()
                    return False
                return True
            
            # 如果窗口不可见但没有记录消失时间，使用原有的延长逻辑
            if self.last_result_time is not None:
                time_since_last_result = current_time - self.last_result_time
                if time_since_last_result >= self.extension_timeout:
                    total_elapsed = current_time - self.start_time
                    logging.info(f"[DynamicOCRTimer] 连续{self.extension_timeout}s无有效结果，总运行时间: {total_elapsed:.2f}s，停止OCR")
                    self.stop_timer()
                    return False
        
        return True
    
    def stop_timer(self) -> None:
        """
        停止计时器并记录统计信息
        """
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.start_time:
            total_runtime = time.time() - self.start_time
            trigger_type = "手动" if self.is_manual_trigger else "自动"
            
            if self.last_result_time:
                time_to_first_result = self.last_result_time - self.start_time
                logging.info(f"[DynamicOCRTimer] 停止计时器 - 触发方式: {trigger_type}, "
                           f"总运行时间: {total_runtime:.2f}s, 首次结果耗时: {time_to_first_result:.2f}s")
            else:
                logging.info(f"[DynamicOCRTimer] 停止计时器 - 触发方式: {trigger_type}, "
                           f"总运行时间: {total_runtime:.2f}s, 无有效结果")
    
    def get_status(self) -> dict:
        """
        获取当前计时器状态信息
        
        Returns:
            dict: 包含计时器状态的字典
        """
        if not self.is_running or not self.start_time:
            return {
                'is_running': False,
                'elapsed_time': 0,
                'time_since_last_result': 0,
                'trigger_type': None,
                'timeout_remaining': 0
            }
        
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        
        if self.last_result_time:
            time_since_last_result = current_time - self.last_result_time
            timeout_remaining = max(0, self.extension_timeout - time_since_last_result)
        else:
            time_since_last_result = elapsed_time
            timeout_remaining = max(0, self.current_timeout - elapsed_time)
        
        return {
            'is_running': self.is_running,
            'elapsed_time': elapsed_time,
            'time_since_last_result': time_since_last_result,
            'trigger_type': '手动' if self.is_manual_trigger else '自动',
            'timeout_remaining': timeout_remaining,
            'has_results': self.last_result_time is not None
        }
    
    def reset(self) -> None:
        """
        重置计时器到初始状态
        """
        self.start_time = None
        self.last_result_time = None
        self.current_timeout = 0
        self.is_manual_trigger = False
        self.is_running = False
        
        logging.info("[DynamicOCRTimer] 计时器已重置")