import requests
import json
import os

# --- 1. 定义我们系统的入口地址和目标文件 ---

# 这是我们部署在ECS服务器上的“公告板”地址，软件客户端唯一需要硬编码的地址
config_server_url = 'http://updater.yuxianglei.com/yimiaojue_config.json'

# 这是我们之前上传到七牛云的测试文件的 Key (在云端的名称)
target_file_key = 'test/upload_test.txt'

# 定义下载后保存在本地的文件名
local_filename = 'downloaded_test_file.txt'

# 预期的文件内容，用于最终验证
expected_content = '你好，七牛云！这是一个来自Python脚本的上传测试。'


# --- 2. 脚本核心逻辑 ---

print("--- 开始测试软件更新下载流程 ---")

try:
    # --- 步骤一：从ECS服务器获取主配置文件 ---
    print(f"\n[步骤 1/4] 正在从公告板获取配置: {config_server_url}")
    response_config = requests.get(config_server_url, timeout=10) # 10秒超时
    
    # 检查请求是否成功 (状态码 200)
    response_config.raise_for_status() 
    
    config_data = response_config.json()
    print("  > 成功获取配置!")

    # --- 步骤二：解析配置文件，获取真实的下载基地址 ---
    print("\n[步骤 2/4] 正在解析配置，寻找下载仓库地址...")
    # 我们以正式版(release)为例
    download_base_url = config_data.get('release', {}).get('download_base_url')
    
    if not download_base_url:
        raise ValueError("配置文件中未找到 'download_base_url' 字段")
    
    # 确保URL以 https:// 开头
    if not download_base_url.startswith('https://'):
        download_base_url = 'https://' + download_base_url

    print(f"  > 成功找到仓库地址: {download_base_url}")
    
    # --- 步骤三：拼接最终下载链接并下载文件 ---
    final_download_url = f"{download_base_url.rstrip('/')}/{target_file_key}"
    print(f"\n[步骤 3/4] 正在从最终地址下载文件: {final_download_url}")
    
    response_file = requests.get(final_download_url, timeout=30) # 30秒下载超时
    response_file.raise_for_status()

    # 将下载内容写入本地文件
    with open(local_filename, 'wb') as f:
        f.write(response_file.content)
    print(f"  > 文件下载成功，已保存为: {local_filename}")
    
    # --- 步骤四：验证下载的文件内容是否正确 ---
    print(f"\n[步骤 4/4] 正在验证文件内容...")
    with open(local_filename, 'r', encoding='utf-8') as f:
        downloaded_content = f.read()
    
    if downloaded_content == expected_content:
        print("  > 文件内容验证通过！与上传的内容完全一致。")
        print("\n--- ✅ 恭喜！整个更新下载链路已完全打通！ ---")
    else:
        print("\n--- ❌ 验证失败！下载的文件内容与预期不符。---")
        print(f"  > 预期内容: {expected_content}")
        print(f"  > 实际内容: {downloaded_content}")

except requests.exceptions.RequestException as e:
    print(f"\n--- ❌ 网络请求失败 ---")
    print(f"错误详情: {e}")
except (ValueError, KeyError) as e:
    print(f"\n--- ❌ 配置文件解析失败 ---")
    print(f"错误详情: {e}")
except Exception as e:
    print(f"\n--- ❌ 发生未知错误 ---")
    print(f"错误详情: {e}")

finally:
    # 清理下载的测试文件
    if os.path.exists(local_filename):
        os.remove(local_filename)
        print(f"\n已清理本地下载的测试文件: {local_filename}")